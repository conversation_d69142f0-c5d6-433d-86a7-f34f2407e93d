{"name": "AHA Workflow enable SAAS and Rebilling", "nodes": [{"parameters": {"httpMethod": "POST", "path": "aha-signup", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [0, 0], "id": "9421b271-e8ce-47c2-8bbe-bc3b988c719c", "name": "Form Submission Webhook", "webhookId": "cf043875-6edf-49d7-9eef-48a0ce9ce9dc"}, {"parameters": {"method": "POST", "url": "https://services.leadconnectorhq.com/locations/", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer pit-ef4e9f36-a6db-480e-a455-e4f305a56735"}, {"name": "Version", "value": "2021-07-28"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"name\": \"{{ $json.body.company_name }}\",\n  \"phone\": \"{{ $json.body.phone }}\",\n  \"companyId\": \"gPkTndcx94O3r573TOMx\",\n  \"address\": \"{{ $json.body.location.address }}\",\n  \"city\": \"{{ $json.body.location.city }}\",\n  \"state\": \"{{ $json.body.location.state }}\",\n  \"country\": \"{{ $json.body.location.country }}\",\n  \"postalCode\": \"{{ $json.body.location.postalCode }}\",\n  \"website\": \"\",\n  \"timezone\": \"US/Central\",\n  \"email\": \"{{ $json.body.email }}\",\n  \"prospectInfo\": {\n    \"firstName\": \"{{ $json.body.first_name }}\",\n    \"lastName\": \"{{ $json.body.last_name }}\",\n    \"email\": \"{{ $json.body.email }}\"\n  },\n  \"settings\": {\n    \"allowDuplicateContact\": false,\n    \"allowDuplicateOpportunity\": false,\n    \"allowFacebookNameMerge\": false,\n    \"disableContactTimezone\": false\n  },\n  \"social\": {\n    \"facebookUrl\": \"\",\n    \"googlePlus\": \"\",\n    \"linkedIn\": \"\",\n    \"foursquare\": \"\",\n    \"twitter\": \"\",\n    \"yelp\": \"\",\n    \"instagram\": \"\",\n    \"youtube\": \"\",\n    \"pinterest\": \"\",\n    \"blogRss\": \"\",\n    \"googlePlacesId\": \"\"\n  }\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, 0], "id": "827b9e5a-6ef1-4612-996c-83572686cf9a", "name": "Create Sub-Account"}, {"parameters": {"method": "POST", "url": "https://services.leadconnectorhq.com/users/", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer pit-ef4e9f36-a6db-480e-a455-e4f305a56735"}, {"name": "Version", "value": "2021-07-28"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"companyId\": \"gPkTndcx94O3r573TOMx\",\n  \"firstName\": \"{{ $node['Form Submission Webhook'].json.body.first_name }}\",\n  \"lastName\": \"{{ $node['Form Submission Webhook'].json.body.last_name }}\",\n  \"email\": \"{{ $node['Form Submission Webhook'].json.body.email }}\",\n  \"password\": \"AhaDefualt*2025\",\n  \"phone\": \"{{ $node['Form Submission Webhook'].json.body.phone }}\",\n  \"type\": \"account\",\n  \"role\": \"admin\",\n  \"locationIds\": [\"{{ $node['Create Sub-Account'].json.id }}\"],\n  \"permissions\": {\n    \"campaignsEnabled\": true,\n    \"campaignsReadOnly\": false,\n    \"contactsEnabled\": true,\n    \"workflowsEnabled\": true,\n    \"workflowsReadOnly\": false,\n    \"triggersEnabled\": true,\n    \"funnelsEnabled\": true,\n    \"websitesEnabled\": true,\n    \"opportunitiesEnabled\": true,\n    \"dashboardStatsEnabled\": true,\n    \"bulkRequestsEnabled\": true,\n    \"appointmentsEnabled\": true,\n    \"reviewsEnabled\": true,\n    \"onlineListingsEnabled\": true,\n    \"phoneCallEnabled\": true,\n    \"conversationsEnabled\": true,\n    \"assignedDataOnly\": false,\n    \"settingsEnabled\": true,\n    \"tagsEnabled\": true,\n    \"leadValueEnabled\": true,\n    \"marketingEnabled\": true,\n    \"agentReportingEnabled\": true,\n    \"socialPlanner\": true,\n    \"bloggingEnabled\": true,\n    \"invoiceEnabled\": true,\n    \"contentAiEnabled\": true,\n    \"paymentsEnabled\": true,\n    \"communitiesEnabled\": true\n  }\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [440, 0], "id": "4d383abb-20cc-41ba-88e3-512bb6d8c15b", "name": "Create User"}, {"parameters": {"method": "POST", "url": "=https://services.leadconnectorhq.com/saas-api/public-api/enable-saas/{{ $node[\"Create Sub-Account\"].json.id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer pit-ef4e9f36-a6db-480e-a455-e4f305a56735"}, {"name": "Version", "value": "2021-04-15"}, {"name": "Content-Type", "value": "application/json"}, {"name": "channel", "value": "OAUTH"}, {"name": "source", "value": "INTEGRATION"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"stripeAccountId\": \"acct_1OZI7UL5UMPPQRhs\",\n  \"name\": \"{{ $node['Form Submission Webhook'].json.body.first_name }} {{ $node['Form Submission Webhook'].json.body.last_name }}\",\n  \"email\": \"{{ $node['Form Submission Webhook'].json.body.email }}\",\n  \"stripeCustomerId\": \"{{ $node['Stripe'].json.id }}\",\n  \"companyId\": \"gPkTndcx94O3r573TOMx\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [580, 260], "id": "d7e71ff6-311a-4965-af68-85e571f6b5fc", "name": "Enable <PERSON> for Sub-Account"}, {"parameters": {"method": "POST", "url": "=https://services.leadconnectorhq.com/saas-api/public-api/update-rebilling/{{ $node['Form Submission Webhook'].json.body.companyId || 'gPkTndcx94O3r573TOMx' }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer pit-ef4e9f36-a6db-480e-a455-e4f305a56735"}, {"name": "Version", "value": "2021-04-15"}, {"name": "Content-Type", "value": "application/json"}, {"name": "channel", "value": "OAUTH"}, {"name": "source", "value": "INTEGRATION"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"product\": \"contentAI\",\n  \"locationIds\": [\"{{ $node['Create Sub-Account'].json.id }}\"],\n  \"config\": {}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [800, 260], "id": "e248da7c-60a9-448c-acf2-753753d33200", "name": "Enable ContentAI"}, {"parameters": {"method": "POST", "url": "=https://services.leadconnectorhq.com/saas-api/public-api/update-rebilling/{{ $node['Form Submission Webhook'].json.body.companyId || 'gPkTndcx94O3r573TOMx' }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer pit-ef4e9f36-a6db-480e-a455-e4f305a56735"}, {"name": "Version", "value": "2021-04-15"}, {"name": "Content-Type", "value": "application/json"}, {"name": "channel", "value": "OAUTH"}, {"name": "source", "value": "INTEGRATION"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"product\": \"workflow_premium_actions\",\n  \"locationIds\": [\"{{ $node['Create Sub-Account'].json.id }}\"],\n  \"config\": {}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1020, 260], "id": "9037021b-6ac6-47aa-bac0-02c6d3b80882", "name": "Enable Workflow Premium"}, {"parameters": {"method": "POST", "url": "=https://services.leadconnectorhq.com/saas-api/public-api/update-rebilling/{{ $node['Form Submission Webhook'].json.body.companyId || 'gPkTndcx94O3r573TOMx' }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer pit-ef4e9f36-a6db-480e-a455-e4f305a56735"}, {"name": "Version", "value": "2021-04-15"}, {"name": "Content-Type", "value": "application/json"}, {"name": "channel", "value": "OAUTH"}, {"name": "source", "value": "INTEGRATION"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"product\": \"conversationAI\",\n  \"locationIds\": [\"{{ $node['Create Sub-Account'].json.id }}\"],\n  \"config\": {}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1240, 260], "id": "7184d076-2991-43b9-a77e-b2907ace7577", "name": "Enable ConversationAI"}, {"parameters": {"method": "POST", "url": "=https://services.leadconnectorhq.com/saas-api/public-api/update-rebilling/{{ $node['Form Submission Webhook'].json.body.companyId || 'gPkTndcx94O3r573TOMx' }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer pit-ef4e9f36-a6db-480e-a455-e4f305a56735"}, {"name": "Version", "value": "2021-04-15"}, {"name": "Content-Type", "value": "application/json"}, {"name": "channel", "value": "OAUTH"}, {"name": "source", "value": "INTEGRATION"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"product\": \"workflow_ai\",\n  \"locationIds\": [\"{{ $node['Create Sub-Account'].json.id }}\"],\n  \"config\": {}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1460, 260], "id": "670cc87c-6cdd-4883-9910-fe17bf17d6ca", "name": "Enable Workflow AI"}, {"parameters": {"method": "POST", "url": "=https://services.leadconnectorhq.com/saas-api/public-api/update-rebilling/{{ $node['Form Submission Webhook'].json.body.companyId || 'gPkTndcx94O3r573TOMx' }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer pit-ef4e9f36-a6db-480e-a455-e4f305a56735"}, {"name": "Version", "value": "2021-04-15"}, {"name": "Content-Type", "value": "application/json"}, {"name": "channel", "value": "OAUTH"}, {"name": "source", "value": "INTEGRATION"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"product\": \"whatsApp\",\n  \"locationIds\": [\"{{ $node['Create Sub-Account'].json.id }}\"],\n  \"config\": {}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1680, 260], "id": "873e9a07-9720-4b9f-9731-c8309c4af4ba", "name": "Enable WhatsApp"}, {"parameters": {"method": "POST", "url": "=https://services.leadconnectorhq.com/saas-api/public-api/update-rebilling/{{ $node['Form Submission Webhook'].json.body.companyId || 'gPkTndcx94O3r573TOMx' }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer pit-ef4e9f36-a6db-480e-a455-e4f305a56735"}, {"name": "Version", "value": "2021-04-15"}, {"name": "Content-Type", "value": "application/json"}, {"name": "channel", "value": "OAUTH"}, {"name": "source", "value": "INTEGRATION"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"product\": \"reviewsAI\",\n  \"locationIds\": [\"{{ $node['Create Sub-Account'].json.id }}\"],\n  \"config\": {}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1900, 260], "id": "fa857d88-7eed-4292-a4b0-98067c6d85bd", "name": "Enable ReviewsAI"}, {"parameters": {"method": "POST", "url": "=https://services.leadconnectorhq.com/saas-api/public-api/update-rebilling/{{ $node['Form Submission Webhook'].json.body.companyId || 'gPkTndcx94O3r573TOMx' }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer pit-ef4e9f36-a6db-480e-a455-e4f305a56735"}, {"name": "Version", "value": "2021-04-15"}, {"name": "Content-Type", "value": "application/json"}, {"name": "channel", "value": "OAUTH"}, {"name": "source", "value": "INTEGRATION"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"product\": \"Phone\",\n  \"locationIds\": [\"{{ $node['Create Sub-Account'].json.id }}\"],\n  \"config\": {}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2120, 260], "id": "605e12cc-5a8f-4a18-9236-df9226e457cf", "name": "Enable Phone"}, {"parameters": {"method": "POST", "url": "=https://services.leadconnectorhq.com/saas-api/public-api/update-rebilling/{{ $node['Form Submission Webhook'].json.body.companyId || 'gPkTndcx94O3r573TOMx' }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer pit-ef4e9f36-a6db-480e-a455-e4f305a56735"}, {"name": "Version", "value": "2021-04-15"}, {"name": "Content-Type", "value": "application/json"}, {"name": "channel", "value": "OAUTH"}, {"name": "source", "value": "INTEGRATION"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"product\": \"Email\",\n  \"locationIds\": [\"{{ $node['Create Sub-Account'].json.id }}\"],\n  \"config\": {}\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2340, 260], "id": "3143a994-159b-4193-a9e7-a2b1d8e79f9f", "name": "Enable Email"}, {"parameters": {"resource": "customer", "operation": "create", "name": "={{ $node['Form Submission Webhook'].json.body.first_name }} {{ $node['Form Submission Webhook'].json.body.last_name }}", "additionalFields": {"description": "=Customer for {{ $node['Form Submission Webhook'].json.body.company_name }} - Free Tier", "phone": "={{ $node['Form Submission Webhook'].json.body.phone }}"}}, "type": "n8n-nodes-base.stripe", "typeVersion": 1, "position": [320, 280], "id": "b68fdf7a-2698-4ae2-b31f-c4df8a99f416", "name": "Stripe", "credentials": {"stripeApi": {"id": "NpPrNT3dDUFiMF95", "name": "Stripe account"}}}], "pinData": {}, "connections": {"Form Submission Webhook": {"main": [[{"node": "Create Sub-Account", "type": "main", "index": 0}]]}, "Create Sub-Account": {"main": [[{"node": "Create User", "type": "main", "index": 0}]]}, "Create User": {"main": [[{"node": "Stripe", "type": "main", "index": 0}]]}, "Stripe": {"main": [[{"node": "Enable <PERSON> for Sub-Account", "type": "main", "index": 0}]]}, "Enable SaaS for Sub-Account": {"main": [[{"node": "Enable ContentAI", "type": "main", "index": 0}]]}, "Enable ContentAI": {"main": [[{"node": "Enable Workflow Premium", "type": "main", "index": 0}]]}, "Enable Workflow Premium": {"main": [[{"node": "Enable ConversationAI", "type": "main", "index": 0}]]}, "Enable ConversationAI": {"main": [[{"node": "Enable Workflow AI", "type": "main", "index": 0}]]}, "Enable Workflow AI": {"main": [[{"node": "Enable WhatsApp", "type": "main", "index": 0}]]}, "Enable WhatsApp": {"main": [[{"node": "Enable ReviewsAI", "type": "main", "index": 0}]]}, "Enable ReviewsAI": {"main": [[{"node": "Enable Phone", "type": "main", "index": 0}]]}, "Enable Phone": {"main": [[{"node": "Enable Email", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "77025c91-13ef-4b1c-9f07-9a8506be77e8", "meta": {"instanceId": "03505ba83e05b3ce8593cf92f0a29982ad1f685832a0ec4f33c18027aeb8e28e"}, "id": "BLezM0ywSu1S22TX", "tags": [{"createdAt": "2025-06-18T10:59:04.487Z", "updatedAt": "2025-06-18T10:59:04.487Z", "id": "a0jOmX8bDJyybdni", "name": "Live"}]}