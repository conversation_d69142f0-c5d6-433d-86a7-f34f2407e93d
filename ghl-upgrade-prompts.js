/**
 * GHL Custom JavaScript for Upgrade Prompts
 * This script should be added to your GHL Custom JavaScript settings
 * It monitors user actions and shows upgrade prompts when limits are reached
 */

(function() {
    'use strict';
    
    // Configuration - Update these based on your plan limits
    const PLAN_LIMITS = {
        free: {
            pipelines: 3,
            contacts: 100,
            emails: 100,
            funnels: 1,
            users: 1
        },
        basic: {
            pipelines: 15,
            contacts: 1000,
            emails: 1000,
            funnels: -1, // unlimited
            users: 3
        },
        agency: {
            pipelines: 50,
            contacts: 10000,
            emails: 7500,
            funnels: -1,
            users: 10
        },
        enterprise: {
            pipelines: -1, // unlimited
            contacts: -1,
            emails: -1,
            funnels: -1,
            users: -1
        }
    };

    // Get user's current plan from your API or local storage
    let currentPlan = 'free'; // Default to free
    let currentUsage = {
        pipelines: 0,
        contacts: 0,
        emails: 0,
        funnels: 0,
        users: 0
    };

    // Upgrade prompt modal HTML
    const upgradeModalHTML = `
        <div id="aha-upgrade-modal" style="
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            backdrop-filter: blur(5px);
        ">
            <div style="
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
                border: 1px solid #ff4444;
                border-radius: 12px;
                padding: 30px;
                max-width: 500px;
                width: 90%;
                box-shadow: 0 20px 40px rgba(255, 68, 68, 0.3);
            ">
                <div style="text-align: center;">
                    <div style="
                        width: 60px;
                        height: 60px;
                        background: linear-gradient(135deg, #ff4444, #cc3333);
                        border-radius: 50%;
                        margin: 0 auto 20px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 24px;
                        color: white;
                    ">⚡</div>
                    
                    <h2 style="
                        color: white;
                        margin: 0 0 10px;
                        font-size: 24px;
                        font-weight: bold;
                    ">Upgrade Required</h2>
                    
                    <p id="upgrade-message" style="
                        color: #ccc;
                        margin: 0 0 25px;
                        line-height: 1.5;
                    "></p>
                    
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button id="upgrade-now-btn" style="
                            background: linear-gradient(135deg, #ff4444, #cc3333);
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 6px;
                            font-weight: bold;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        ">Upgrade Now</button>
                        
                        <button id="close-upgrade-modal" style="
                            background: transparent;
                            color: #ccc;
                            border: 1px solid #555;
                            padding: 12px 24px;
                            border-radius: 6px;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        ">Maybe Later</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Initialize the upgrade system
    function initUpgradeSystem() {
        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', upgradeModalHTML);
        
        // Add event listeners
        document.getElementById('close-upgrade-modal').addEventListener('click', closeUpgradeModal);
        document.getElementById('upgrade-now-btn').addEventListener('click', redirectToUpgrade);
        
        // Monitor for limit-triggering actions
        monitorUserActions();
        
        // Fetch current usage from your API
        fetchCurrentUsage();
    }

    // Show upgrade modal with specific message
    function showUpgradeModal(limitType, currentCount, limit) {
        const modal = document.getElementById('aha-upgrade-modal');
        const message = document.getElementById('upgrade-message');
        
        const messages = {
            pipelines: `You've reached your limit of ${limit} pipelines. Upgrade to create more and scale your business!`,
            contacts: `You've reached your limit of ${limit} contacts. Upgrade to manage more leads and grow your business!`,
            emails: `You've reached your monthly limit of ${limit} emails. Upgrade to send more and reach more customers!`,
            funnels: `You've reached your limit of ${limit} sales funnel. Upgrade to create unlimited funnels!`,
            users: `You've reached your limit of ${limit} team members. Upgrade to add more users to your account!`
        };
        
        message.textContent = messages[limitType] || 'You\'ve reached a plan limit. Upgrade to unlock more features!';
        modal.style.display = 'block';
        
        // Track the upgrade prompt event
        trackUpgradePrompt(limitType, currentCount, limit);
    }

    // Close upgrade modal
    function closeUpgradeModal() {
        document.getElementById('aha-upgrade-modal').style.display = 'none';
    }

    // Redirect to upgrade page
    function redirectToUpgrade() {
        window.open('https://www.aha-innovations.com/pricing', '_blank');
        closeUpgradeModal();
    }

    // Check if user has reached a limit
    function checkLimits(actionType, currentCount) {
        const limits = PLAN_LIMITS[currentPlan];
        const limit = limits[actionType];
        
        // -1 means unlimited
        if (limit === -1) return false;
        
        if (currentCount >= limit) {
            showUpgradeModal(actionType, currentCount, limit);
            return true;
        }
        
        return false;
    }

    // Monitor user actions that might trigger limits
    function monitorUserActions() {
        // Monitor pipeline creation
        const pipelineButtons = document.querySelectorAll('[data-testid*="pipeline"], .pipeline-create, .add-pipeline');
        pipelineButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                if (checkLimits('pipelines', currentUsage.pipelines + 1)) {
                    event.preventDefault();
                    event.stopPropagation();
                }
            });
        });

        // Monitor contact creation
        const contactButtons = document.querySelectorAll('[data-testid*="contact"], .contact-create, .add-contact');
        contactButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                if (checkLimits('contacts', currentUsage.contacts + 1)) {
                    event.preventDefault();
                    event.stopPropagation();
                }
            });
        });

        // Monitor funnel creation
        const funnelButtons = document.querySelectorAll('[data-testid*="funnel"], .funnel-create, .add-funnel');
        funnelButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                if (checkLimits('funnels', currentUsage.funnels + 1)) {
                    event.preventDefault();
                    event.stopPropagation();
                }
            });
        });

        // Use MutationObserver to catch dynamically added buttons
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // Element node
                        // Re-attach listeners to new buttons
                        const newButtons = node.querySelectorAll('[data-testid*="pipeline"], [data-testid*="contact"], [data-testid*="funnel"]');
                        newButtons.forEach(btn => {
                            // Add appropriate listeners based on button type
                            if (btn.getAttribute('data-testid').includes('pipeline')) {
                                btn.addEventListener('click', (e) => {
                                    if (checkLimits('pipelines', currentUsage.pipelines + 1)) {
                                        e.preventDefault();
                                        e.stopPropagation();
                                    }
                                });
                            }
                            // Add similar logic for contacts and funnels
                        });
                    }
                });
            });
        });

        observer.observe(document.body, { childList: true, subtree: true });
    }

    // Fetch current usage from your API
    async function fetchCurrentUsage() {
        try {
            // Replace with your actual API endpoint
            const response = await fetch('https://your-api.supabase.co/functions/v1/get-user-usage', {
                headers: {
                    'Authorization': 'Bearer ' + getAuthToken(),
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                currentUsage = data.usage;
                currentPlan = data.plan;
            }
        } catch (error) {
            console.log('Could not fetch usage data:', error);
        }
    }

    // Get auth token (implement based on your auth system)
    function getAuthToken() {
        // This should return the user's auth token
        // You might store this in localStorage or get it from your auth system
        return localStorage.getItem('supabase.auth.token') || '';
    }

    // Track upgrade prompt events
    function trackUpgradePrompt(limitType, currentCount, limit) {
        // Send analytics event to track upgrade prompt effectiveness
        try {
            fetch('https://your-api.supabase.co/functions/v1/track-upgrade-prompt', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + getAuthToken()
                },
                body: JSON.stringify({
                    event: 'upgrade_prompt_shown',
                    limit_type: limitType,
                    current_count: currentCount,
                    limit: limit,
                    plan: currentPlan,
                    timestamp: new Date().toISOString()
                })
            });
        } catch (error) {
            console.log('Analytics tracking failed:', error);
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initUpgradeSystem);
    } else {
        initUpgradeSystem();
    }

})();
