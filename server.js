import express from 'express';
import cors from 'cors';
import fetch from 'node-fetch';

const app = express();
const port = 3000;

// Enable CORS for all routes
app.use(cors());
app.use(express.json());

// GHL API constants
const GHL_API_URL = 'https://services.leadconnectorhq.com';
const GHL_TOKEN = 'pit-a329e4c6-962c-4d43-9c41-01eb6933dbea';

// Proxy endpoint to create a GHL location
app.post('/api/create-ghl-location', async (req, res) => {
  try {
    const { email, firstName, lastName, companyName } = req.body;

    // Validate required fields
    if (!email || !firstName || !lastName) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    console.log('Creating GHL location for:', email);

    // Create a location in GHL
    console.log('Request payload:', JSON.stringify({
      name: companyName || `${firstName}'s Business`,
      email: email,
      phone: '',
      address: {
        city: '',
        state: '',
        country: '',
      },
      timezone: 'America/New_York', // Default timezone
      companyName: companyName || `${firstName}'s Business`,
      firstName: firstName,
      lastName: lastName
    }));

    const createResponse = await fetch(`${GHL_API_URL}/locations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': GHL_TOKEN,
        'Version': '2021-07-28'
      },
      body: JSON.stringify({
        name: companyName || `${firstName}'s Business`,
        email: email,
        phone: '',
        address: {
          city: '',
          state: '',
          country: '',
        },
        timezone: 'America/New_York', // Default timezone
        companyName: companyName || `${firstName}'s Business`,
        firstName: firstName,
        lastName: lastName
      }),
    });

    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      console.error('Error creating location:', errorText);
      console.error('Status code:', createResponse.status);
      console.error('Headers:', JSON.stringify([...createResponse.headers.entries()]));
      return res.status(createResponse.status).json({
        error: `Failed to create GHL location: ${errorText}`,
        status: createResponse.status,
        statusText: createResponse.statusText
      });
    }

    const locationData = await createResponse.json();
    console.log('Location created:', locationData.id);

    // Generate a random password
    const password = Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8);

    // Create a user for this location
    const createUserResponse = await fetch(`${GHL_API_URL}/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': GHL_TOKEN,
        'Version': '2021-07-28'
      },
      body: JSON.stringify({
        email: email,
        firstName: firstName,
        lastName: lastName,
        password: password,
        locationId: locationData.id,
        role: 'admin',
        type: 'account'
      }),
    });

    if (!createUserResponse.ok) {
      const errorText = await createUserResponse.text();
      console.error('Error creating user:', errorText);
      return res.status(createUserResponse.status).json({
        error: `Failed to create GHL user: ${errorText}`,
        locationId: locationData.id
      });
    }

    const userData = await createUserResponse.json();
    console.log('User created:', userData.id);

    // Return success response
    return res.status(200).json({
      success: true,
      location: locationData,
      user: userData
    });
  } catch (error) {
    console.error('Server error:', error);
    return res.status(500).json({
      error: 'Server error',
      message: error.message
    });
  }
});

// Start the server
app.listen(port, () => {
  console.log(`Proxy server running at http://localhost:${port}`);
});
