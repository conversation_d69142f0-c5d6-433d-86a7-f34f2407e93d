# GHL SSO Implementation Guide

## Overview

This document describes the implementation of GoHighLevel (GHL) Single Sign-On (SSO) integration for seamless user authentication and account management. The implementation allows users to sign up on our platform and automatically get a GHL account created under our agency with seamless authentication to their white-labeled GHL dashboard.

## Architecture

### Components

1. **GHL SSO Edge Function** (`ghl-sso-users`)
   - Handles all GHL SSO User Management API operations
   - Creates, searches, updates, and manages GHL users
   - Uses the GHL SSO Users API specification

2. **GHL SSO Service** (`src/services/ghlSSOService.ts`)
   - TypeScript service layer for GHL SSO operations
   - Provides high-level methods for user management
   - Handles complete account creation workflow

3. **Updated Signup Flow** (`src/pages/TestSocialLogin.tsx`)
   - Integrates social login with GHL account creation
   - Handles existing user detection and redirection
   - Supports both social login and email-only signup

## Key Features

### 1. Seamless Account Creation
- Users sign up via Google/social login or email
- Automatically creates GHL account under our agency
- Maps Supabase user ID to GHL user via `externalUserId`
- Generates required user details from email and geolocation

### 2. Existing User Detection
- Checks if user already exists in GHL before account creation
- Redirects existing users to their GHL dashboard
- Prevents duplicate account creation

### 3. White-labeled Dashboard Access
- Users are redirected to `https://app.aha-innovations.com/v2/location/{locationId}`
- Seamless authentication once SSO is enabled by GHL
- Maintains branded experience throughout

## API Endpoints

### GHL SSO Users Edge Function

**Base URL:** `/functions/v1/ghl-sso-users`

#### Create User
```
POST /functions/v1/ghl-sso-users?action=create
```

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "+**********",
  "locationIds": ["location-id"],
  "externalUserId": "supabase-user-id",
  "type": "account",
  "role": "admin"
}
```

#### Search Users
```
POST /functions/v1/ghl-sso-users?action=search
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "externalUserId": "supabase-user-id",
  "limit": 25
}
```

#### Get User
```
GET /functions/v1/ghl-sso-users?action=get&userId=user-id
```

#### Update User
```
PUT /functions/v1/ghl-sso-users?action=update&userId=user-id
```

#### Delete User
```
DELETE /functions/v1/ghl-sso-users?action=delete&userId=user-id
```

## Service Layer Methods

### GHLSSOService

#### `createUser(userData: CreateGHLUserData): Promise<GHLUser>`
Creates a new GHL user for SSO integration.

#### `searchUsers(searchData: SearchGHLUserData): Promise<GHLUser[]>`
Searches for existing GHL users by email or externalUserId.

#### `checkUserExists(email: string, externalUserId?: string): Promise<{exists: boolean, user?: GHLUser}>`
Checks if a user already exists in GHL.

#### `createCompleteAccount(userData): Promise<{success: boolean, locationId: string, userId: string, dashboardUrl: string}>`
Creates a complete GHL account (Location + User) for seamless onboarding.

#### `authenticateExistingUser(email: string, supabaseUserId: string): Promise<{success: boolean, dashboardUrl?: string}>`
Handles authentication for existing GHL users.

## Signup Flow

### 1. Social Login Flow
```
User clicks "Sign in with Google" 
→ Google OAuth authentication
→ Check if user exists in GHL
→ If exists: Redirect to GHL dashboard
→ If not exists: Create GHL account → Redirect to dashboard
```

### 2. Email-Only Signup Flow
```
User enters email → Supabase email confirmation
→ User confirms email → Create GHL account
→ Redirect to GHL dashboard
```

## Environment Variables

The following environment variables are required:

- `GHL_AGENCY_TOKEN` - GHL Agency Bearer token with 'Create or Edit Users' scope
- `GHL_COMPANY_ID` - GHL Company ID for the agency
- `GHL_CLIENT_ID` - GHL OAuth Client ID (for future OAuth features)
- `GHL_CLIENT_SECRET` - GHL OAuth Client Secret (for future OAuth features)

## Database Schema

### Profiles Table Updates

The `profiles` table stores the mapping between Supabase users and GHL accounts:

```sql
-- Additional columns for GHL SSO integration
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS ghl_external_user_id TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS ghl_user_id TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS ghl_location_id TEXT;
```

## Testing

### GHL SSO Test Component

A test component (`GHLSSOTest`) is available at `/test-social-login` for testing:

1. **Search Users** - Test user search functionality
2. **Check User Exists** - Test existence checking
3. **Create User** - Test user creation (use test email)

### Manual Testing Steps

1. Navigate to `/test-social-login`
2. Use the GHL SSO Test section
3. Enter a test email address
4. Run each test to verify functionality
5. Check test results for success/failure

## Deployment

### Deploy Edge Function
```bash
supabase functions deploy ghl-sso-users
```

### Environment Variables
Set the required environment variables in Supabase Dashboard:
1. Go to Project Settings → Edge Functions
2. Add the GHL credentials as secrets

## SSO Activation

### Current Status
- ✅ GHL SSO User Management API implemented
- ✅ Complete account creation workflow
- ✅ User existence checking and redirection
- ⏳ Waiting for GHL SSO beta access approval

### Next Steps (After SSO Activation)
1. Configure SSO settings in GHL dashboard
2. Update white-label domain configuration
3. Test seamless authentication flow
4. Enable production SSO integration

## Security Considerations

1. **API Keys** - All GHL credentials stored as Supabase secrets
2. **CORS** - Restricted to allowed origins only
3. **User Mapping** - Secure mapping via `externalUserId`
4. **Error Handling** - Graceful fallbacks for API failures

## Troubleshooting

### Common Issues

1. **"GHL credentials not found"**
   - Check environment variables in Supabase
   - Verify `GHL_AGENCY_TOKEN` and `GHL_COMPANY_ID` are set

2. **"Failed to create GHL user"**
   - Check GHL API token permissions
   - Verify location IDs are valid
   - Check API rate limits

3. **"User already exists"**
   - This is expected behavior for existing users
   - User should be redirected to dashboard

### Debug Mode

Enable debug logging by checking browser console and Supabase Edge Function logs.

## Future Enhancements

1. **Bulk User Management** - Import existing users
2. **Advanced Permissions** - Granular role management
3. **SSO Configuration UI** - Admin interface for SSO settings
4. **Analytics Integration** - Track user onboarding metrics
