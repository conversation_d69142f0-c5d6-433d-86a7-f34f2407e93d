import fetch from 'node-fetch';

// GHL API constants
const GHL_API_URL = 'https://services.leadconnectorhq.com';
const GHL_TOKEN = 'pit-a329e4c6-962c-4d43-9c41-01eb6933dbea';

async function testGHLAPI() {
  try {
    console.log('Testing GHL API v2 with correct headers...');
    
    // Try to get locations/sub-accounts
    const response = await fetch(`${GHL_API_URL}/agency/locations`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': GHL_TOKEN,
        'Version': '2021-07-28'
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response status text:', response.statusText);
    
    const responseHeaders = {};
    response.headers.forEach((value, name) => {
      responseHeaders[name] = value;
    });
    console.log('Response headers:', JSON.stringify(responseHeaders, null, 2));
    
    const responseText = await response.text();
    console.log('Response body:', responseText);
    
    if (response.ok) {
      console.log('Success! The token is working correctly.');
    } else {
      console.log('Failed to access the API. Please check the token and endpoint.');
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the test
testGHLAPI();
