# Supabase Edge Functions Setup Guide

This guide explains how to set up and use the Supabase Edge Functions for GoHighLevel integration.

## Edge Functions

The following Edge Functions have been created:

1. `get-ghl-credentials` - Returns the GHL credentials from environment variables
2. `check-ghl-user-exists` - Checks if a user already exists in GHL
3. `create-ghl-full-account` - Creates a GHL location and user account
4. `create-ghl-subaccount` - Legacy function for creating a GHL subaccount
5. `exchange-ghl-auth-code` - Exchanges an OAuth code for an access token
6. `ghl-sso-users` - **NEW** GHL SSO User Management API for seamless single sign-on

## Environment Variables

The following environment variables need to be set in your Supabase project:

- `GHL_AGENCY_TOKEN` - Your GoHighLevel Agency Token (required)
- `GHL_COMPANY_ID` - Your GoHighLevel Company ID (optional)
- `GHL_CLIENT_ID` - Your GoHighLevel Client ID (optional, for OAuth flow)
- `GHL_CLIENT_SECRET` - Your GoHighLevel Client Secret (optional, for OAuth flow)

## Setting Up Environment Variables

1. Go to your Supabase dashboard
2. Navigate to "Settings" > "API"
3. Scroll down to "Project API keys" and copy the "anon" key
4. Go to "Edge Functions" > "Secrets"
5. Add the environment variables listed above

## Local Development

For local development, create a `.env` file in the root of your project with the following variables:

```
VITE_SUPABASE_URL=https://fpratwslcktwpzlbzlhm.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
```

## Security Considerations

- The Edge Functions are secured with proper CORS headers and authentication
- All API calls to GHL are made from the server-side, keeping your credentials secure
- The frontend never has direct access to GHL credentials
- All requests to Edge Functions require the Supabase anon key for authentication

## Troubleshooting

If you encounter issues with the Edge Functions:

1. Check that all required environment variables are set
2. Verify that the Edge Functions are deployed correctly
3. Check the Supabase logs for any errors
4. Ensure your GHL Agency Token has the necessary permissions

## Updating Supabase URL and Keys

If you need to update the Supabase URL and anon key:

1. Update the `.env` file with the new values
2. If you're using hardcoded fallbacks, update them in:
   - `src/utils/supabaseClient.ts`

## Testing Edge Functions

You can test the Edge Functions directly from the Supabase dashboard:

1. Go to "Edge Functions"
2. Click on the function you want to test
3. Click on the "Test" tab
4. Enter a sample JSON payload (e.g., `{"email": "<EMAIL>"}`)
5. Click "Run" to test the function
