import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface ContactData {
  locationId: string;
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  phone?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  goal?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get GHL credentials from environment variables (secure server-side)
    // @ts-ignore
    const ghlToken = Deno.env.get('GHL_LOCATION_TOKEN');
    // @ts-ignore
    const companyId = Deno.env.get('GHL_COMPANY_ID');

    console.log('=== TOKEN DEBUG INFO ===');
    console.log('GHL_LOCATION_TOKEN found:', !!ghlToken);
    console.log('GHL_LOCATION_TOKEN length:', ghlToken ? ghlToken.length : 0);
    console.log('GHL_LOCATION_TOKEN starts with:', ghlToken ? ghlToken.substring(0, 15) + '...' : 'N/A');
    console.log('Expected token starts with:', 'pit-d6b26228-e407...');

    if (!ghlToken) {
      console.error('GHL_LOCATION_TOKEN not found in environment variables');
      throw new Error('GHL_LOCATION_TOKEN not found in environment variables');
    }

    // Parse the request body
    const contactData: ContactData = await req.json();

    // Validate required fields
    if (!contactData.locationId || !contactData.email || !contactData.firstName || !contactData.lastName) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Missing required fields: locationId, email, firstName, lastName' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    console.log('Creating GHL contact with data:', {
      locationId: contactData.locationId,
      email: contactData.email,
      firstName: contactData.firstName,
      lastName: contactData.lastName,
    });

    console.log('GHL Token available:', !!ghlToken);
    console.log('GHL Token length:', ghlToken ? ghlToken.length : 0);
    console.log('GHL Token starts with:', ghlToken ? ghlToken.substring(0, 10) + '...' : 'N/A');
    console.log('Expected token starts with:', 'pit-d6b26228-e407...');

    // Create contact data matching exact curl format that worked
    const ghlContactData = {
      firstName: contactData.firstName,
      lastName: contactData.lastName,
      name: `${contactData.firstName} ${contactData.lastName}`,
      email: contactData.email,
      phone: contactData.phone || "+1234567890", // Default phone if not provided
      locationId: contactData.locationId || 'LL7TmGrkL72EOf8O0FKA',
      tags: ['website_signups'], // Tag for tracking and automation
      companyName: contactData.companyName,
      city: contactData.city,
      state: contactData.state,
      country: contactData.country || 'US',
      postalCode: contactData.postalCode
    };

    console.log('Sending to GHL API:', JSON.stringify(ghlContactData, null, 2));

    // Make the API call to GHL with correct headers (following official format)
    const ghlResponse = await fetch('https://services.leadconnectorhq.com/contacts/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Version': '2021-07-28',
        'Authorization': `Bearer ${ghlToken}`,
      },
      body: JSON.stringify(ghlContactData),
    });

    const responseText = await ghlResponse.text();
    console.log('GHL API response status:', ghlResponse.status);
    console.log('GHL API response:', responseText);

    let ghlData;
    try {
      ghlData = JSON.parse(responseText);
    } catch (parseError) {
      console.error('Failed to parse GHL response:', parseError);
      throw new Error(`Invalid JSON response from GHL API: ${responseText}`);
    }

    if (!ghlResponse.ok) {
      console.error('GHL API error:', ghlData);
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to create GHL contact',
          details: ghlData,
          status: ghlResponse.status,
        }),
        { 
          status: ghlResponse.status, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Success response
    return new Response(
      JSON.stringify({
        success: true,
        message: 'GHL contact created successfully with website_signups tag',
        contact: ghlData,
        tags: ghlContactData.tags,
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error creating GHL contact:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Internal server error',
        timestamp: new Date().toISOString(),
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
})
