import { corsHeaders } from '../_shared/cors.ts'

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const stripeSecretKey = Deno.env.get('STRIPE_SECRET_KEY')
    const couponId = 'BNyjT7Un'

    if (!stripeSecretKey) {
      throw new Error('STRIPE_SECRET_KEY not found in environment')
    }

    console.log('Testing Stripe connection...')

    // Test 1: Verify Stripe API connection
    const accountResponse = await fetch('https://api.stripe.com/v1/account', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${stripeSecretKey}`,
      }
    })

    if (!accountResponse.ok) {
      throw new Error(`Stripe API connection failed: ${await accountResponse.text()}`)
    }

    const accountData = await accountResponse.json()
    console.log('Stripe account verified:', accountData.id)

    // Test 2: Verify the coupon exists
    const couponResponse = await fetch(`https://api.stripe.com/v1/coupons/${couponId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${stripeSecretKey}`,
      }
    })

    if (!couponResponse.ok) {
      throw new Error(`Coupon verification failed: ${await couponResponse.text()}`)
    }

    const couponData = await couponResponse.json()
    console.log('Coupon verified:', couponData)

    // Test 3: Verify the Free Plan price exists
    const priceId = 'price_1RbMOCL5UMPPQRhs2NiED6ml'
    const priceResponse = await fetch(`https://api.stripe.com/v1/prices/${priceId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${stripeSecretKey}`,
      }
    })

    if (!priceResponse.ok) {
      throw new Error(`Price verification failed: ${await priceResponse.text()}`)
    }

    const priceData = await priceResponse.json()
    console.log('Price verified:', priceData)

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Stripe connection and coupon verified successfully',
        account: {
          id: accountData.id,
          business_profile: accountData.business_profile
        },
        coupon: {
          id: couponData.id,
          percent_off: couponData.percent_off,
          duration: couponData.duration,
          valid: couponData.valid
        },
        price: {
          id: priceData.id,
          unit_amount: priceData.unit_amount,
          currency: priceData.currency,
          recurring: priceData.recurring
        }
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Stripe verification error:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        details: 'Check the logs for more information'
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
