import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface UserUsage {
  pipelines: number;
  contacts: number;
  emails_sent_this_month: number;
  funnels: number;
  users: number;
  plan: string;
  usage_reset_date: string;
}

interface PlanLimits {
  pipelines: number;
  contacts: number;
  emails: number;
  funnels: number;
  users: number;
}

const PLAN_LIMITS: Record<string, PlanLimits> = {
  free: {
    pipelines: 3,
    contacts: 100,
    emails: 100,
    funnels: 1,
    users: 1
  },
  basic: {
    pipelines: 15,
    contacts: 1000,
    emails: 1000,
    funnels: -1, // unlimited
    users: 3
  },
  agency: {
    pipelines: 50,
    contacts: 10000,
    emails: 7500,
    funnels: -1,
    users: 10
  },
  enterprise: {
    pipelines: -1, // unlimited
    contacts: -1,
    emails: -1,
    funnels: -1,
    users: -1
  }
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get user from JWT token
    const authHeader = req.headers.get('Authorization')!
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get user's profile to determine current plan
    const { data: profile, error: profileError } = await supabaseClient
      .from('profiles')
      .select('current_plan, ghl_location_id')
      .eq('id', user.id)
      .single()

    if (profileError) {
      console.error('Error fetching profile:', profileError)
      return new Response(
        JSON.stringify({ error: 'Could not fetch user profile' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const currentPlan = profile?.current_plan || 'free'
    const locationId = profile?.ghl_location_id

    // Get or create usage record for current month
    const currentMonth = new Date().toISOString().slice(0, 7) // YYYY-MM format
    
    let { data: usage, error: usageError } = await supabaseClient
      .from('user_usage')
      .select('*')
      .eq('user_id', user.id)
      .eq('month', currentMonth)
      .single()

    if (usageError && usageError.code === 'PGRST116') {
      // No usage record for this month, create one
      const { data: newUsage, error: createError } = await supabaseClient
        .from('user_usage')
        .insert({
          user_id: user.id,
          month: currentMonth,
          pipelines: 0,
          contacts: 0,
          emails_sent: 0,
          funnels: 0,
          users: 1, // User themselves
          plan: currentPlan,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (createError) {
        console.error('Error creating usage record:', createError)
        return new Response(
          JSON.stringify({ error: 'Could not create usage record' }),
          { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }

      usage = newUsage
    } else if (usageError) {
      console.error('Error fetching usage:', usageError)
      return new Response(
        JSON.stringify({ error: 'Could not fetch usage data' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // If we have a GHL location ID, try to get real-time data from GHL
    let realTimeUsage = {
      pipelines: usage.pipelines,
      contacts: usage.contacts,
      emails_sent: usage.emails_sent,
      funnels: usage.funnels,
      users: usage.users
    }

    if (locationId) {
      try {
        // Fetch real-time data from GHL API
        const ghlUsage = await fetchGHLUsageData(locationId)
        if (ghlUsage) {
          realTimeUsage = {
            ...realTimeUsage,
            ...ghlUsage
          }

          // Update our database with real-time data
          await supabaseClient
            .from('user_usage')
            .update({
              pipelines: realTimeUsage.pipelines,
              contacts: realTimeUsage.contacts,
              funnels: realTimeUsage.funnels,
              updated_at: new Date().toISOString()
            })
            .eq('user_id', user.id)
            .eq('month', currentMonth)
        }
      } catch (error) {
        console.warn('Could not fetch real-time GHL data:', error)
        // Continue with cached data
      }
    }

    // Calculate usage percentages and warnings
    const limits = PLAN_LIMITS[currentPlan]
    const usageWithLimits = {
      pipelines: {
        current: realTimeUsage.pipelines,
        limit: limits.pipelines,
        percentage: limits.pipelines === -1 ? 0 : (realTimeUsage.pipelines / limits.pipelines) * 100,
        unlimited: limits.pipelines === -1
      },
      contacts: {
        current: realTimeUsage.contacts,
        limit: limits.contacts,
        percentage: limits.contacts === -1 ? 0 : (realTimeUsage.contacts / limits.contacts) * 100,
        unlimited: limits.contacts === -1
      },
      emails: {
        current: realTimeUsage.emails_sent,
        limit: limits.emails,
        percentage: limits.emails === -1 ? 0 : (realTimeUsage.emails_sent / limits.emails) * 100,
        unlimited: limits.emails === -1
      },
      funnels: {
        current: realTimeUsage.funnels,
        limit: limits.funnels,
        percentage: limits.funnels === -1 ? 0 : (realTimeUsage.funnels / limits.funnels) * 100,
        unlimited: limits.funnels === -1
      },
      users: {
        current: realTimeUsage.users,
        limit: limits.users,
        percentage: limits.users === -1 ? 0 : (realTimeUsage.users / limits.users) * 100,
        unlimited: limits.users === -1
      }
    }

    // Determine if user should see upgrade prompts
    const shouldShowUpgrade = Object.values(usageWithLimits).some(
      metric => !metric.unlimited && metric.percentage >= 80
    )

    const response = {
      plan: currentPlan,
      usage: realTimeUsage,
      limits: limits,
      usage_with_limits: usageWithLimits,
      should_show_upgrade: shouldShowUpgrade,
      month: currentMonth,
      last_updated: new Date().toISOString()
    }

    return new Response(
      JSON.stringify(response),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in get-user-usage function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

// Helper function to fetch usage data from GHL API
async function fetchGHLUsageData(locationId: string) {
  try {
    const accessToken = Deno.env.get('GHL_PRIVATE_INTEGRATION_TOKEN')
    if (!accessToken) {
      throw new Error('GHL token not configured')
    }

    // Fetch pipelines count
    const pipelinesResponse = await fetch(`https://services.leadconnectorhq.com/opportunities/pipelines?locationId=${locationId}`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Version': '2021-04-15'
      }
    })

    // Fetch contacts count
    const contactsResponse = await fetch(`https://services.leadconnectorhq.com/contacts/?locationId=${locationId}&limit=1`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Version': '2021-04-15'
      }
    })

    // Fetch funnels count (if available in API)
    // Note: You may need to adjust this based on actual GHL API endpoints

    let pipelines = 0
    let contacts = 0
    let funnels = 0

    if (pipelinesResponse.ok) {
      const pipelinesData = await pipelinesResponse.json()
      pipelines = pipelinesData.pipelines?.length || 0
    }

    if (contactsResponse.ok) {
      const contactsData = await contactsResponse.json()
      contacts = contactsData.meta?.total || 0
    }

    return {
      pipelines,
      contacts,
      funnels // This might need to be fetched differently
    }

  } catch (error) {
    console.error('Error fetching GHL usage data:', error)
    return null
  }
}
