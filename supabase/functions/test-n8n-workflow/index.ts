// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.com/deploy/docs/projects
// Setup type definitions for built-in Supabase Runtime APIs
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// Define the handler function
serve(async (req: Request) => {
  // CORS headers - Allow both local development and production domains
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers,
    });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers,
    });
  }

  try {
    // Parse the request body
    const userData = await req.json();

    // Validate required fields
    const requiredFields = ['email', 'firstName', 'lastName'];
    for (const field of requiredFields) {
      if (!userData[field]) {
        return new Response(JSON.stringify({ error: `Missing required field: ${field}` }), {
          status: 400,
          headers,
        });
      }
    }

    console.log('Test n8n workflow received data:', userData);

    // TEMPORARY: Use the test webhook URL for now to help with n8n mapping
    // @ts-ignore
    const n8nWebhookUrl = 'https://n8n-1-i8dz.onrender.com/webhook-test/aha-signup';
    console.log('Using n8n webhook URL:', n8nWebhookUrl);

    // Log all available environment variables (names only)
    try {
      // @ts-ignore
      console.log('Available environment variables:', Object.keys(Deno.env.toObject()));
    } catch (e) {
      console.error('Could not list environment variables:', e);
    }

    // Prepare the request body for n8n
    // Structure the data exactly as expected by the n8n workflow
    const n8nRequestBody = {
      body: {
        // User data
        email: userData.email,
        first_name: userData.firstName,
        last_name: userData.lastName,
        company_name: userData.companyName || `${userData.firstName}'s Agency`,
        password: userData.password || 'defaultPassword123!',

        // Location data in the nested structure expected by n8n
        location: {
          address: '123 Main Street',
          city: 'Default City',
          state: 'Default State',
          country: 'US',
          postalCode: '12345'
        },

        // Additional data
        phone: userData.phone || '',
        website: userData.website || '',
        timezone: 'US/Central',
        companyId: "gPkTndcx94O3r573TOMx",

        // These fields are not used in the template but include them for completeness
        role: userData.role || '',
        company_size: userData.company_size || '',
        referral_source: userData.referral_source || '',

        // Include these in case they're used elsewhere in the workflow
        prospectInfo: {
          firstName: userData.firstName,
          lastName: userData.lastName,
          email: userData.email
        },
        settings: {
          allowDuplicateContact: false,
          allowDuplicateOpportunity: false,
          allowFacebookNameMerge: false,
          disableContactTimezone: false
        },
        social: {
          facebookUrl: '',
          googlePlus: '',
          linkedIn: '',
          foursquare: '',
          twitter: '',
          yelp: '',
          instagram: '',
          youtube: '',
          pinterest: '',
          blogRss: '',
          googlePlacesId: ''
        }
      }
    };

    console.log('Sending data to n8n webhook:', JSON.stringify(n8nRequestBody));

    try {
      // Forward the data to n8n webhook
      const n8nResponse = await fetch(n8nWebhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(n8nRequestBody),
      });

      if (!n8nResponse.ok) {
        const errorText = await n8nResponse.text();
        console.error('n8n webhook error:', {
          status: n8nResponse.status,
          statusText: n8nResponse.statusText,
          body: errorText
        });

        // If n8n call fails, return a mock response for testing
        console.log('Falling back to mock response');
        return new Response(JSON.stringify({
          success: true,
          locationId: "mock-location-" + Math.floor(Math.random() * 1000),
          userId: "mock-user-" + Math.floor(Math.random() * 1000),
          message: 'Test successful with mock response (n8n call failed).',
          receivedData: n8nRequestBody,
          n8nError: {
            status: n8nResponse.status,
            statusText: n8nResponse.statusText
          }
        }), {
          status: 200,
          headers
        });
      }

      // Parse the n8n response
      const result = await n8nResponse.json();

      // Return the result to the client
      return new Response(JSON.stringify({
        success: true,
        locationId: result.locationId || "mock-location-" + Math.floor(Math.random() * 1000),
        userId: result.userId || "mock-user-" + Math.floor(Math.random() * 1000),
        message: 'GHL account creation triggered successfully',
        n8nResponse: result
      }), {
        status: 200,
        headers
      });
    } catch (fetchError) {
      console.error('Error fetching n8n webhook:', fetchError);

      // If fetch fails completely, return a mock response
      return new Response(JSON.stringify({
        success: true,
        locationId: "mock-location-" + Math.floor(Math.random() * 1000),
        userId: "mock-user-" + Math.floor(Math.random() * 1000),
        message: 'Test successful with mock response (fetch failed).',
        receivedData: n8nRequestBody,
        fetchError: fetchError.message
      }), {
        status: 200,
        headers
      });
    }

  } catch (error) {
    console.error('Error in test-n8n-workflow function:', error);

    // Check for environment variable issues
    let errorMessage = error.message || 'Failed to trigger n8n workflow';
    let errorDetails = 'Check Supabase logs for more information';

    return new Response(JSON.stringify({
      error: errorMessage,
      details: errorDetails,
      success: false
    }), {
      status: 500,
      headers
    });
  }
});
