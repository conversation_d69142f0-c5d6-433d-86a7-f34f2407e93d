
// Setup type definitions for built-in Supabase Runtime APIs
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// GHL API base URL
const GHL_API_URL = 'https://services.leadconnectorhq.com';

serve(async (req: Request) => {
  // CORS headers - Allow both local development and production domains
  const headers = {
    'Access-Control-Allow-Origin': '*', // Allow all origins for testing
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers });
  }

  try {
    // Parse the request body
    const { code, redirectUri } = await req.json();

    if (!code) {
      throw new Error('Authorization code is required');
    }

    if (!redirectUri) {
      throw new Error('Redirect URI is required');
    }

    // Get GHL credentials from environment variables
    // @ts-ignore
    const clientId = Deno.env.get('GHL_CLIENT_ID');
    // @ts-ignore
    const clientSecret = Deno.env.get('GHL_CLIENT_SECRET');

    if (!clientId || !clientSecret) {
      throw new Error('GHL credentials not found in environment variables');
    }

    // Exchange authorization code for access token
    const tokenResponse = await fetch(`${GHL_API_URL}/oauth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: clientId,
        client_secret: clientSecret,
        code: code,
        grant_type: 'authorization_code',
        redirect_uri: redirectUri,
      }),
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      throw new Error(`Failed to exchange authorization code: ${errorText}`);
    }

    const tokenData = await tokenResponse.json();

    return new Response(JSON.stringify(tokenData), {
      status: 200,
      headers
    });
  } catch (error) {
    console.error('Error exchanging GHL auth code:', error);

    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers
    });
  }
});
