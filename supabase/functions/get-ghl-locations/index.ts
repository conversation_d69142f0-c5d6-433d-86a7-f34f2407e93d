import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'GET') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }

  try {
    // Get GHL credentials from environment variables (secure server-side)
    // @ts-ignore
    const ghlToken = Deno.env.get('GHL_AGENCY_TOKEN');

    if (!ghlToken) {
      throw new Error('GHL_AGENCY_TOKEN not found in environment variables');
    }

    console.log('Fetching GHL locations...');
    console.log('GHL Token available:', !!ghlToken);
    console.log('GHL Token length:', ghlToken ? ghlToken.length : 0);
    console.log('GHL Token starts with:', ghlToken ? ghlToken.substring(0, 10) + '...' : 'N/A');
    console.log('Expected token starts with:', 'pit-ef4e9f...');

    // Make the API call to GHL
    const ghlResponse = await fetch('https://services.leadconnectorhq.com/locations/', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${ghlToken}`,
        'Content-Type': 'application/json',
        'Version': '2021-07-28',
      },
    });

    const responseText = await ghlResponse.text();
    console.log('GHL API response status:', ghlResponse.status);
    console.log('GHL API response:', responseText);

    let ghlData;
    try {
      ghlData = JSON.parse(responseText);
    } catch (parseError) {
      console.error('Failed to parse GHL response:', parseError);
      throw new Error(`Invalid JSON response from GHL API: ${responseText}`);
    }

    if (!ghlResponse.ok) {
      console.error('GHL API error:', ghlData);
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to fetch GHL locations',
          details: ghlData,
          status: ghlResponse.status,
        }),
        { 
          status: ghlResponse.status, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Success response
    return new Response(
      JSON.stringify({
        success: true,
        message: 'GHL locations fetched successfully',
        locations: ghlData.locations || ghlData,
        count: ghlData.locations ? ghlData.locations.length : (Array.isArray(ghlData) ? ghlData.length : 1),
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error fetching GHL locations:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || 'Internal server error',
        timestamp: new Date().toISOString(),
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
})
