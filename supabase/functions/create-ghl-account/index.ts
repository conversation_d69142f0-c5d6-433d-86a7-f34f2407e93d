// Supabase Edge Function to create a GHL account directly
// This replaces the n8n workflow for account creation

// Setup type definitions for built-in Supabase Runtime APIs
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// GHL API base URL
const GHL_API_URL = 'https://services.leadconnectorhq.com';

// Get GHL API credentials from environment variables
function getGHLCredentials() {
  try {
    // @ts-ignore - Deno.env is available in Supabase Edge Functions
    const clientId = Deno.env.get('GHL_CLIENT_ID');
    // @ts-ignore
    const clientSecret = Deno.env.get('GHL_CLIENT_SECRET');
    // @ts-ignore
    const agencyToken = Deno.env.get('GHL_AGENCY_TOKEN');
    // @ts-ignore
    const locationToken = Deno.env.get('GHL_LOCATION_TOKEN');
    // @ts-ignore
    const companyId = Deno.env.get('GHL_COMPANY_ID');

    console.log('GHL credentials retrieved:', {
      hasClientId: !!clientId,
      hasClientSecret: !!clientSecret,
      hasAgencyToken: !!agencyToken,
      hasLocationToken: !!locationToken,
      hasCompanyId: !!companyId
    });

    return {
      clientId,
      clientSecret,
      agencyToken,
      locationToken,
      companyId
    };
  } catch (error) {
    console.error('Error retrieving GHL credentials:', error);
    return {
      clientId: null,
      clientSecret: null,
      agencyToken: null,
      locationToken: null,
      companyId: null
    };
  }
}

// Make an authenticated request to the GHL API
async function callGHLApi(
  endpoint: string,
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE',
  token: string,
  body?: any
): Promise<any> {
  const url = `${GHL_API_URL}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    'Version': '2021-07-28'
  };

  const options: RequestInit = {
    method,
    headers
  };

  if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    options.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(url, options);

    if (!response.ok) {
      // Clone the response before reading it
      const clonedResponse = response.clone();
      let errorText;
      try {
        errorText = await clonedResponse.text();
      } catch (e) {
        errorText = `Failed to read error response: ${e.message}`;
      }

      console.error(`GHL API error (${method} ${endpoint}):`, {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      throw new Error(`GHL API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    // For DELETE requests or other requests that might not return JSON
    if (method === 'DELETE' || response.headers.get('content-length') === '0') {
      return { success: true };
    }

    // Clone the response before reading it as JSON
    const clonedResponse = response.clone();
    try {
      return await clonedResponse.json();
    } catch (e) {
      console.error(`Error parsing JSON response: ${e.message}`);
      // Try to read as text if JSON parsing fails
      const textResponse = await response.text();
      return { rawResponse: textResponse };
    }
  } catch (error) {
    console.error(`Network error calling GHL API (${method} ${endpoint}):`, error);
    throw error;
  }
}

// Create a location in GHL
async function createLocation(token: string, locationData: any): Promise<any> {
  return callGHLApi('/api/v1/locations', 'POST', token, locationData);
}

// Create a user in GHL
async function createUser(token: string, userData: any): Promise<any> {
  return callGHLApi('/api/v1/users', 'POST', token, userData);
}

serve(async (req: Request) => {
    // CORS headers - Allow both local development and production domains
    const headers = {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // Handle preflight OPTIONS request
    if (req.method === 'OPTIONS') {
      return new Response(null, {
        status: 204,
        headers,
      });
    }

    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        status: 405,
        headers,
      });
    }

    try {
      // Parse the request body
      const userData = await req.json();

      // Validate required fields
      const requiredFields = ['email', 'firstName', 'lastName'];
      for (const field of requiredFields) {
        if (!userData[field]) {
          return new Response(JSON.stringify({ error: `Missing required field: ${field}` }), {
            status: 400,
            headers,
          });
        }
      }

      console.log('Creating GHL account with data:', userData);

      // Get GHL API credentials
      const { agencyToken, companyId } = getGHLCredentials();

      if (!agencyToken) {
        return new Response(JSON.stringify({
          error: 'Missing GHL API credentials',
          details: 'GHL_AGENCY_TOKEN environment variable is not set'
        }), {
          status: 500,
          headers,
        });
      }

      if (!companyId) {
        return new Response(JSON.stringify({
          error: 'Missing GHL API credentials',
          details: 'GHL_COMPANY_ID environment variable is not set'
        }), {
          status: 500,
          headers,
        });
      }

      // Step 1: Create a location
      const locationData = {
        name: userData.companyName || `${userData.firstName}'s Agency`,
        email: userData.email,
        companyId: companyId,
        address1: userData.address || '123 Main Street',
        city: userData.city || 'Default City',
        state: userData.state || 'Default State',
        country: userData.country || 'US',
        postalCode: userData.postalCode || '12345',
        phone: userData.phone || '+15555555555',
        timezone: userData.timezone || 'America/New_York',
        currency: userData.currency || 'USD'
      };

      console.log('Creating location with data:', locationData);

      try {
        // Call GHL API to create location using our utility function
        const locationResult = await createLocation(agencyToken, locationData);
        const locationId = locationResult.id;

        console.log('Location created successfully:', locationId);

        // Step 2: Create a user and associate with the location
        const userData2 = {
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          password: userData.password || 'DefaultPassword123!',
          type: 'account',
          role: 'admin',
          locationIds: [locationId],
          permissions: {
            campaignsEnabled: true,
            contactsEnabled: true,
            workflowsEnabled: true,
            triggersEnabled: true,
            funnelsEnabled: true,
            opportunitiesEnabled: true,
            dashboardStatsEnabled: true,
            settingsEnabled: true,
            tagsEnabled: true,
            marketingEnabled: true,
            conversationsEnabled: true
          }
        };

        console.log('Creating user with data:', userData2);

        // Call GHL API to create user using our utility function
        const userResult = await createUser(agencyToken, userData2);
        const userId = userResult.id;

        console.log('User created successfully:', userId);

        // Return success response with location and user IDs
        return new Response(JSON.stringify({
          success: true,
          locationId: locationId,
          userId: userId,
          message: 'GHL account created successfully'
        }), {
          status: 200,
          headers
        });
      } catch (apiError) {
        console.error('GHL API error:', apiError);

        return new Response(JSON.stringify({
          error: apiError.message || 'Failed to create GHL account',
          details: 'Error calling GHL API'
        }), {
          status: 500,
          headers
        });
      }
    } catch (error) {
      console.error('Error creating GHL account:', error);

      return new Response(JSON.stringify({
        error: error.message || 'Failed to create GHL account',
        details: 'Check Supabase logs for more information'
      }), {
        status: 500,
        headers
      });
    }
  });
