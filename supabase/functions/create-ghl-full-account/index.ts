
// Setup type definitions for built-in Supabase Runtime APIs
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// GHL API base URL
const GHL_API_URL = 'https://services.leadconnectorhq.com';

serve(async (req: Request) => {
  // CORS headers - Allow specific origins for credentials
  const origin = req.headers.get('origin');
  const allowedOrigins = [
    'http://localhost:8080',
    'http://127.0.0.1:8080',
    'https://app.aha-innovations.com',
    'https://www.aha-innovations.com',
    'https://easy-ghl-launchpad.lovable.app'
  ];

  const headers = {
    'Access-Control-Allow-Origin': allowedOrigins.includes(origin) ? origin : 'http://localhost:8080',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Access-Control-Allow-Credentials': 'true',
    'Content-Type': 'application/json'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers });
  }

  try {
    // Parse the request body to get user data
    // @ts-ignore
    const userData = await req.json();
    const {
      email,
      firstName,
      lastName,
      companyName,
      password,
      city,
      state,
      country,
      phone,
      address,
      postalCode,
      role,
      company_size,
      referral_source
    } = userData;

    if (!email) {
      throw new Error('Email is required');
    }

    // Get GHL credentials from environment variables
    // For Agency access, we need the Agency Bearer Token and Company ID
    // @ts-ignore
    const agencyToken = Deno.env.get('GHL_AGENCY_TOKEN');
    // @ts-ignore
    const companyId = Deno.env.get('GHL_COMPANY_ID');

    if (!agencyToken) {
      throw new Error('GHL Agency Token not found in environment variables');
    }

    if (!companyId) {
      throw new Error('GHL_COMPANY_ID not found in environment variables');
    }

    // Use the Agency Bearer Token for authentication
    const accessToken = agencyToken;

    console.log('Using Agency Bearer Token for authentication');

    // Log the country value for debugging
    console.log('Country value received:', country);

    // Use country directly as 2-letter code (frontend now sends proper format)
    const formattedCountry = country || 'US';
    console.log('Using country code:', formattedCountry);

    // Step 2: Create a subaccount/location
    const createLocationResponse = await fetch(`${GHL_API_URL}/locations/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'Version': '2021-07-28'
      },
      body: JSON.stringify({
        name: companyName || `${firstName}'s Business`,
        phone: phone || '',
        companyId: companyId,
        address: address || '123 Main Street',
        city: city || 'Default City',
        state: state || 'Default State',
        country: formattedCountry,
        postalCode: postalCode || '12345',
        website: '',
        timezone: 'US/Central',
        email: email,
        prospectInfo: {
          firstName: firstName,
          lastName: lastName,
          email: email
        },
        settings: {
          allowDuplicateContact: false,
          allowDuplicateOpportunity: false,
          allowFacebookNameMerge: false,
          disableContactTimezone: false
        },
        social: {
          facebookUrl: '',
          googlePlus: '',
          linkedIn: '',
          foursquare: '',
          twitter: '',
          yelp: '',
          instagram: '',
          youtube: '',
          pinterest: '',
          blogRss: '',
          googlePlacesId: ''
        }
      }),
    });

    if (!createLocationResponse.ok) {
      const errorText = await createLocationResponse.text();
      throw new Error(`Failed to create GHL location: ${errorText}`);
    }

    const locationData = await createLocationResponse.json();
    const locationId = locationData.id;

    if (!locationId) {
      throw new Error('Location ID not found in GHL response');
    }

    // Step 3: Create a user account for this location
    // Using the endpoint from the n8n workflow: POST /users/
    const createUserResponse = await fetch(`${GHL_API_URL}/users/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'Version': '2021-07-28'
      },
      body: JSON.stringify({
        companyId: companyId,
        firstName: firstName,
        lastName: lastName,
        email: email,
        password: password || 'AhaDefualt*2025',
        phone: phone || '',
        type: 'account',
        role: 'admin',
        locationIds: [locationId],
        permissions: {
          campaignsEnabled: true,
          campaignsReadOnly: false,
          contactsEnabled: true,
          workflowsEnabled: true,
          workflowsReadOnly: false,
          triggersEnabled: true,
          funnelsEnabled: true,
          websitesEnabled: true,
          opportunitiesEnabled: true,
          dashboardStatsEnabled: true,
          bulkRequestsEnabled: true,
          appointmentsEnabled: true,
          reviewsEnabled: true,
          onlineListingsEnabled: true,
          phoneCallEnabled: true,
          conversationsEnabled: true,
          assignedDataOnly: false,
          settingsEnabled: true,
          tagsEnabled: true,
          leadValueEnabled: true,
          marketingEnabled: true,
          agentReportingEnabled: true,
          socialPlanner: true,
          bloggingEnabled: true,
          invoiceEnabled: true,
          contentAiEnabled: true,
          paymentsEnabled: true,
          communitiesEnabled: true
        }
      }),
    });

    if (!createUserResponse.ok) {
      const errorText = await createUserResponse.text();
      // If user creation fails but we created the location, return partial success
      return new Response(JSON.stringify({
        locationId: locationId,
        error: `User creation failed: ${errorText}`
      }), {
        status: 207,
        headers
      });
    }

    // @ts-ignore
    const userResponseData = await createUserResponse.json();
    const userId = userResponseData.id;

    console.log('GHL User created successfully:', userId);

    // Step 4: Create GHL Contact for automation triggering
    let contactCreated = false;
    try {
      // Get location-level token for contact creation
      const locationToken = Deno.env.get('GHL_LOCATION_TOKEN');

      if (locationToken) {
        const contactResponse = await fetch(`${GHL_API_URL}/contacts/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${locationToken}`,
            'Version': '2021-07-28'
          },
          body: JSON.stringify({
            locationId: locationId,
            email: email,
            firstName: firstName,
            lastName: lastName,
            companyName: companyName || `${firstName}'s Business`,
            phone: phone || '',
            city: city || '',
            state: state || '',
            country: country || 'US',
            tags: ['website_signups'] // Tag for automation triggering
          })
        });

        if (contactResponse.ok) {
          const contactData = await contactResponse.json();
          console.log('GHL Contact created successfully for automation:', contactData.contact?.id);
          contactCreated = true;
        } else {
          const errorText = await contactResponse.text();
          console.warn('Contact creation failed (automation may not trigger):', errorText);
        }
      } else {
        console.warn('Location token not found - contact creation skipped');
      }
    } catch (error) {
      console.warn('Contact creation error (automation may not trigger):', error);
    }

    // Step 5: Create Stripe Customer with 100% off coupon (replicating n8n workflow)
    let stripeCustomerId = null;
    let stripeCouponId = null;

    try {
      const stripeApiKey = Deno.env.get('STRIPE_SECRET_KEY');
      if (stripeApiKey) {
        // First, create or get the 100% off coupon
        try {
          const couponResponse = await fetch('https://api.stripe.com/v1/coupons', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${stripeApiKey}`,
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
              id: 'FREE_PLAN_100_OFF',
              percent_off: '100',
              duration: 'forever',
              name: 'Free Plan - 100% Off',
              metadata: JSON.stringify({
                plan_type: 'free',
                created_by: 'aha_innovations_api'
              })
            })
          });

          if (couponResponse.ok) {
            const couponData = await couponResponse.json();
            stripeCouponId = couponData.id;
            console.log('Stripe coupon created/retrieved:', stripeCouponId);
          } else {
            // Coupon might already exist, try to retrieve it
            const existingCouponResponse = await fetch('https://api.stripe.com/v1/coupons/FREE_PLAN_100_OFF', {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${stripeApiKey}`,
              }
            });

            if (existingCouponResponse.ok) {
              const existingCouponData = await existingCouponResponse.json();
              stripeCouponId = existingCouponData.id;
              console.log('Using existing Stripe coupon:', stripeCouponId);
            }
          }
        } catch (couponError) {
          console.warn('Stripe coupon creation/retrieval error:', couponError);
        }

        // Create Stripe customer
        const stripeResponse = await fetch('https://api.stripe.com/v1/customers', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${stripeApiKey}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            name: `${firstName} ${lastName}`,
            email: email,
            description: `Customer for ${companyName || `${firstName}'s Business`} - Free Tier with SaaS`,
            phone: phone || '',
            ...(stripeCouponId && { coupon: stripeCouponId })
          })
        });

        if (stripeResponse.ok) {
          const stripeData = await stripeResponse.json();
          stripeCustomerId = stripeData.id;
          console.log('Stripe customer created:', stripeCustomerId);
        } else {
          console.warn('Stripe customer creation failed, continuing without it');
        }
      }
    } catch (error) {
      console.warn('Stripe integration error:', error);
    }

    // Step 6: Create Stripe subscription with Free Plan (this will auto-create GHL subaccount)
    let subscriptionCreated = false;

    if (stripeCustomerId) {
      try {
        console.log('Creating Stripe subscription with Free Plan Improved...');

        // Create Stripe subscription with Free Plan + 100% coupon
        // This will automatically create the GHL subaccount via Stripe integration
        const subscriptionResponse = await fetch('https://api.stripe.com/v1/subscriptions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${stripeApiKey}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            customer: stripeCustomerId,
            'items[0][price]': 'price_1RbMOCL5UMPPQRhs2NiED6ml', // Free Plan Improved
            coupon: 'BNyjT7Un', // 100% forever coupon
            'metadata[signup_source]': 'aha_innovations_website',
            'metadata[plan_type]': 'free_plan_improved',
            'metadata[user_name]': `${firstName} ${lastName}`,
            'metadata[user_email]': email
          })
        });

        if (subscriptionResponse.ok) {
          const subscriptionResult = await subscriptionResponse.json();
          console.log('Stripe subscription created successfully:', subscriptionResult);
          subscriptionCreated = true;

          // The subscription creation should automatically create a GHL subaccount
          // We'll skip all SaaS enablement since Stripe handles it
          console.log('Subscription ID:', subscriptionResult.id);
          console.log('Stripe will automatically create GHL subaccount via integration');

        } else {
          const errorText = await subscriptionResponse.text();
          console.warn('Stripe subscription creation failed:', errorText);

          // If Stripe subscription fails, we could fall back to manual SaaS enablement
          // But for now, let's see what the error is
        }
      } catch (error) {
        console.warn('Stripe subscription creation error:', error);
      }
    } else {
      console.warn('No Stripe customer ID available for subscription creation');
    }

    // Step 7: Enable Premium Features (replicating n8n workflow)
    const premiumFeatures = [
      'contentAI',
      'workflow_premium_actions',
      'conversationAI',
      'workflow_ai',
      'whatsApp',
      'reviewsAI',
      'Phone',
      'Email'
    ];

    const featureResults = [];
    for (const feature of premiumFeatures) {
      try {
        const enableFeatureResponse = await fetch(`${GHL_API_URL}/saas-api/public-api/update-rebilling/${companyId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`,
            'Version': '2021-04-15',
            'channel': 'OAUTH',
            'source': 'INTEGRATION'
          },
          body: JSON.stringify({
            product: feature,
            locationIds: [locationId],
            config: {}
          })
        });

        if (enableFeatureResponse.ok) {
          console.log(`${feature} enabled successfully`);
          featureResults.push({ feature, status: 'enabled' });
        } else {
          console.warn(`${feature} enablement failed:`, await enableFeatureResponse.text());
          featureResults.push({ feature, status: 'failed' });
        }
      } catch (error) {
        console.warn(`${feature} enablement error:`, error);
        featureResults.push({ feature, status: 'error', error: error.message });
      }
    }

    // Return comprehensive response with all created resources
    return new Response(JSON.stringify({
      success: true,
      locationId: locationId,
      userId: userId,
      stripeCustomerId: stripeCustomerId,
      subscriptionCreated: subscriptionCreated,
      contactCreated: contactCreated,
      premiumFeatures: featureResults,
      role: role || '',
      company_size: company_size || '',
      referral_source: referral_source || '',
      message: `Complete GHL account setup finished - Location, User, Stripe Customer${subscriptionCreated ? ', Free Plan Subscription' : ''}, Premium Features${contactCreated ? ', and Contact for automation' : ''} configured`
    }), {
      status: 200,
      headers
    });
  } catch (error) {
    console.error('Error creating GHL account:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers
    });
  }
});
