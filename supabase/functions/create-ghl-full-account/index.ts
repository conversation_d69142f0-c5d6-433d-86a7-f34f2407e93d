
// Setup type definitions for built-in Supabase Runtime APIs
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// GHL API base URL
const GHL_API_URL = 'https://services.leadconnectorhq.com';

serve(async (req: Request) => {
  // CORS headers - Allow specific origins for credentials
  const origin = req.headers.get('origin');
  const allowedOrigins = [
    'http://localhost:8080',
    'http://127.0.0.1:8080',
    'https://app.aha-innovations.com',
    'https://www.aha-innovations.com',
    'https://easy-ghl-launchpad.lovable.app'
  ];

  const headers = {
    'Access-Control-Allow-Origin': allowedOrigins.includes(origin) ? origin : 'http://localhost:8080',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
    'Access-Control-Allow-Credentials': 'true',
    'Content-Type': 'application/json'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers });
  }

  try {
    // Parse the request body to get user data
    // @ts-ignore
    const userData = await req.json();
    const {
      email,
      firstName,
      lastName,
      companyName,
      password,
      city,
      state,
      country,
      phone,
      address,
      postalCode,
      role,
      company_size,
      referral_source
    } = userData;

    if (!email) {
      throw new Error('Email is required');
    }

    // Get GHL credentials from environment variables
    // For Agency access, we need the Agency Bearer Token and Company ID
    // @ts-ignore
    const agencyToken = Deno.env.get('GHL_AGENCY_TOKEN');
    // @ts-ignore
    const companyId = Deno.env.get('GHL_COMPANY_ID');

    if (!agencyToken) {
      throw new Error('GHL Agency Token not found in environment variables');
    }

    if (!companyId) {
      throw new Error('GHL_COMPANY_ID not found in environment variables');
    }

    console.log('Creating Stripe-first Free Plan account (no manual GHL creation)');

    // Get Stripe API key
    const stripeApiKey = Deno.env.get('STRIPE_SECRET_KEY');

    if (!stripeApiKey) {
      throw new Error('STRIPE_SECRET_KEY not found in environment variables');
    }

    let stripeCustomerId = null;
    let subscriptionCreated = false;

    // Step 1: Create Stripe customer
    console.log('Creating Stripe customer...');

    const stripeResponse = await fetch('https://api.stripe.com/v1/customers', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${stripeApiKey}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        name: `${firstName} ${lastName}`,
        email: email,
        description: `Free Plan customer for ${companyName || `${firstName}'s Business`}`,
        phone: phone || '',
        'metadata[company_name]': companyName || '',
        'metadata[role]': role || '',
        'metadata[company_size]': company_size || '',
        'metadata[referral_source]': referral_source || '',
        'metadata[signup_source]': 'aha_innovations_website'
      })
    });

    if (stripeResponse.ok) {
      const stripeData = await stripeResponse.json();
      stripeCustomerId = stripeData.id;
      console.log('Stripe customer created:', stripeCustomerId);
    } else {
      const errorText = await stripeResponse.text();
      throw new Error(`Failed to create Stripe customer: ${errorText}`);
    }

    // Step 2: Create Stripe subscription with Free Plan + 100% coupon
    // This will trigger Stripe-GHL integration to auto-create user + subaccount
    console.log('Creating Stripe subscription with Free Plan Improved (1 year)...');

    const subscriptionResponse = await fetch('https://api.stripe.com/v1/subscriptions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${stripeApiKey}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        customer: stripeCustomerId,
        'items[0][price]': 'price_1RbMOCL5UMPPQRhs2NiED6ml', // Free Plan Improved (1 year)
        coupon: 'BNyjT7Un', // 100% forever coupon
        'metadata[signup_source]': 'aha_innovations_website',
        'metadata[plan_type]': 'free_plan_improved',
        'metadata[user_name]': `${firstName} ${lastName}`,
        'metadata[user_email]': email,
        'metadata[company_name]': companyName || '',
        'metadata[role]': role || '',
        'metadata[company_size]': company_size || '',
        'metadata[referral_source]': referral_source || ''
      })
    });

    if (subscriptionResponse.ok) {
      const subscriptionResult = await subscriptionResponse.json();
      console.log('Stripe subscription created successfully:', subscriptionResult.id);
      subscriptionCreated = true;

      console.log('Stripe-GHL integration will automatically create:');
      console.log('1. GHL user account');
      console.log('2. GHL subaccount');
      console.log('3. Send activation email');
    } else {
      const errorText = await subscriptionResponse.text();
      throw new Error(`Failed to create Stripe subscription: ${errorText}`);
    }

    // Step 3: Create GHL Contact for automation triggering
    // Note: We'll use the default location since Stripe will create the actual subaccount
    let contactCreated = false;
    try {
      // Get location-level token for contact creation
      const locationToken = Deno.env.get('GHL_LOCATION_TOKEN');
      const defaultLocationId = Deno.env.get('GHL_DEFAULT_LOCATION_ID') || 'LL7TmGrkL72EOf8O0FKA'; // Your main location

      if (locationToken) {
        const contactResponse = await fetch(`${GHL_API_URL}/contacts/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${locationToken}`,
            'Version': '2021-07-28'
          },
          body: JSON.stringify({
            locationId: defaultLocationId,
            email: email,
            firstName: firstName,
            lastName: lastName,
            companyName: companyName || `${firstName}'s Business`,
            phone: phone || '',
            city: city || '',
            state: state || '',
            country: country || 'US',
            tags: ['website_signups'] // Tag for automation triggering
          })
        });

        if (contactResponse.ok) {
          const contactData = await contactResponse.json();
          console.log('GHL Contact created successfully for automation:', contactData.contact?.id);
          contactCreated = true;
        } else {
          const errorText = await contactResponse.text();
          console.warn('Contact creation failed (automation may not trigger):', errorText);
        }
      } else {
        console.warn('Location token not found - contact creation skipped');
      }
    } catch (error) {
      console.warn('Contact creation error (automation may not trigger):', error);
    }

    // Return response - Stripe will handle user/subaccount creation automatically
    return new Response(JSON.stringify({
      success: true,
      stripeCustomerId: stripeCustomerId,
      subscriptionCreated: subscriptionCreated,
      contactCreated: contactCreated,
      role: role || '',
      company_size: company_size || '',
      referral_source: referral_source || '',
      message: 'Free Plan subscription created successfully. Stripe will automatically create GHL user account, subaccount, and send activation email.',
      nextSteps: [
        'Check email for GHL account activation',
        'GHL user account and subaccount will be created automatically by Stripe integration',
        'Contact created for automation triggering'
      ]
    }), {
      status: 200,
      headers
    });
  } catch (error) {
    console.error('Error creating GHL account:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers
    });
  }
});
