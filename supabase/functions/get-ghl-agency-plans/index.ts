// Setup type definitions for built-in Supabase Runtime APIs
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Agency plans are company-level, so we don't need locationId
    console.log('Fetching agency plans - no location ID needed');

    // GHL API configuration
    const GHL_API_URL = 'https://services.leadconnectorhq.com';
    const accessToken = Deno.env.get('GHL_PRIVATE_INTEGRATION_TOKEN') || 'pit-d6b26228-e407-46ff-b13b-ecb4fd6d1237';
    const companyId = Deno.env.get('GHL_COMPANY_ID') || 'gPkTndcx94O3r573TOMx';

    if (!accessToken) {
      return new Response(
        JSON.stringify({ error: 'GHL access token not configured' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // For now, let's fetch a specific plan that we know exists
    const planId = 'price_1RbMOCL5UMPPQRhs1AFQl4GH'; // Your plan ID

    console.log('Fetching SaaS plan for company:', companyId, 'planId:', planId);
    console.log('Request details:', {
      url: `${GHL_API_URL}/saas-api/public-api/saas-plan/${planId}?companyId=${companyId}`,
      hasToken: !!accessToken,
      tokenPrefix: accessToken ? accessToken.substring(0, 10) + '...' : 'none',
      companyId: companyId,
      planId: planId
    });

    // First, let's try to test if the token works with a simpler endpoint
    console.log('Testing token with a simple endpoint first...');
    try {
      const testResponse = await fetch(`${GHL_API_URL}/locations/`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Version': '2021-07-28',
          'Content-Type': 'application/json'
        }
      });
      console.log('Token test response status:', testResponse.status);
      if (!testResponse.ok) {
        console.log('Token test failed:', await testResponse.text());
      }
    } catch (tokenTestError) {
      console.log('Token test error:', tokenTestError);
    }

    // Fetch agency plans with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const saasPlansResponse = await fetch(`${GHL_API_URL}/saas-api/public-api/saas-plan/${planId}?companyId=${companyId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Version': '2021-04-15',
          'Content-Type': 'application/json'
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!saasPlansResponse.ok) {
      const errorText = await saasPlansResponse.text();
      console.error('Failed to fetch SaaS plan:', {
        status: saasPlansResponse.status,
        statusText: saasPlansResponse.statusText,
        error: errorText,
        url: `${GHL_API_URL}/saas-api/public-api/saas-plan/${planId}?companyId=${companyId}`,
        headers: saasPlansResponse.headers
      });
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch SaaS plan',
          status: saasPlansResponse.status,
          statusText: saasPlansResponse.statusText,
          details: errorText,
          url: `${GHL_API_URL}/saas-api/public-api/saas-plan/${planId}?companyId=${companyId}`
        }),
        {
          status: saasPlansResponse.status,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
      }

      const planData = await saasPlansResponse.json();
      console.log('SaaS plan retrieved:', planData);

      // Convert single plan to array format for the frontend
      const plans = planData ? [planData] : [];

      return new Response(
        JSON.stringify({
          success: true,
          plans: plans,
          companyId: companyId
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );

    } catch (fetchError) {
      clearTimeout(timeoutId);
      console.error('Fetch error:', fetchError);

      if (fetchError.name === 'AbortError') {
        return new Response(
          JSON.stringify({
            error: 'Request timeout',
            message: 'The GHL API request timed out after 10 seconds'
          }),
          {
            status: 408,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
      }

      return new Response(
        JSON.stringify({
          error: 'Fetch failed',
          message: fetchError.message
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

  } catch (error) {
    console.error('Error in get-ghl-agency-plans function:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
