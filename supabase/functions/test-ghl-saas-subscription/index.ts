import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { corsHeaders } from '../_shared/cors.ts'

const GHL_API_URL = 'https://services.leadconnectorhq.com';

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { locationId, planId, email, firstName, lastName } = await req.json()

    if (!locationId) {
      return new Response(
        JSON.stringify({ error: 'locationId is required' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    if (!planId) {
      return new Response(
        JSON.stringify({ error: 'Plan ID is required' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const accessToken = Deno.env.get('GHL_AGENCY_TOKEN') || Deno.env.get('GHL_PRIVATE_INTEGRATION_TOKEN');
    const companyId = Deno.env.get('GHL_COMPANY_ID') || 'gPkTndcx94O3r573TOMx';

    console.log('Testing SaaS subscription for location:', locationId);

    // Step 1: Get current subscription details for the location to extract real customer ID
    let stripeCustomerId = null;
    let subscriptionId = null;
    let currentSubscriptionDetails = null;

    console.log('Fetching current subscription details for location:', locationId);

    try {
      const subscriptionDetailsResponse = await fetch(`${GHL_API_URL}/saas-api/public-api/get-saas-subscription/${locationId}?companyId=${companyId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Version': '2021-04-15',
          'Content-Type': 'application/json'
        }
      });

      if (subscriptionDetailsResponse.ok) {
        currentSubscriptionDetails = await subscriptionDetailsResponse.json();
        console.log('Current subscription details:', currentSubscriptionDetails);

        // Extract the real customer ID and subscription ID
        stripeCustomerId = currentSubscriptionDetails.customerId;
        subscriptionId = currentSubscriptionDetails.subscriptionId;

        console.log('Extracted real customer ID:', stripeCustomerId);
        console.log('Extracted subscription ID:', subscriptionId);
        console.log('Location SaaS mode:', currentSubscriptionDetails.saasMode);
        console.log('Current subscription status:', currentSubscriptionDetails.subscriptionStatus);
      } else {
        const errorText = await subscriptionDetailsResponse.text();
        console.warn('Failed to get subscription details:', errorText);
        currentSubscriptionDetails = { error: errorText };
      }
    } catch (error) {
      console.warn('Error fetching subscription details:', error);
      currentSubscriptionDetails = { error: error.message };
    }

    // Step 2: Get Agency Plans
    let agencyPlans = null;
    try {
      const agencyPlansResponse = await fetch(`${GHL_API_URL}/saas-api/public-api/agency-plans/${companyId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Version': '2021-04-15',
          'channel': 'OAUTH',
          'source': 'INTEGRATION'
        }
      });

      if (agencyPlansResponse.ok) {
        agencyPlans = await agencyPlansResponse.json();
        console.log('Agency plans retrieved:', agencyPlans);
      } else {
        console.warn('Failed to fetch agency plans:', await agencyPlansResponse.text());
      }
    } catch (error) {
      console.warn('Agency plans error:', error);
    }

    // Step 3: Check current subscription status
    let currentSubscription = null;
    try {
      const subscriptionResponse = await fetch(`${GHL_API_URL}/saas-api/public-api/get-saas-subscription/${locationId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Version': '2021-04-15',
          'channel': 'OAUTH',
          'source': 'INTEGRATION'
        }
      });

      if (subscriptionResponse.ok) {
        currentSubscription = await subscriptionResponse.json();
        console.log('Current subscription:', currentSubscription);
      } else {
        console.warn('Failed to get current subscription:', await subscriptionResponse.text());
      }
    } catch (error) {
      console.warn('Current subscription error:', error);
    }

    // Step 4: Enable SaaS mode if location is in setup_pending
    let saasEnabled = false;
    if (currentSubscriptionDetails?.saasMode === 'setup_pending' && stripeCustomerId) {
      console.log('Location is in setup_pending mode, enabling SaaS...');

      try {
        const enableSaasResponse = await fetch(`${GHL_API_URL}/saas-api/public-api/enable-saas/${locationId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`,
            'Version': '2021-04-15',
            'channel': 'OAUTH',
            'source': 'INTEGRATION'
          },
          body: JSON.stringify({
            stripeAccountId: Deno.env.get('STRIPE_ACCOUNT_ID') || 'acct_1OZI7UL5UMPPQRhs',
            name: `Test User`,
            email: email || '<EMAIL>',
            stripeCustomerId: stripeCustomerId,
            companyId: companyId
          })
        });

        if (enableSaasResponse.ok) {
          console.log('SaaS enabled for location:', locationId);
          saasEnabled = true;
        } else {
          const errorText = await enableSaasResponse.text();
          console.warn('SaaS enablement failed:', errorText);
        }
      } catch (error) {
        console.warn('SaaS enablement error:', error);
      }
    } else {
      console.log('Location SaaS mode:', currentSubscriptionDetails?.saasMode);
      saasEnabled = currentSubscriptionDetails?.saasMode !== 'setup_pending';
    }

    // Step 5: Try to attach subscription if we have everything needed
    let subscriptionResult = null;
    if (stripeCustomerId && planId && saasEnabled) {
      try {
        // Find the selected plan to get its details
        const selectedPlan = agencyPlans?.data?.find(plan =>
          plan.prices?.some(price => price.id === planId)
        );

        console.log('Attaching plan:', selectedPlan || { planId: planId });
        console.log('Using real customer ID:', stripeCustomerId);
        console.log('Using subscription ID:', subscriptionId || 'none (new subscription)');

        const subscriptionResponse = await fetch(`${GHL_API_URL}/saas-api/public-api/update-saas-subscription/${locationId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`,
            'Version': '2021-04-15',
            'channel': 'OAUTH',
            'source': 'INTEGRATION'
          },
          body: JSON.stringify({
            subscriptionId: subscriptionId || planId, // Use real subscriptionId if available, otherwise planId
            customerId: stripeCustomerId,
            companyId: companyId
          })
        });

        if (subscriptionResponse.ok) {
            subscriptionResult = await subscriptionResponse.json();
            console.log('Subscription attached successfully:', subscriptionResult);
          } else {
            const errorText = await subscriptionResponse.text();
            console.warn('Failed to attach subscription:', errorText);
            subscriptionResult = { error: errorText };
          }
      } catch (error) {
        console.warn('Subscription attachment error:', error);
        subscriptionResult = { error: error.message };
      }
    } else {
      console.log('Missing requirements for subscription attachment:', {
        hasStripeCustomer: !!stripeCustomerId,
        hasPlanId: !!planId
      });
    }

    return new Response(
      JSON.stringify({
        success: true,
        locationId,
        planId,
        stripeCustomerId,
        subscriptionId,
        saasEnabled,
        agencyPlans,
        currentSubscriptionDetails,
        subscriptionResult,
        message: 'SaaS subscription test completed with SaaS enablement and real customer data'
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Test function error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
