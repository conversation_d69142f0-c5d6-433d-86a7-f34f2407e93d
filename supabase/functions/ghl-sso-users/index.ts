// Supabase Edge Function for GHL SSO User Management
// This function handles creating, searching, updating, and managing GHL users via the SSO Users API
// Based on the GHL SSO Users API specification

import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// GHL API base URL
const GHL_API_URL = 'https://services.leadconnectorhq.com';

// Interface for GHL User creation
interface CreateGHLUserRequest {
  companyId: string;
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone?: string;
  type: 'account' | 'agency';
  role: 'admin' | 'user';
  locationIds: string[];
  externalUserId: string; // This is crucial for SSO integration
  permissions?: any;
  scopes?: string[];
}

// Interface for user search
interface SearchGHLUserRequest {
  companyId: string;
  query?: string;
  email?: string;
  externalUserId?: string;
  locationId?: string;
  limit?: number;
  skip?: number;
}

serve(async (req: Request) => {
  // CORS headers - Allow both local development and production domains
  const origin = req.headers.get('origin') || '';
  const allowedOrigins = ['http://localhost:8080', 'https://app.aha-innovations.com'];
  const corsOrigin = allowedOrigins.includes(origin) ? origin : allowedOrigins[0];

  const headers = {
    'Access-Control-Allow-Origin': corsOrigin,
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS, PUT, DELETE',
    'Access-Control-Allow-Credentials': 'true',
    'Content-Type': 'application/json'
  };

  // Handle OPTIONS request for CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers });
  }

  try {
    // Get GHL credentials from environment variables
    // @ts-ignore
    const agencyToken = Deno.env.get('GHL_AGENCY_TOKEN');
    // @ts-ignore
    const companyId = Deno.env.get('GHL_COMPANY_ID');

    if (!agencyToken || !companyId) {
      throw new Error('GHL credentials not found in environment variables');
    }

    // Parse request body and URL
    const url = new URL(req.url);
    const action = url.searchParams.get('action') || 'create';
    const userId = url.searchParams.get('userId');

    let requestData: any = {};
    if (req.method === 'POST' || req.method === 'PUT') {
      requestData = await req.json();
    }

    // Common headers for GHL API requests
    const ghlHeaders = {
      'Authorization': `Bearer ${agencyToken}`,
      'Version': '2021-07-28',
      'Content-Type': 'application/json'
    };

    switch (action) {
      case 'create':
        return await createUser(requestData, ghlHeaders, companyId, headers);
      
      case 'search':
        return await searchUsers(requestData, ghlHeaders, companyId, headers);
      
      case 'get':
        if (!userId) {
          throw new Error('userId is required for get action');
        }
        return await getUser(userId, ghlHeaders, headers);
      
      case 'update':
        if (!userId) {
          throw new Error('userId is required for update action');
        }
        return await updateUser(userId, requestData, ghlHeaders, headers);
      
      case 'delete':
        if (!userId) {
          throw new Error('userId is required for delete action');
        }
        return await deleteUser(userId, ghlHeaders, headers);
      
      default:
        throw new Error(`Unknown action: ${action}`);
    }

  } catch (error) {
    console.error('Error in GHL SSO Users function:', error);
    return new Response(JSON.stringify({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers
    });
  }
});

// Create a new GHL user for SSO
async function createUser(
  userData: CreateGHLUserRequest, 
  ghlHeaders: any, 
  companyId: string, 
  responseHeaders: any
) {
  console.log('Creating GHL user for SSO:', userData);

  // Validate required fields
  const requiredFields = ['firstName', 'lastName', 'email', 'externalUserId', 'locationIds'];
  for (const field of requiredFields) {
    if (!userData[field]) {
      throw new Error(`Missing required field: ${field}`);
    }
  }

  // Set defaults for SSO user creation
  const createUserPayload = {
    companyId: companyId,
    firstName: userData.firstName,
    lastName: userData.lastName,
    email: userData.email,
    password: userData.password || generateRandomPassword(),
    phone: userData.phone || '',
    type: userData.type || 'account',
    role: userData.role || 'admin',
    locationIds: userData.locationIds,
    externalUserId: userData.externalUserId, // Critical for SSO
    permissions: userData.permissions || getDefaultPermissions(),
    scopes: userData.scopes || getDefaultScopes()
  };

  console.log('GHL Create User payload:', createUserPayload);

  const response = await fetch(`${GHL_API_URL}/users/`, {
    method: 'POST',
    headers: ghlHeaders,
    body: JSON.stringify(createUserPayload)
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('Failed to create GHL user:', errorText);
    throw new Error(`Failed to create GHL user: ${errorText}`);
  }

  const result = await response.json();
  console.log('GHL user created successfully:', result);

  return new Response(JSON.stringify({
    success: true,
    user: result,
    externalUserId: userData.externalUserId,
    message: 'GHL user created successfully for SSO'
  }), {
    status: 201,
    headers: responseHeaders
  });
}

// Search for existing GHL users
async function searchUsers(
  searchData: SearchGHLUserRequest,
  ghlHeaders: any,
  companyId: string,
  responseHeaders: any
) {
  console.log('Searching GHL users:', searchData);

  // Build query parameters
  const params = new URLSearchParams({
    companyId: companyId,
    limit: (searchData.limit || 25).toString(),
    skip: (searchData.skip || 0).toString()
  });

  if (searchData.query) params.append('query', searchData.query);
  if (searchData.locationId) params.append('locationId', searchData.locationId);

  const response = await fetch(`${GHL_API_URL}/users/search?${params.toString()}`, {
    method: 'GET',
    headers: ghlHeaders
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('Failed to search GHL users:', errorText);
    throw new Error(`Failed to search GHL users: ${errorText}`);
  }

  const result = await response.json();
  
  // If searching by email or externalUserId, filter results
  let filteredUsers = result.users || [];
  
  if (searchData.email) {
    filteredUsers = filteredUsers.filter((user: any) => 
      user.email?.toLowerCase() === searchData.email?.toLowerCase()
    );
  }
  
  if (searchData.externalUserId) {
    filteredUsers = filteredUsers.filter((user: any) => 
      user.externalUserId === searchData.externalUserId
    );
  }

  return new Response(JSON.stringify({
    success: true,
    users: filteredUsers,
    count: filteredUsers.length,
    totalCount: result.count || 0
  }), {
    status: 200,
    headers: responseHeaders
  });
}

// Get a specific user by ID
async function getUser(userId: string, ghlHeaders: any, responseHeaders: any) {
  console.log('Getting GHL user:', userId);

  const response = await fetch(`${GHL_API_URL}/users/${userId}`, {
    method: 'GET',
    headers: ghlHeaders
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('Failed to get GHL user:', errorText);
    throw new Error(`Failed to get GHL user: ${errorText}`);
  }

  const result = await response.json();

  return new Response(JSON.stringify({
    success: true,
    user: result
  }), {
    status: 200,
    headers: responseHeaders
  });
}

// Update a user
async function updateUser(userId: string, updateData: any, ghlHeaders: any, responseHeaders: any) {
  console.log('Updating GHL user:', userId, updateData);

  const response = await fetch(`${GHL_API_URL}/users/${userId}`, {
    method: 'PUT',
    headers: ghlHeaders,
    body: JSON.stringify(updateData)
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('Failed to update GHL user:', errorText);
    throw new Error(`Failed to update GHL user: ${errorText}`);
  }

  const result = await response.json();

  return new Response(JSON.stringify({
    success: true,
    user: result
  }), {
    status: 200,
    headers: responseHeaders
  });
}

// Delete a user
async function deleteUser(userId: string, ghlHeaders: any, responseHeaders: any) {
  console.log('Deleting GHL user:', userId);

  const response = await fetch(`${GHL_API_URL}/users/${userId}`, {
    method: 'DELETE',
    headers: ghlHeaders
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('Failed to delete GHL user:', errorText);
    throw new Error(`Failed to delete GHL user: ${errorText}`);
  }

  const result = await response.json();

  return new Response(JSON.stringify({
    success: true,
    result: result
  }), {
    status: 200,
    headers: responseHeaders
  });
}

// Helper function to generate a random password
function generateRandomPassword(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

// Default permissions for new users
function getDefaultPermissions() {
  return {
    campaignsEnabled: true,
    contactsEnabled: true,
    workflowsEnabled: true,
    funnelsEnabled: true,
    appointmentsEnabled: true,
    conversationsEnabled: true,
    dashboardStatsEnabled: true,
    settingsEnabled: true,
    marketingEnabled: true
  };
}

// Default scopes for new users
function getDefaultScopes(): string[] {
  return [
    'contacts.write',
    'campaigns.write',
    'workflows.write',
    'funnels.write',
    'conversations.write',
    'dashboard/stats.readonly',
    'settings.write'
  ];
}
