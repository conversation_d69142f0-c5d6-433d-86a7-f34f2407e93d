import { corsHeaders } from '../_shared/cors.ts'

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { 
      firstName, 
      lastName, 
      email, 
      phone, 
      companyName, 
      role, 
      company_size, 
      referral_source 
    } = await req.json()

    console.log('Creating Stripe-only Free Plan account:', { firstName, lastName, email, companyName })

    // Validate required fields
    if (!firstName || !lastName || !email) {
      return new Response(JSON.stringify({ error: 'Missing required fields: firstName, lastName, email' }), {
        status: 400,
        headers: corsHeaders
      })
    }

    const stripeApiKey = Deno.env.get('STRIPE_SECRET_KEY')
    
    if (!stripeApiKey) {
      return new Response(JSON.stringify({ error: 'Missing Stripe configuration' }), {
        status: 500,
        headers: corsHeaders
      })
    }

    let stripeCustomerId = null
    let subscriptionCreated = false

    // Step 1: Create Stripe customer
    console.log('Creating Stripe customer...')
    
    const stripeResponse = await fetch('https://api.stripe.com/v1/customers', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${stripeApiKey}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        name: `${firstName} ${lastName}`,
        email: email,
        description: `Free Plan customer for ${companyName || `${firstName}'s Business`}`,
        phone: phone || '',
        'metadata[company_name]': companyName || '',
        'metadata[role]': role || '',
        'metadata[company_size]': company_size || '',
        'metadata[referral_source]': referral_source || '',
        'metadata[signup_source]': 'aha_innovations_website'
      })
    })

    if (stripeResponse.ok) {
      const stripeData = await stripeResponse.json()
      stripeCustomerId = stripeData.id
      console.log('Stripe customer created:', stripeCustomerId)
    } else {
      const errorText = await stripeResponse.text()
      console.error('Stripe customer creation failed:', errorText)
      return new Response(JSON.stringify({ error: 'Failed to create Stripe customer', details: errorText }), {
        status: 500,
        headers: corsHeaders
      })
    }

    // Step 2: Create Stripe subscription with Free Plan + 100% coupon
    // This will trigger the Stripe-GHL integration to auto-create user + subaccount
    console.log('Creating Stripe subscription with Free Plan Improved (1 year)...')
    
    const subscriptionResponse = await fetch('https://api.stripe.com/v1/subscriptions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${stripeApiKey}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        customer: stripeCustomerId,
        'items[0][price]': 'price_1RbMOCL5UMPPQRhs2NiED6ml', // Free Plan Improved (1 year)
        coupon: 'BNyjT7Un', // 100% forever coupon
        'metadata[signup_source]': 'aha_innovations_website',
        'metadata[plan_type]': 'free_plan_improved',
        'metadata[user_name]': `${firstName} ${lastName}`,
        'metadata[user_email]': email,
        'metadata[company_name]': companyName || '',
        'metadata[role]': role || '',
        'metadata[company_size]': company_size || '',
        'metadata[referral_source]': referral_source || ''
      })
    })

    if (subscriptionResponse.ok) {
      const subscriptionResult = await subscriptionResponse.json()
      console.log('Stripe subscription created successfully:', subscriptionResult.id)
      subscriptionCreated = true
      
      console.log('Stripe-GHL integration should now automatically create:')
      console.log('1. GHL user account')
      console.log('2. GHL subaccount') 
      console.log('3. Send activation email')
      console.log('Subscription ID:', subscriptionResult.id)
      
      return new Response(JSON.stringify({
        success: true,
        stripeCustomerId: stripeCustomerId,
        subscriptionId: subscriptionResult.id,
        subscriptionCreated: true,
        message: 'Free Plan subscription created successfully. Stripe will automatically create GHL account and send activation email.',
        nextSteps: [
          'Check email for GHL account activation',
          'GHL user account and subaccount will be created automatically',
          'No further action needed'
        ]
      }), {
        status: 200,
        headers: corsHeaders
      })
      
    } else {
      const errorText = await subscriptionResponse.text()
      console.error('Stripe subscription creation failed:', errorText)
      
      return new Response(JSON.stringify({ 
        error: 'Failed to create Stripe subscription', 
        details: errorText,
        stripeCustomerId: stripeCustomerId // Customer was created successfully
      }), {
        status: 500,
        headers: corsHeaders
      })
    }

  } catch (error) {
    console.error('Error creating Stripe Free Plan account:', error)
    return new Response(JSON.stringify({ 
      error: error.message,
      details: 'Check the logs for more information'
    }), {
      status: 500,
      headers: corsHeaders
    })
  }
})
