import { corsHeaders } from '../_shared/cors.ts'

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { 
      firstName, 
      lastName, 
      email, 
      priceId,
      companyName
    } = await req.json()

    console.log('Creating Stripe subscription:', { firstName, lastName, email, priceId, companyName })

    // Validate required fields
    if (!firstName || !lastName || !email || !priceId) {
      return new Response(JSON.stringify({ error: 'Missing required fields: firstName, lastName, email, priceId' }), {
        status: 400,
        headers: corsHeaders
      })
    }

    const stripeApiKey = Deno.env.get('STRIPE_SECRET_KEY')
    
    if (!stripeApiKey) {
      return new Response(JSON.stringify({ error: 'Missing Stripe configuration' }), {
        status: 500,
        headers: corsHeaders
      })
    }

    // Validate price belongs to allowed products
    const ALLOWED_PRODUCTS = {
      'prod_RA7Wr64BcGkiFa': 'basic_plan',
      'prod_R9llZ9zoxgWOgE': 'agency_plan', 
      'prod_ScxhXd1F2zBsHw': 'enterprise_plan',
      'prod_SWPC9unYpg0Hc2': 'free_plan'
    }

    // Step 1: Fetch price details from Stripe to validate and get trial info
    console.log('Fetching price details from Stripe...')
    
    const priceResponse = await fetch(`https://api.stripe.com/v1/prices/${priceId}`, {
      headers: { 'Authorization': `Bearer ${stripeApiKey}` }
    })

    if (!priceResponse.ok) {
      return new Response(JSON.stringify({ error: 'Invalid price ID' }), {
        status: 400,
        headers: corsHeaders
      })
    }

    const priceData = await priceResponse.json()
    const productId = priceData.product
    
    // Validate product is allowed
    if (!ALLOWED_PRODUCTS[productId]) {
      return new Response(JSON.stringify({ error: 'Product not allowed' }), {
        status: 400,
        headers: corsHeaders
      })
    }

    const planType = ALLOWED_PRODUCTS[productId]
    const isActualFreePlan = priceId === 'price_1RbMOCL5UMPPQRhs2NiED6ml'

    // For seamless trial flow, we'll create all plans as "free trials" initially
    // GHL will handle the trial period and automatic pausing/upgrading
    const trialPriceId = 'price_1RbMOCL5UMPPQRhs2NiED6ml' // Free Plan improved
    const originalPriceId = priceId // Store original for metadata

    console.log('Plan details:', { planType, isActualFreePlan, productId, originalPriceId, trialPriceId })

    // Step 2: Create Stripe customer
    console.log('Creating Stripe customer...')
    
    const customerResponse = await fetch('https://api.stripe.com/v1/customers', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${stripeApiKey}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        name: `${firstName} ${lastName}`,
        email: email,
        description: `${planType} customer for ${companyName || `${firstName}'s Business`}`,
        'metadata[plan_type]': planType,
        'metadata[company_name]': companyName || '',
        'metadata[signup_source]': 'aha_innovations_website'
      })
    })

    if (!customerResponse.ok) {
      const errorText = await customerResponse.text()
      return new Response(JSON.stringify({ error: 'Failed to create Stripe customer', details: errorText }), {
        status: 500,
        headers: corsHeaders
      })
    }

    const customerData = await customerResponse.json()
    const stripeCustomerId = customerData.id
    console.log('Stripe customer created:', stripeCustomerId)

    // Step 3: Create Stripe subscription (different logic for free vs paid)
    console.log('Creating Stripe subscription...')
    
    let subscriptionParams = {
      customer: stripeCustomerId,
      'items[0][price]': priceId,
      'metadata[signup_source]': 'aha_innovations_website',
      'metadata[plan_type]': planType,
      'metadata[user_name]': `${firstName} ${lastName}`,
      'metadata[user_email]': email,
      'metadata[company_name]': companyName || ''
    }

    if (isFreePlan) {
      // Free Plan: Use 100% coupon
      subscriptionParams.coupon = 'BNyjT7Un'
      console.log('Using 100% coupon for free plan')
    } else {
      // Paid Plans: Use trial period (no payment method required during trial)
      console.log('Using trial period for paid plan')
      // Note: trial_period_days will be automatically applied from the price if configured in Stripe
    }

    const subscriptionResponse = await fetch('https://api.stripe.com/v1/subscriptions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${stripeApiKey}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams(subscriptionParams)
    })

    if (!subscriptionResponse.ok) {
      const errorText = await subscriptionResponse.text()
      return new Response(JSON.stringify({ 
        error: 'Failed to create Stripe subscription', 
        details: errorText,
        stripeCustomerId: stripeCustomerId
      }), {
        status: 500,
        headers: corsHeaders
      })
    }

    const subscriptionResult = await subscriptionResponse.json()
    console.log('Stripe subscription created successfully:', subscriptionResult.id)

    // Step 4: Create GHL contact for automation (using existing location)
    let contactCreated = false
    try {
      const GHL_API_URL = 'https://services.leadconnectorhq.com'
      const locationToken = Deno.env.get('GHL_LOCATION_TOKEN')
      const defaultLocationId = Deno.env.get('GHL_DEFAULT_LOCATION_ID') || 'LL7TmGrkL72EOf8O0FKA'

      if (locationToken) {
        const contactResponse = await fetch(`${GHL_API_URL}/contacts/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${locationToken}`,
            'Version': '2021-07-28'
          },
          body: JSON.stringify({
            locationId: defaultLocationId,
            email: email,
            firstName: firstName,
            lastName: lastName,
            companyName: companyName || `${firstName}'s Business`,
            tags: ['website_signups', planType] // Tag with plan type for automation
          })
        })

        if (contactResponse.ok) {
          const contactData = await contactResponse.json()
          console.log('GHL Contact created for automation:', contactData.contact?.id)
          contactCreated = true
        } else {
          console.warn('Contact creation failed:', await contactResponse.text())
        }
      }
    } catch (error) {
      console.warn('Contact creation error:', error)
    }

    return new Response(JSON.stringify({
      success: true,
      stripeCustomerId: stripeCustomerId,
      subscriptionId: subscriptionResult.id,
      planType: planType,
      isFreePlan: isFreePlan,
      contactCreated: contactCreated,
      message: `${planType} subscription created successfully. Stripe will automatically create GHL account and send activation email.`,
      nextSteps: [
        'Check email for GHL account activation',
        'GHL user account and subaccount will be created automatically',
        'No further action needed'
      ]
    }), {
      status: 200,
      headers: corsHeaders
    })

  } catch (error) {
    console.error('Error creating subscription:', error)
    return new Response(JSON.stringify({ 
      error: error.message,
      details: 'Check the logs for more information'
    }), {
      status: 500,
      headers: corsHeaders
    })
  }
})
