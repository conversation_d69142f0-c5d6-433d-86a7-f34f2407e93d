// Existing chat widget loader
(function() {
    var script = document.createElement('script');
    script.src = 'https://widgets.leadconnectorhq.com/loader.js';
    script.setAttribute('data-resources-url', 'https://widgets.leadconnectorhq.com/chat-widget/loader.js');
    script.setAttribute('data-widget-id', '67fd2e2c30666b387227ccb7');
    document.head.appendChild(script);
})();

// Add "Sign up here" button to login page
function addSignupButton() {
    console.log('=== Attempting to add signup button ===');

    // Check if button already exists
    if (document.querySelector('.custom-signup-link')) {
        console.log('Signup button already exists');
        return;
    }

    // Try multiple selectors to find the login form - updated for new CSS structure
    const selectors = [
        '.hl_login--body .card .card-body',
        '.hl_login--body .card-body',
        '.hl_login .card-body',
        '.card-body',
        'form',
        '.hl_login form',
        '.hl_login .card',
        '.hl_login--body',
        '[class*="login"] form',
        '[class*="card-body"]'
    ];

    let targetElement = null;

    for (let selector of selectors) {
        targetElement = document.querySelector(selector);
        if (targetElement) {
            console.log(`Found target element with selector: ${selector}`);
            break;
        }
    }

    if (!targetElement) {
        console.log('No suitable target element found. Available elements:');
        console.log('All elements with "login" in class:', document.querySelectorAll('[class*="login"]'));
        console.log('All elements with "card" in class:', document.querySelectorAll('[class*="card"]'));
        console.log('All form elements:', document.querySelectorAll('form'));
        return;
    }

    console.log('Target element found:', targetElement);

    // Create the signup link container with improved styling
    const signupContainer = document.createElement('div');
    signupContainer.className = 'custom-signup-link';
    signupContainer.style.cssText = `
        text-align: center !important;
        margin-top: 32px !important;
        padding-top: 24px !important;
        border-top: 1px solid rgba(255, 255, 255, 0.15) !important;
        position: relative !important;
        z-index: 1000 !important;
    `;

    // Create the signup text and link
    const signupText = document.createElement('div');
    signupText.style.cssText = `
        color: rgba(156, 163, 175, 1) !important;
        font-size: 0.875rem !important;
        margin-bottom: 8px !important;
    `;
    signupText.textContent = 'No account yet?';

    const signupLink = document.createElement('a');
    signupLink.href = 'https://easy-ghl-launchpad.vercel.app/signup';
    signupLink.textContent = 'Sign up here';
    signupLink.target = '_blank';
    signupLink.style.cssText = `
        color: #EA384C !important;
        text-decoration: none !important;
        font-size: 0.875rem !important;
        font-weight: 600 !important;
        display: inline-block !important;
        padding: 8px 16px !important;
        border-radius: 6px !important;
        transition: all 0.3s ease !important;
        background: transparent !important;
        border: 1px solid transparent !important;
        cursor: pointer !important;
        position: relative !important;
        z-index: 1001 !important;
    `;

    // Add hover effect
    signupLink.addEventListener('mouseenter', function() {
        this.style.background = 'rgba(234, 56, 76, 0.1) !important';
        this.style.borderColor = '#EA384C !important';
        this.style.transform = 'translateY(-1px) !important';
    });

    signupLink.addEventListener('mouseleave', function() {
        this.style.background = 'transparent !important';
        this.style.borderColor = 'transparent !important';
        this.style.transform = 'translateY(0) !important';
    });

    // Add click handler to ensure it works
    signupLink.addEventListener('click', function(e) {
        console.log('Signup link clicked!');
        // Let the default behavior handle the navigation
    });

    signupContainer.appendChild(signupText);
    signupContainer.appendChild(signupLink);
    targetElement.appendChild(signupContainer);

    console.log('Signup button added successfully to:', targetElement);
}

// More aggressive approach - try every 500ms for 10 seconds
let attempts = 0;
const maxAttempts = 20; // 10 seconds

function tryAddButton() {
    attempts++;
    console.log(`Attempt ${attempts}/${maxAttempts} to add signup button`);

    addSignupButton();

    if (attempts < maxAttempts && !document.querySelector('.custom-signup-link')) {
        setTimeout(tryAddButton, 500);
    } else if (attempts >= maxAttempts) {
        console.log('Max attempts reached. Final DOM state:');
        console.log('Body innerHTML:', document.body.innerHTML);
    }
}

// Start immediately
tryAddButton();

// Also try on standard events
document.addEventListener('DOMContentLoaded', tryAddButton);
window.addEventListener('load', tryAddButton);

// Watch for DOM changes with throttling
let observerTimeout;
const observer = new MutationObserver(function(mutations) {
    clearTimeout(observerTimeout);
    observerTimeout = setTimeout(function() {
        if (!document.querySelector('.custom-signup-link')) {
            addSignupButton();
        }
    }, 100);
});

// Start observing immediately
observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeOldValue: true
});

// Alternative approach: Try to inject directly into any form
function injectIntoAnyForm() {
    const forms = document.querySelectorAll('form');
    console.log(`Found ${forms.length} forms on page`);

    forms.forEach((form, index) => {
        console.log(`Form ${index}:`, form);
        if (!form.querySelector('.custom-signup-link')) {
            // Try to add to this form
            const signupContainer = document.createElement('div');
            signupContainer.className = 'custom-signup-link';
            signupContainer.innerHTML = `
                <div style="text-align: center; margin-top: 25px; padding-top: 20px; border-top: 1px solid rgba(255, 255, 255, 0.2);">
                    <a href="https://easy-ghl-launchpad.vercel.app/signup" target="_blank"
                       style="color: #fff; text-decoration: none; font-size: 15px; font-weight: 500;
                              display: inline-block; padding: 12px 24px; border: 2px solid #fff;
                              border-radius: 8px; background: rgba(255, 255, 255, 0.1);">
                        No account yet? Sign up here
                    </a>
                </div>
            `;
            form.appendChild(signupContainer);
            console.log('Added signup button to form', index);
        }
    });
}

// Try form injection after delays
setTimeout(injectIntoAnyForm, 2000);
setTimeout(injectIntoAnyForm, 5000);
