
-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE,
  first_name TEXT,
  last_name TEXT,
  company_name TEXT,
  ghl_location_id TEXT,
  ghl_user_id TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  PRIMARY KEY (id)
);

-- Create function to handle new user signup
CREATE OR REPLACE FUNCTION handle_new_user_ghl() 
RETURNS TRIGGER AS $$
BEGIN
  -- Call Edge Function to create GHL accounts
  SELECT net.http_post(
    url:='https://fpratwslcktwpzlbzlhm.supabase.co/functions/v1/create-ghl-full-account',
    headers:='{"Content-Type": "application/json", "Authorization": "Bearer ' || current_setting('request.jwt.claims', true)::json->>'role' || '"}',
    body:=json_build_object(
      'email', NEW.email,
      'firstName', NEW.raw_user_meta_data->>'first_name',
      'lastName', NEW.raw_user_meta_data->>'last_name',
      'companyName', NEW.raw_user_meta_data->>'company_name',
      'password', NEW.encrypted_password -- Note: This is already hashed
    )::text
  ) INTO NEW.ghl_data;
  
  -- Create profile entry
  INSERT INTO public.profiles (id, first_name, last_name, company_name)
  VALUES (
    NEW.id,
    NEW.raw_user_meta_data->>'first_name',
    NEW.raw_user_meta_data->>'last_name',
    NEW.raw_user_meta_data->>'company_name'
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user_ghl();

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policy for users to view and update their own profile
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
CREATE POLICY "Users can view own profile"
  ON profiles FOR SELECT
  USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
CREATE POLICY "Users can update own profile"
  ON profiles FOR UPDATE
  USING (auth.uid() = id);
