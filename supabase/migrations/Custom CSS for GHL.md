/* menu reorder */
#inline-FvHf3pIEy3Iq7WwZWhtN body{
  background: #1C212E;
}

.hl_nav-header > nav {
	display: grid;
}
.hl_nav-header > nav > a#sb_dashboard {
	order: 1;
}
.hl_nav-header > nav > a[meta="reputation"] {
	order: 2;
}
.hl_nav-header > nav > a[meta="reporting"] {
	order: 3;
}
.hl_nav-header > nav > a[meta="launchpad"] {
	order: 4;
}
.hl_nav-header > nav > a[meta="automation"] {
	order: 5;
}
.hl_nav-header > nav > a[meta="email-marketing"] {
	order: 6;
}
.hl_nav-header > nav > a[meta="sites"] {
	order: 7;
}
.hl_nav-header > nav > a[meta="memberships"] {
	order: 8;
}
.hl_nav-header > nav > a[meta="contacts"] {
	order: 9;
}
.hl_nav-header > nav > a[meta="opportunities"] {
	order: 10;
}
.hl_nav-header > nav > a[meta="payments"] {
	order: 11;
}
.hl_nav-header > nav > a[meta="conversations"] {
	order: 12;
}
.hl_nav-header > nav > a[meta="calendars"] {
	order: 13;
}
.hl_nav-header > nav > a[meta="app-media"] {
	order: 14;
}
.hl_nav-header > nav > a[meta="ai-employee-promo"] {
	order: 15;
}
.hl_nav-header > nav > a[meta="65c1d6becf89f73c9019e05e"] {
	order: 16;
}
.hl_nav-header > nav > a[meta="app-marketplace"] {
	order: 17;
}

/* side bar -user options*/
.sidebar-v2-location #sidebar-v2 #globalSearchOpener, .sidebar-v2-location #sidebar-v2 #quickActions {
background: #fff;
}
.sidebar-v2-location #sidebar-v2 #location-switcher-sidbar-v2,
.sidebar-v2-agency #sidebar-v2 #location-switcher-sidbar-v2,
.sidebar-v2-location #sidebar-v2 #globalSearchOpener .search-shortcut {
background-color: #c12026;
color: #fff
}

/* Global Styling */
:root {
  --primary-dark: #000000;
  --primary-red: #bd0808;
  --primary-blue: #1A2165;
  --gradient-dark-red: linear-gradient(to top, #8e0e00 0%, #1f1c18 70%);
}

.sidebar-v2-location #sidebar-v2.default-bg-color,
.sidebar-v2-agency #sidebar-v2.default-bg-color {
background-image: var(--gradient-dark-red);
}
.sidebar-v2-location .hl_header,
.sidebar-v2-agency .hl_header.--agency {
    background: #0F0C2C;
}
.sidebar-v2-location .hl_header .topmenu-nav{
    background: #e5e7eb;
    padding-top: 10px;
}
.sidebar-v2-location #sidebar-v2 .hl_nav-header nav a.active,
.sidebar-v2-agency #sidebar-v2 .hl_nav-header nav a.active {
background: #1A2165
}
/* Enhanced Login Page CSS - Clean Background Colors */
:root {
    /* Background Colors - Matching Signup Page */
    --background-main: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    --background-card: rgba(30, 41, 59, 0.8);

    /* AHA Brand Colors */
    --primary-color: #ea384c;
    --hover-color: #c4162a;
    --text-color: #ffffff;

    /* Input Styles */
    --input-bg: rgba(31, 41, 55, 0.8);
    --input-border: rgba(75, 85, 99, 1);

    /* Effects */
    --border-radius: 8px;
}

/* Background Styles - Matching Signup Page with Corner Glows */
.hl_login {
    background: var(--background-main) !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 0 auto !important;
    padding: 20px !important;
    min-height: 100vh;
    position: relative !important;
    overflow: hidden !important;
}

/* Upper right red glow */
.hl_login::before {
    content: "" !important;
    position: absolute !important;
    top: -200px !important;
    right: -200px !important;
    width: 400px !important;
    height: 400px !important;
    background: radial-gradient(circle, rgba(220, 38, 127, 0.15) 0%, rgba(220, 38, 127, 0.08) 40%, transparent 70%) !important;
    pointer-events: none !important;
    z-index: 1 !important;
}

/* Lower left red glow */
.hl_login::after {
    content: "" !important;
    position: absolute !important;
    bottom: -200px !important;
    left: -200px !important;
    width: 400px !important;
    height: 400px !important;
    background: radial-gradient(circle, rgba(220, 38, 127, 0.12) 0%, rgba(220, 38, 127, 0.06) 40%, transparent 70%) !important;
    pointer-events: none !important;
    z-index: 1 !important;
}

/* Header Hiding */
.hl_login--header {
    display: none !important;
}

/* Login Box Body - Two Column Layout Like Signup Page */
div.hl_login--body {
    max-width: 400px !important;
    width: 90% !important;
    background: var(--background-card) !important;
    border-radius: var(--border-radius) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    padding: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: auto !important;
}

/* Remove branding pseudo-element - it interferes with form */

/* Responsive Design */
@media only screen and (max-width: 768px) {
    div.hl_login--body {
        flex-direction: column !important;
        max-width: 400px !important;
        margin: 20px !important;
    }

    .hl_login--body::before {
        font-size: 1.8rem !important;
        padding: 40px 30px 20px 30px !important;
        text-align: center !important;
        position: relative !important;
    }

    .hl_login--body::after {
        position: relative !important;
        left: auto !important;
        top: auto !important;
        text-align: center !important;
        padding: 0 30px 20px 30px !important;
    }

    .hl_login {
        padding: 20px !important;
    }
}

/* Remove all custom branding text - keep it clean */

/* Card Styles - Clean Single Container - NO DOUBLE CONTAINERS */
.hl_login--body .card {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    flex: 1 !important;
    max-width: 450px !important;
    display: flex !important;
    align-items: center !important;
    position: relative !important;
    z-index: 2 !important;
}

.hl_login--body .card .card-body {
    padding: 60px 40px !important;
    background: transparent !important;
    width: 100% !important;
    position: relative !important;
    z-index: 3 !important;
}

/* Input Fields - Clean Style */
.hl_login .hl_login--body .form-control {
    background: var(--input-bg) !important;
    color: var(--text-color) !important;
    border: 1px solid var(--input-border) !important;
    border-radius: var(--border-radius) !important;
    padding: 12px 16px !important;
    margin-bottom: 16px !important;
    transition: all 0.3s ease !important;
    font-size: 0.875rem;
}

.hl_login .hl_login--body .form-control:focus {
    background: rgba(31, 41, 55, 0.9) !important;
    border-color: #dc267f !important;
    box-shadow: 0 0 0 3px rgba(220, 38, 127, 0.1), 0 0 15px rgba(220, 38, 127, 0.2) !important;
    outline: none !important;
}

.hl_login .hl_login--body .form-control::placeholder {
    color: rgba(156, 163, 175, 1) !important;
}

/* Keep default heading visible */

/* Hide original titles to prevent duplication */
.hl_login--body .card-body h1,
.hl_login--body .card-body h2,
.hl_login--body .card-body .login-card-heading,
.hl_login--body .card-body .login-card-heading h2 {
    display: none !important;
}

/* Remove custom form titles - keep it clean */

/* Add signup link at bottom - using JavaScript for functionality */
.hl_login--body .card-body {
    position: relative !important;
}

/* Create signup link after form */
.hl_login--body .card-body:after {
    content: "" !important;
    display: block !important;
    margin-top: 24px !important;
}

/* Style for signup link that will be added via JavaScript */
.signup-link {
    display: block !important;
    text-align: center !important;
    margin-top: 24px !important;
    padding: 12px 24px !important;
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 0.875rem !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 8px !important;
    background: transparent !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

.signup-link:hover {
    background: rgba(255, 255, 255, 1) !important;
    color: var(--primary-color) !important;
    text-decoration: none !important;
}

/* Button Styles - Matching Signup Page */
.hl_login .btn.btn-blue,
.hl_login .btn.btn-blue:active,
.hl_login .btn.btn-blue:focus {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: var(--text-color) !important;
    transition: all 0.3s ease-in-out !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    min-height: 48px !important;
    width: 100% !important;
    font-size: 1rem !important;
    margin-top: 16px !important;
    border: none !important;
}

.hl_login .btn.btn-blue:hover {
    background-color: var(--hover-color) !important;
    color: var(--text-color) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(234, 56, 76, 0.3) !important;
}

/* Fix Google Button Text Visibility */
.hl_login button,
.hl_login .btn,
.hl_login [class*="btn"] {
    background-color: var(--primary-color) !important;
    color: #ffffff !important;
    min-height: 48px !important;
    width: 100% !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    border: none !important;
    font-size: 1rem !important;
}

.hl_login button:hover,
.hl_login .btn:hover,
.hl_login [class*="btn"]:hover {
    background-color: var(--hover-color) !important;
    color: #ffffff !important;
    transform: translateY(-1px) !important;
}

/* Specifically target Google button text */
.hl_login button span,
.hl_login .btn span,
.hl_login button *,
.hl_login .btn * {
    color: #ffffff !important;
    font-weight: 600 !important;
}

/* Additional Text Colors */
.hl_login--body .card-body .login-card-heading h2,
.hl_login--body .card-body .flex span {
    color: var(--text-color);
}

#forgot_passowrd_btn,
.card-body .foot-note .font-semibold,
.hl_login--body .card-body .font-semibold.cursor-pointer.text-curious-blue-500 {
    color: var(--primary-color);
}

/* Input Styles */
.hl_login--body input[type="radio"] {
    color: var(--primary-color);
}

/* Input Fields - Targeted GHL Login Only */
.hl_login--body input.hl-text-input,
.hl_login--body input[type="email"],
.hl_login--body input[type="password"],
.hl_login--body input[type="text"],
.hl_login--body .form-control {
    background-color: rgba(26, 31, 44, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 6px !important;
    color: #fff !important;
    font-size: 14px !important;
    padding: 12px !important;
    width: 100% !important;
    transition: all 0.3s ease !important;
    box-sizing: border-box !important;
}

/* Focus states for GHL login input fields only */
.hl_login--body input.hl-text-input:focus,
.hl_login--body input[type="email"]:focus,
.hl_login--body input[type="password"]:focus,
.hl_login--body input[type="text"]:focus,
.hl_login--body .form-control:focus {
    outline: none !important;
    border-color: #dc267f !important;
    box-shadow: 0 0 0 3px rgba(220, 38, 127, 0.2) !important;
}

/* Focus Styles */
.hl_login--body .card-body div .focus\:outline-none {
    background: var(--primary-color);
    border: none;
    border-radius: 8px !important;
    color: var(--text-color);
    font-size: 16px;
}

/* Paragraph Text Styles */
.hl_login--body .card .card-body p {
    color: var(--text-color);
}

/* Custom signup link styling */
/* REMOVED: .hl_login--body .form-group label { display: none; } - This was hiding field labels */

.hl_login--body .foot-note {
    font-size: .75rem;
    text-align: center;
    margin-top: 10px;
    color: #fff;
}

.custom-signup-link {
    text-align: center !important;
    margin-top: 25px !important;
    padding-top: 20px !important;
    border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.custom-signup-link a {
    color: var(--text-color) !important;
    text-decoration: none !important;
    font-size: 15px !important;
    font-weight: 500 !important;
    display: inline-block !important;
    padding: 12px 24px !important;
    border: 2px solid var(--primary-color) !important;
    border-radius: var(--border-radius) !important;
    transition: all 0.3s ease !important;
    background: var(--primary-color) !important;
}

.custom-signup-link a:hover {
    background: var(--hover-color) !important;
    color: var(--text-color) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(234, 56, 76, 0.4) !important;
}

/* Fix field labels (Email, Password) visibility - Clean White Labels */
/* Target all possible label selectors in GHL login */
.hl_login label,
.hl_login--body label,
.hl_login--body .card label,
.hl_login--body .card-body label,
.hl_login--body .form-group label,
.hl_login--body .card-body .form-group label,
.hl_login--body .card-body .form-label,
.hl_login--body .card-body [for],
.hl_login--body .card-body .input-label,
.hl_login .form-group label,
.hl_login .form-label,
.hl_login [for],
label[for="email"],
label[for="password"],
.hl_login--body label[for="email"],
.hl_login--body label[for="password"],
.hl_login--body .card-body label[for="email"],
.hl_login--body .card-body label[for="password"] {
    color: #ffffff !important;
    font-weight: 500 !important;
    margin-bottom: 8px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    background-color: transparent !important;
    padding: 0 !important;
    width: auto !important;
    max-width: 100% !important;
    font-size: 14px !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5) !important;
    position: relative !important;
    z-index: 999 !important;
    border-radius: 0 !important;
    margin: 0 0 8px 0 !important;
    line-height: 1.4 !important;
}

/* Force override any hiding rules */
.hl_login--body .form-group label,
.hl_login .form-group label {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Target specific input containers to ensure labels show */
.hl_login--body .form-group,
.hl_login .form-group {
    position: relative !important;
}

.hl_login--body .form-group > label:first-child,
.hl_login .form-group > label:first-child {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: #ffffff !important;
}

/* Fix "Or continue with" text visibility - Make it darker and more visible */
.hl_login--body .card-body .text-center,
.hl_login--body .card-body .text-gray-400,
.hl_login--body .card-body .text-sm,
.hl_login--body .card-body div[class*="text-"],
.hl_login--body .card-body span,
.hl_login--body .card-body p,
.hl_login--body .card-body div,
.hl_login--body .card-body .text-xs,
.hl_login--body .card-body [class*="text-gray"],
.hl_login--body .card-body [class*="opacity"] {
    color: #374151 !important;
    opacity: 1 !important;
    font-weight: 500 !important;
}

/* Sign In Button - Red styling with glowing accent */
.hl_login--body button[type="submit"],
.hl_login--body .btn-primary,
.hl_login--body button:first-of-type {
    background: linear-gradient(135deg, #dc267f 0%, #e91e63 100%) !important;
    color: white !important;
    border: 2px solid #dc267f !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    width: 100% !important;
    margin-bottom: 16px !important;
    box-shadow: 0 4px 15px rgba(220, 38, 127, 0.4), 0 0 20px rgba(220, 38, 127, 0.2) !important;
    transition: all 0.3s ease !important;
}

.hl_login--body button[type="submit"]:hover,
.hl_login--body .btn-primary:hover {
    background: linear-gradient(135deg, #e91e63 0%, #dc267f 100%) !important;
    border-color: #e91e63 !important;
    box-shadow: 0 8px 25px rgba(220, 38, 127, 0.6), 0 0 40px rgba(220, 38, 127, 0.4), 0 0 60px rgba(220, 38, 127, 0.2) !important;
    transform: translateY(-2px) scale(1.02) !important;
}

/* Google Button - White styling */
.hl_login--body button[type="button"]:not([type="submit"]),
.hl_login--body .btn:not(.btn-primary),
.hl_login--body button:last-of-type {
    background: white !important;
    color: #374151 !important;
    border: 1px solid #d1d5db !important;
    font-weight: 500 !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    width: 100% !important;
    margin-bottom: 16px !important;
}

.hl_login--body button[type="button"]:hover,
.hl_login--body .btn:not(.btn-primary):hover {
    background: #f9fafb !important;
    color: #374151 !important;
    box-shadow: 0 4px 15px rgba(220, 38, 127, 0.2), 0 0 20px rgba(220, 38, 127, 0.1) !important;
    transform: translateY(-1px) !important;
}

/* Force all text elements to be visible - but not buttons */
.hl_login--body .card-body p,
.hl_login--body .card-body span,
.hl_login--body .card-body div:not(button):not(.btn) {
    color: #374151 !important;
    opacity: 1 !important;
    font-weight: 500 !important;
}

/* Specific styling for "Or continue with" text */
.hl_login--body .card-body div:contains("Or"),
.hl_login--body .card-body span:contains("Or"),
.hl_login--body .card-body p:contains("Or"),
.hl_login--body .card-body div:contains("continue"),
.hl_login--body .card-body span:contains("continue"),
.hl_login--body .card-body p:contains("continue") {
    color: #374151 !important;
    opacity: 1 !important;
    font-weight: 500 !important;
    text-align: center !important;
}

/* Style the terms/conditions area for signup link */
.hl_login--body .card-body .foot-note,
.hl_login--body .card-body p:last-child,
.hl_login--body .card-body div:last-child p {
    text-align: center !important;
    margin-top: 24px !important;
    font-size: 0.875rem !important;
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Style all links in the footer area */
.hl_login--body .card-body .foot-note a,
.hl_login--body .card-body p:last-child a,
.hl_login--body .card-body div:last-child p a,
.hl_login--body .card-body a {
    color: var(--primary-color) !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    transition: color 0.2s ease !important;
}

.hl_login--body .card-body .foot-note a:hover,
.hl_login--body .card-body p:last-child a:hover,
.hl_login--body .card-body div:last-child p a:hover,
.hl_login--body .card-body a:hover {
    color: #c53030 !important;
    text-decoration: underline !important;
}

/* ULTRA AGGRESSIVE LABEL TARGETING - Every possible GHL selector */
/* Target absolutely every possible label selector GHL might use */
.hl_login * label,
.hl_login--body * label,
.hl_login div label,
.hl_login--body div label,
.hl_login form label,
.hl_login--body form label,
.hl_login .card label,
.hl_login--body .card label,
.hl_login .card-body label,
.hl_login--body .card-body label,
.hl_login .form-group label,
.hl_login--body .form-group label,
.hl_login input + label,
.hl_login--body input + label,
.hl_login label + input,
.hl_login--body label + input,
.hl_login [for="email"],
.hl_login--body [for="email"],
.hl_login [for="password"],
.hl_login--body [for="password"],
.hl_login [for="user_email"],
.hl_login--body [for="user_email"],
.hl_login [for="user_password"],
.hl_login--body [for="user_password"],
.hl_login .input-label,
.hl_login--body .input-label,
.hl_login .field-label,
.hl_login--body .field-label,
.hl_login .form-label,
.hl_login--body .form-label,
.hl_login .control-label,
.hl_login--body .control-label,
label[class*="email"],
label[class*="password"],
label[class*="user"],
label[class*="login"] {
    color: #ffffff !important;
    font-weight: 500 !important;
    margin-bottom: 8px !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    background-color: transparent !important;
    padding: 0 !important;
    width: auto !important;
    max-width: 100% !important;
    font-size: 14px !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5) !important;
    position: relative !important;
    z-index: 9999 !important;
    border-radius: 0 !important;
    margin: 0 0 8px 0 !important;
    line-height: 1.4 !important;
    height: auto !important;
    min-height: auto !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important;
    text-indent: 0 !important;
    left: auto !important;
    top: auto !important;
    right: auto !important;
    bottom: auto !important;
}

/* Force show any hidden labels */
.hl_login label[style*="display: none"],
.hl_login--body label[style*="display: none"],
.hl_login label[style*="visibility: hidden"],
.hl_login--body label[style*="visibility: hidden"],
.hl_login label[style*="opacity: 0"],
.hl_login--body label[style*="opacity: 0"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* End of CSS - JavaScript should be added separately in GHL JavaScript field */

/* ===== JAVASCRIPT CODE FOR GHL JAVASCRIPT FIELD ===== */
/*
// Add this JavaScript code to the GHL JavaScript field (not CSS field)
document.addEventListener('DOMContentLoaded', function() {
    // Function to ensure labels are visible
    function ensureLabelsVisible() {
        // Find all input fields
        const emailInput = document.querySelector('input[type="email"], input[name*="email"], input[id*="email"]');
        const passwordInput = document.querySelector('input[type="password"], input[name*="password"], input[id*="password"]');

        // Function to create or show label
        function createOrShowLabel(input, labelText) {
            if (!input) return;

            // Look for existing label
            let label = input.previousElementSibling;
            if (label && label.tagName === 'LABEL') {
                label.style.display = 'block';
                label.style.visibility = 'visible';
                label.style.opacity = '1';
                label.style.color = '#ffffff';
                label.style.marginBottom = '8px';
                label.style.fontSize = '14px';
                label.style.fontWeight = '500';
                return;
            }

            // Look for label by for attribute
            label = document.querySelector(`label[for="${input.id}"]`);
            if (label) {
                label.style.display = 'block';
                label.style.visibility = 'visible';
                label.style.opacity = '1';
                label.style.color = '#ffffff';
                label.style.marginBottom = '8px';
                label.style.fontSize = '14px';
                label.style.fontWeight = '500';
                return;
            }

            // Create new label if none exists
            label = document.createElement('label');
            label.textContent = labelText;
            label.style.display = 'block';
            label.style.color = '#ffffff';
            label.style.marginBottom = '8px';
            label.style.fontSize = '14px';
            label.style.fontWeight = '500';
            label.style.textShadow = '0 1px 3px rgba(0, 0, 0, 0.5)';

            // Insert before input
            input.parentNode.insertBefore(label, input);
        }

        // Create/show labels
        createOrShowLabel(emailInput, 'Email');
        createOrShowLabel(passwordInput, 'Password');
    }

    // Run immediately
    ensureLabelsVisible();

    // Run again after a short delay in case elements load later
    setTimeout(ensureLabelsVisible, 500);
    setTimeout(ensureLabelsVisible, 1000);
    setTimeout(ensureLabelsVisible, 2000);

    // Add signup link
    function addSignupLink() {
        const cardBody = document.querySelector('.hl_login--body .card-body');
        if (cardBody && !document.querySelector('.custom-signup-link')) {
            const signupDiv = document.createElement('div');
            signupDiv.className = 'custom-signup-link';
            signupDiv.innerHTML = '<a href="https://www.aha-innovations.com/signup">No account yet? Sign up here</a>';
            cardBody.appendChild(signupDiv);
        }
    }

    // Add signup link
    addSignupLink();
    setTimeout(addSignupLink, 1000);
});
*/

