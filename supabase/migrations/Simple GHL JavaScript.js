// Existing chat widget loader
(function() {
    var script = document.createElement('script');
    script.src = 'https://widgets.leadconnectorhq.com/loader.js';
    script.setAttribute('data-resources-url', 'https://widgets.leadconnectorhq.com/chat-widget/loader.js');
    script.setAttribute('data-widget-id', '67fd2e2c30666b387227ccb7');
    document.head.appendChild(script);
})();

// SIMPLE APPROACH: Just add HTML directly to the page with matching colors
setTimeout(function() {
    console.log('Simple approach: Adding signup HTML directly to body');

    // Create a fixed position signup button with matching colors
    const signupHTML = `
        <div id="ghl-signup-button" style="
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 9999;
            background: #ea384c;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(234, 56, 76, 0.3);
            font-family: Arial, sans-serif;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        ">
            <a href="https://easy-ghl-launchpad.vercel.app/signup" target="_blank" style="
                color: white;
                text-decoration: none;
                display: block;
            ">
                📝 No account? Sign up here
            </a>
        </div>
    `;

    // Add hover effect with matching colors
    const style = document.createElement('style');
    style.textContent = `
        #ghl-signup-button:hover {
            background: #c4162a !important;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(234, 56, 76, 0.4) !important;
        }
    `;
    document.head.appendChild(style);

    // Add to body
    document.body.insertAdjacentHTML('beforeend', signupHTML);
    console.log('Fixed signup button added to page');
}, 1000);

// ALTERNATIVE: Try to inject into the actual login form
function injectIntoLoginForm() {
    console.log('Trying to inject into login form...');
    
    // Look for any element that might be the login container
    const possibleContainers = [
        document.body,
        document.querySelector('main'),
        document.querySelector('.container'),
        document.querySelector('[class*="login"]'),
        document.querySelector('form'),
        document.getElementById('root'),
        document.querySelector('.app')
    ].filter(el => el !== null);
    
    possibleContainers.forEach((container, index) => {
        if (container && !container.querySelector('.inline-signup-link')) {
            console.log(`Adding inline signup to container ${index}:`, container);
            
            const inlineSignup = document.createElement('div');
            inlineSignup.className = 'inline-signup-link';
            inlineSignup.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                background: rgba(234, 56, 76, 0.9);
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-size: 12px;
                z-index: 1000;
            `;
            inlineSignup.innerHTML = `
                <a href="https://easy-ghl-launchpad.vercel.app/signup" target="_blank"
                   style="color: white; text-decoration: none;">
                    Sign up
                </a>
            `;
            
            container.style.position = 'relative';
            container.appendChild(inlineSignup);
        }
    });
}

// Try both approaches
setTimeout(injectIntoLoginForm, 2000);
setTimeout(injectIntoLoginForm, 5000);

// NUCLEAR OPTION: Replace page content if nothing else works
setTimeout(function() {
    if (!document.querySelector('.custom-signup-link') && 
        !document.querySelector('#ghl-signup-button') && 
        !document.querySelector('.inline-signup-link')) {
        
        console.log('Nuclear option: Adding signup message to page title');
        
        // Change page title
        document.title = '🚀 Sign up at easy-ghl-launchpad.vercel.app/signup | ' + document.title;
        
        // Add a banner at the top with matching colors
        const banner = document.createElement('div');
        banner.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #ea384c;
            color: white;
            text-align: center;
            padding: 10px;
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
        `;
        banner.innerHTML = `
            🚀 <strong>New to AHA Innovations?</strong> 
            <a href="https://easy-ghl-launchpad.vercel.app/signup" target="_blank" 
               style="color: white; text-decoration: underline; margin-left: 10px;">
                Sign up here
            </a>
        `;
        
        document.body.insertBefore(banner, document.body.firstChild);
        console.log('Banner added to top of page');
    }
}, 8000);
