-- Add missing columns to the profiles table
ALTER TABLE IF EXISTS public.profiles
ADD COLUMN IF NOT EXISTS email TEXT,
ADD COLUMN IF NOT EXISTS full_name TEXT,
ADD COLUMN IF NOT EXISTS company_name TEXT,
ADD COLUMN IF NOT EXISTS phone TEXT,
ADD COLUMN IF NOT EXISTS city TEXT,
ADD COLUMN IF NOT EXISTS state TEXT,
ADD COLUMN IF NOT EXISTS country TEXT DEFAULT 'US',
ADD COLUMN IF NOT EXISTS role TEXT,
ADD COLUMN IF NOT EXISTS company_size TEXT,
ADD COLUMN IF NOT EXISTS referral_source TEXT,
ADD COLUMN IF NOT EXISTS ghl_account_id TEXT,
ADD COLUMN IF NOT EXISTS ghl_external_user_id TEXT,
ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT,
ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS ghl_auth_code TEXT;

-- Add comments to explain the new fields
COMMENT ON COLUMN public.profiles.email IS 'User email address (cached from auth.users)';
COMMENT ON COLUMN public.profiles.full_name IS 'User full name for display purposes';
COMMENT ON COLUMN public.profiles.company_name IS 'User company name for GHL account creation';
COMMENT ON COLUMN public.profiles.phone IS 'User phone number with country code';
COMMENT ON COLUMN public.profiles.city IS 'User city for GHL account creation';
COMMENT ON COLUMN public.profiles.state IS 'User state/province for GHL account creation';
COMMENT ON COLUMN public.profiles.country IS 'User country code (ISO 2-letter)';
COMMENT ON COLUMN public.profiles.role IS 'User role in company';
COMMENT ON COLUMN public.profiles.company_size IS 'Size of user company';
COMMENT ON COLUMN public.profiles.referral_source IS 'How user found out about the service';
COMMENT ON COLUMN public.profiles.ghl_account_id IS 'GHL Account ID (same as location ID)';
COMMENT ON COLUMN public.profiles.ghl_external_user_id IS 'External user ID for GHL SSO integration';
COMMENT ON COLUMN public.profiles.stripe_customer_id IS 'Stripe customer ID for billing';
COMMENT ON COLUMN public.profiles.onboarding_completed IS 'Whether user has completed onboarding flow';
COMMENT ON COLUMN public.profiles.ghl_auth_code IS 'GHL OAuth authorization code';

-- Create index on email for faster lookups
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_ghl_location_id ON public.profiles(ghl_location_id);
-- Only create ghl_user_id index if column exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'profiles'
               AND column_name = 'ghl_user_id'
               AND table_schema = 'public') THEN
        CREATE INDEX IF NOT EXISTS idx_profiles_ghl_user_id ON public.profiles(ghl_user_id);
    END IF;
END $$;