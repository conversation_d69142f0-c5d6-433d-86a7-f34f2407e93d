-- Create table for storing application settings
CREATE TABLE IF NOT EXISTS public.settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add comment to table
COMMENT ON TABLE public.settings IS 'Stores application settings like API keys and tokens';

-- Enable RLS but allow all operations for authenticated users
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;

-- Create policy to allow service role to manage settings
DROP POLICY IF EXISTS "Service role can manage settings" ON public.settings;
CREATE POLICY "Service role can manage settings"
  ON public.settings
  USING (auth.role() = 'service_role');

-- Create policy to allow authenticated users to read settings
DROP POLICY IF EXISTS "Authenticated users can read settings" ON public.settings;
CREATE POLICY "Authenticated users can read settings"
  ON public.settings
  FOR SELECT
  USING (auth.role() = 'authenticated');
