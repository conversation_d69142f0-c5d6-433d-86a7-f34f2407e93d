import fetch from 'node-fetch';

// GHL API constants
const GHL_API_URL = 'https://services.leadconnectorhq.com';
const GHL_TOKEN = 'pit-a329e4c6-962c-4d43-9c41-01eb6933dbea';

async function getGHLLocations() {
  try {
    console.log('Getting GHL locations...');
    
    // Try different endpoint formats
    const endpoints = [
      '/locations',
      '/api/v1/locations',
      '/v1/locations',
      '/v2/locations'
    ];
    
    for (const endpoint of endpoints) {
      console.log(`\nTrying endpoint: ${endpoint}`);
      
      const response = await fetch(`${GHL_API_URL}${endpoint}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': GHL_TOKEN,
          'Version': '2021-07-28'
        }
      });
      
      console.log('Response status:', response.status);
      console.log('Response status text:', response.statusText);
      
      const responseHeaders = {};
      response.headers.forEach((value, name) => {
        responseHeaders[name] = value;
      });
      console.log('Response headers:', JSON.stringify(responseHeaders, null, 2));
      
      const responseText = await response.text();
      console.log('Response body (truncated):', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the test
getGHLLocations();
