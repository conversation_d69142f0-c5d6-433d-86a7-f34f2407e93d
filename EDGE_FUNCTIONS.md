# Edge Functions Documentation

## Overview

Edge Functions are serverless functions that run on Supabase's infrastructure. They provide a secure way to execute server-side code without managing a traditional backend. In this project, Edge Functions are used to interact with the GoHighLevel API securely, without exposing API keys to the client.

## Available Edge Functions

### 1. `create-ghl-full-account`

Creates a complete GHL account including location and user in one operation.

**Input Parameters:**
```json
{
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "companyName": "Example Company",
  "password": "securePassword123",
  "city": "New York",
  "state": "NY",
  "country": "United States",
  "phone": "+**********",
  "role": "Marketing Manager",
  "company_size": "10-50",
  "referral_source": "Google"
}
```

**Response:**
```json
{
  "locationId": "abc123",
  "userId": "xyz456",
  "role": "Marketing Manager",
  "company_size": "10-50",
  "referral_source": "Google",
  "message": "GHL location and user created successfully"
}
```

### 2. `check-ghl-user-exists`

Checks if a user already exists in GHL to prevent duplicate accounts.

**Input Parameters:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "exists": true,
  "locationId": "abc123",
  "userId": "xyz456"
}
```

### 3. `exchange-ghl-auth-code`

Exchanges OAuth authorization codes for access tokens.

**Input Parameters:**
```json
{
  "code": "auth_code_from_oauth",
  "redirectUri": "https://app.aha-innovations.com/ghl-auth-callback"
}
```

**Response:**
```json
{
  "access_token": "access_token_value",
  "refresh_token": "refresh_token_value",
  "expires_in": 3600
}
```

### 4. `get-ghl-credentials`

Securely retrieves GHL API credentials without exposing them to the client.

**Input Parameters:** None

**Response:**
```json
{
  "clientId": "masked_client_id",
  "companyId": "masked_company_id"
}
```

## Environment Variables

Edge Functions require the following environment variables to be set in the Supabase dashboard:

- `GHL_CLIENT_ID`: GoHighLevel OAuth client ID
- `GHL_CLIENT_SECRET`: GoHighLevel OAuth client secret
- `GHL_AGENCY_TOKEN`: GoHighLevel Agency Bearer token
- `GHL_COMPANY_ID`: GoHighLevel Company ID

## Deployment

To deploy Edge Functions:

1. Install Supabase CLI:
```sh
npm install -g supabase
```

2. Login to Supabase:
```sh
supabase login
```

3. Link your project:
```sh
supabase link --project-ref your-project-ref
```

4. Deploy all functions:
```sh
supabase functions deploy
```

5. Or deploy a specific function:
```sh
supabase functions deploy create-ghl-full-account
```

## Local Development

To run Edge Functions locally:

1. Start the Supabase local development server:
```sh
supabase start
```

2. Run a specific function:
```sh
supabase functions serve create-ghl-full-account
```

3. Test the function with curl:
```sh
curl -X POST http://localhost:54321/functions/v1/create-ghl-full-account \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","firstName":"Test","lastName":"User"}'
```

## Error Handling

Edge Functions return standard HTTP status codes:

- `200`: Success
- `400`: Bad Request (missing or invalid parameters)
- `401`: Unauthorized (invalid API credentials)
- `404`: Not Found
- `500`: Internal Server Error

Error responses include a JSON body with error details:

```json
{
  "error": "Error message",
  "details": "Additional error details"
}
```

## Security Considerations

- Edge Functions run in a secure environment with access to environment variables
- API keys are never exposed to the client
- CORS headers are configured to allow requests only from authorized domains
- Rate limiting is applied to prevent abuse
