// This is a serverless function that acts as a proxy to forward requests to n8n
// It avoids CORS issues by making the request server-side

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get the data from the request body
    const data = req.body;

    // Use the test webhook URL for now (can be toggled via environment variable later)
    const useTestWebhook = true; // Set to false to use the production webhook

    const webhookUrl = useTestWebhook
      ? 'https://n8n-1-i8dz.onrender.com/webhook-test/aha-signup'
      : 'https://n8n-1-i8dz.onrender.com/webhook/aha-signup';

    // Log the request for debugging
    console.log('Proxying request to n8n webhook:', webhookUrl, data);

    // Forward the request to n8n
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    // Get the response from n8n
    const responseData = await response.json().catch(() => ({}));

    // Return the response from n8n
    return res.status(response.status).json(responseData);
  } catch (error) {
    console.error('Error proxying request to n8n:', error);
    return res.status(500).json({ error: 'Failed to proxy request to n8n' });
  }
}
