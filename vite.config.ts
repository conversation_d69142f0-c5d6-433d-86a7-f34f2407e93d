import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import { handleRequest } from "./src/server";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  base: '/',
  server: {
    host: true,
    port: 8080,
    strictPort: true,
    proxy: {
      // Comment out the proxy to use local API endpoints
      // '/api': {
      //   target: 'https://ahasignup.vercel.app',
      //   changeOrigin: true,
      // },
    },
    // Custom middleware to handle API requests
    middlewares: [
      {
        name: 'handle-api-requests',
        configureServer(server) {
          server.middlewares.use(async (req, res, next) => {
            if (req.url?.startsWith('/api/')) {
              try {
                // Create a Request object from the incoming request
                const url = new URL(req.url, `http://${req.headers.host}`);
                const headers = new Headers();
                for (const [key, value] of Object.entries(req.headers)) {
                  if (value) headers.append(key, value.toString());
                }

                // Read the request body if it exists
                let body = null;
                if (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH') {
                  const chunks = [];
                  for await (const chunk of req) {
                    chunks.push(chunk);
                  }
                  body = Buffer.concat(chunks).toString();
                }

                const request = new Request(url.toString(), {
                  method: req.method,
                  headers,
                  body: body || undefined,
                });

                // Handle the request using our server handler
                const response = await handleRequest(request);

                // Set the status code and headers
                res.statusCode = response.status;
                response.headers.forEach((value, key) => {
                  res.setHeader(key, value);
                });

                // Send the response body
                const responseBody = await response.text();
                res.end(responseBody);
              } catch (error) {
                console.error('Error handling API request:', error);
                res.statusCode = 500;
                res.end(JSON.stringify({ error: 'Internal Server Error' }));
              }
            } else {
              next();
            }
          });
        },
      },
    ],
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
