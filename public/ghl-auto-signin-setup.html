<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GHL Auto Google Sign-In Setup Guide</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        .code-block {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
        }
        .step {
            background: #f8f9fa;
            border-left: 4px solid #dc2626;
            padding: 15px;
            margin: 20px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        h1, h2 { color: #dc2626; }
        .button {
            background: #dc2626;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
    </style>
</head>
<body>
    <h1>🚀 GHL Auto Google Sign-In Setup Guide</h1>
    
    <div class="success">
        <strong>✅ What this does:</strong> Automatically clicks the Google sign-in button when users are redirected from your signup page to the GHL login page, creating a seamless authentication experience.
    </div>

    <h2>📋 Setup Instructions</h2>

    <div class="step">
        <h3>Step 1: Upload JavaScript File</h3>
        <p>First, upload the <code>ghl-auto-google-signin.js</code> file to your web server or CDN so it's accessible via URL.</p>
        <p><strong>Example URL:</strong> <code>https://your-domain.com/ghl-auto-google-signin.js</code></p>
    </div>

    <div class="step">
        <h3>Step 2: Add JavaScript to GHL</h3>
        <p>In your GHL account, go to <strong>Settings → White Label → Custom JavaScript</strong> and add:</p>
        <div class="code-block">
// Load the auto-signin script
(function() {
    const script = document.createElement('script');
    script.src = 'https://your-domain.com/ghl-auto-google-signin.js';
    script.async = true;
    document.head.appendChild(script);
})();
        </div>
    </div>

    <div class="step">
        <h3>Step 3: Add CSS to GHL (Optional)</h3>
        <p>In your GHL account, go to <strong>Settings → White Label → Custom CSS</strong> and add the contents of <code>ghl-login-auto-signin.css</code> for enhanced styling.</p>
    </div>

    <div class="step">
        <h3>Step 4: Update Your Domain</h3>
        <p>In the JavaScript file, replace <code>https://your-domain.com/</code> with your actual domain where the script is hosted.</p>
    </div>

    <h2>🔧 How It Works</h2>

    <p>The auto-signin script triggers when:</p>
    <ul>
        <li>URL contains parameters: <code>auto_google=true</code>, <code>from_signup=true</code>, or <code>google_redirect=true</code></li>
        <li>User came from your signup domain (localhost:8080 or aha-innovations.com)</li>
        <li>Email field is pre-filled (indicates redirect from your system)</li>
    </ul>

    <h2>🎯 Testing</h2>

    <div class="step">
        <h3>Test the Auto-Signin</h3>
        <ol>
            <li>Go to your signup page: <code>http://localhost:8080/signup</code></li>
            <li>Click "Continue with Google"</li>
            <li>Use an email that already has a GHL account</li>
            <li>You should see "Found your account!" message</li>
            <li>You'll be redirected to GHL with auto-signin triggered</li>
        </ol>
    </div>

    <div class="warning">
        <strong>⚠️ Important:</strong> Make sure your GHL login page has a Google sign-in button visible. The script looks for various button selectors and will attempt to click the first one it finds.
    </div>

    <h2>🐛 Troubleshooting</h2>

    <div class="step">
        <h3>If Auto-Signin Doesn't Work</h3>
        <ol>
            <li>Open browser developer tools (F12)</li>
            <li>Check the Console tab for error messages</li>
            <li>Look for messages starting with "GHL Auto Google Sign-In Script"</li>
            <li>Verify the script is loading correctly</li>
            <li>Check if the Google button selector needs updating</li>
        </ol>
    </div>

    <h2>🎨 Customization</h2>

    <p>You can customize the script by modifying these settings in <code>ghl-auto-google-signin.js</code>:</p>

    <div class="code-block">
const CONFIG = {
    // Add more button selectors if needed
    googleButtonSelectors: [
        'button[data-testid="google-signin"]',
        'button:contains("Sign in with Google")',
        // Add your custom selectors here
    ],
    
    // Adjust timing
    maxWaitTime: 10000,  // 10 seconds
    checkInterval: 500,  // Check every 500ms
    
    // Add more trigger conditions
    triggerParams: ['auto_google', 'from_signup'],
    triggerReferrers: ['your-domain.com']
};
    </div>

    <div class="success">
        <strong>🎉 Result:</strong> Users who sign up with Google and already have GHL accounts will experience seamless authentication without having to click the Google button again!
    </div>

    <h2>📱 Mobile Compatibility</h2>
    <p>The script is fully compatible with mobile devices and includes responsive CSS styling for optimal mobile experience.</p>

    <h2>🔒 Security Notes</h2>
    <ul>
        <li>The script only triggers on your specified domains/conditions</li>
        <li>No sensitive data is stored or transmitted</li>
        <li>Uses standard browser APIs for button clicking</li>
        <li>Includes timeout protection to prevent infinite loops</li>
    </ul>

    <footer style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #eee; color: #666; text-align: center;">
        <p>Auto Google Sign-In for GHL - Created for AHA Innovations</p>
    </footer>
</body>
</html>
