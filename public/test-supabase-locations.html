<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test GHL Locations (Supabase Edge Function)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .security-note {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        button {
            background-color: #dc2626;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        button:hover {
            background-color: #b91c1c;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #10b981;
        }
        .error {
            border-left: 4px solid #ef4444;
        }
        .location-item {
            background: #f0f0f0;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            font-family: Arial, sans-serif;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 Secure GHL Locations Test</h1>
        <div class="security-note">
            <p><strong>✅ Fully Secure:</strong> This uses Supabase Edge Functions with server-side environment variables. No sensitive tokens are exposed to the frontend!</p>
        </div>
        
        <p>This will fetch your GHL locations using a secure Supabase Edge Function to get valid Location IDs for testing.</p>
        
        <button onclick="fetchLocations()" id="fetchBtn">Fetch GHL Locations (Secure)</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function fetchLocations() {
            const resultDiv = document.getElementById('result');
            const fetchBtn = document.getElementById('fetchBtn');
            
            resultDiv.textContent = 'Loading...';
            resultDiv.className = '';
            fetchBtn.disabled = true;

            try {
                console.log('Fetching GHL locations via Supabase Edge Function...');
                
                const response = await fetch('https://fpratwslcktwpzlbzlhm.supabase.co/functions/v1/get-ghl-locations', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                console.log('Response status:', response.status);

                let data;
                const responseText = await response.text();
                console.log('Raw response:', responseText);

                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error(`Failed to parse JSON response: ${responseText}`);
                }

                if (data.success) {
                    resultDiv.className = 'success';
                    
                    let resultText = `SUCCESS! ✅\n\n`;
                    resultText += `Found ${data.count} location(s)\n\n`;
                    
                    if (data.locations && Array.isArray(data.locations)) {
                        resultText += `LOCATION IDs FOR TESTING:\n`;
                        resultText += `${'='.repeat(50)}\n`;
                        
                        data.locations.forEach((loc, index) => {
                            resultText += `${index + 1}. ${loc.name}\n`;
                            resultText += `   ID: ${loc.id}\n`;
                            resultText += `   Address: ${loc.address || 'N/A'}\n`;
                            resultText += `   Phone: ${loc.phone || 'N/A'}\n\n`;
                        });
                        
                        // Show just the IDs for easy copying
                        resultText += `\nQUICK COPY (Location IDs only):\n`;
                        resultText += `${'='.repeat(30)}\n`;
                        data.locations.forEach(loc => {
                            resultText += `${loc.id}\n`;
                        });
                    }
                    
                    resultDiv.textContent = resultText;
                } else {
                    resultDiv.className = 'error';
                    resultDiv.textContent = `ERROR: ${data.error}\n\nDetails:\n${JSON.stringify(data, null, 2)}`;
                }

            } catch (error) {
                console.error('Error:', error);
                resultDiv.className = 'error';
                resultDiv.textContent = `ERROR: ${error.message}`;
            } finally {
                fetchBtn.disabled = false;
            }
        }
    </script>
</body>
</html>
