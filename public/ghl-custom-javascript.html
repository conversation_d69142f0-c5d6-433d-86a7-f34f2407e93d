<script>
/**
 * GHL Auto Google Sign-In Script
 * Automatically clicks Google sign-in button when users come from signup page
 */
(function() {
    'use strict';
    
    console.log('GHL Auto Google Sign-In Script loaded');
    
    // Configuration
    const CONFIG = {
        googleButtonSelectors: [
            // GHL specific selectors based on the screenshot
            'button:contains("Sign in with Google")',
            'button:contains("Continue with Google")',
            'button[class*="google"]',
            'div[class*="google"] button',
            '.google-signin-button',
            '[data-testid="google-signin"]',
            // Generic fallbacks
            'button[type="button"]',
            'button',
            // Try any clickable element with Google text
            '[role="button"]:contains("Google")',
            'div:contains("Sign in with Google")',
            'span:contains("Sign in with Google")'
        ],
        maxWaitTime: 15000,
        checkInterval: 1000,
        triggerParams: ['auto_google', 'from_signup', 'google_redirect'],
        triggerReferrers: ['localhost:8080', 'aha-innovations.com']
    };
    
    // Check if auto-signin should be triggered
    function shouldTriggerAutoSignin() {
        const urlParams = new URLSearchParams(window.location.search);
        const referrer = document.referrer;
        
        console.log('Checking auto-signin conditions:', {
            urlParams: Object.fromEntries(urlParams),
            referrer: referrer
        });
        
        // Check URL parameters
        for (const param of CONFIG.triggerParams) {
            if (urlParams.has(param)) {
                console.log('Auto-signin triggered by URL parameter: ' + param);
                return true;
            }
        }
        
        // Check referrer
        for (const domain of CONFIG.triggerReferrers) {
            if (referrer.includes(domain)) {
                console.log('Auto-signin triggered by referrer: ' + domain);
                return true;
            }
        }
        
        // Check if email is prefilled
        const emailField = document.querySelector('input[type="email"]');
        if (emailField && emailField.value) {
            console.log('Auto-signin triggered by prefilled email');
            return true;
        }
        
        return false;
    }
    
    // Find the Google sign-in button
    function findGoogleButton() {
        console.log('Searching for Google button...');

        // First, try to find any button with Google text
        const allButtons = document.querySelectorAll('button, div[role="button"], span[role="button"], a[role="button"]');
        console.log('Found ' + allButtons.length + ' clickable elements');

        for (let i = 0; i < allButtons.length; i++) {
            const btn = allButtons[i];
            const text = btn.textContent || btn.innerText || '';
            console.log('Button ' + i + ': "' + text.trim() + '"');

            if (text.toLowerCase().includes('google') || text.toLowerCase().includes('sign in with')) {
                console.log('Found potential Google button: ' + text.trim());
                if (btn.offsetParent !== null) {
                    console.log('Button is visible, using it');
                    return btn;
                }
            }
        }

        // Fallback: try specific selectors
        for (let j = 0; j < CONFIG.googleButtonSelectors.length; j++) {
            const selector = CONFIG.googleButtonSelectors[j];
            let elements;

            if (selector.includes(':contains(')) {
                const text = selector.match(/:contains\("([^"]+)"\)/)[1];
                elements = Array.from(document.querySelectorAll('button, div, span')).filter(function(btn) {
                    return (btn.textContent || btn.innerText || '').includes(text);
                });
            } else {
                elements = document.querySelectorAll(selector);
            }

            if (elements.length > 0) {
                for (let k = 0; k < elements.length; k++) {
                    const element = elements[k];
                    if (element.offsetParent !== null) {
                        console.log('Found Google button with selector: ' + selector);
                        return element;
                    }
                }
            }
        }

        console.log('No Google button found');
        return null;
    }
    
    // Show loading indicator
    function showLoadingIndicator() {
        const overlay = document.createElement('div');
        overlay.id = 'auto-signin-overlay';
        overlay.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); display: flex; align-items: center; justify-content: center; z-index: 10000; font-family: Arial, sans-serif;';
        
        overlay.innerHTML = '<div style="background: white; padding: 30px; border-radius: 10px; text-align: center; max-width: 400px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);"><div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #dc2626; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div><h3 style="margin: 0 0 10px; color: #333;">Signing you in...</h3><p style="margin: 0; color: #666; font-size: 14px;">Automatically continuing with Google authentication</p></div><style>@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>';
        
        document.body.appendChild(overlay);
        
        setTimeout(function() {
            const existingOverlay = document.getElementById('auto-signin-overlay');
            if (existingOverlay) existingOverlay.remove();
        }, 5000);
    }
    
    // Click the Google button
    function clickGoogleButton(button) {
        console.log('Attempting to click Google sign-in button:', button);
        showLoadingIndicator();

        setTimeout(function() {
            console.log('Executing click on button');

            // Try multiple click methods
            try {
                // Method 1: Direct click
                if (button.click) {
                    console.log('Trying direct click()');
                    button.click();
                }

                // Method 2: Mouse events
                console.log('Trying mouse events');
                const mouseDown = new MouseEvent('mousedown', {bubbles: true, cancelable: true, view: window});
                const mouseUp = new MouseEvent('mouseup', {bubbles: true, cancelable: true, view: window});
                const clickEvent = new MouseEvent('click', {bubbles: true, cancelable: true, view: window});

                button.dispatchEvent(mouseDown);
                button.dispatchEvent(mouseUp);
                button.dispatchEvent(clickEvent);

                // Method 3: Focus and keyboard
                console.log('Trying focus and keyboard events');
                button.focus();
                const enterEvent = new KeyboardEvent('keydown', {key: 'Enter', code: 'Enter', keyCode: 13, bubbles: true});
                const spaceEvent = new KeyboardEvent('keydown', {key: ' ', code: 'Space', keyCode: 32, bubbles: true});

                button.dispatchEvent(enterEvent);
                button.dispatchEvent(spaceEvent);

                console.log('All click methods attempted');

            } catch (error) {
                console.error('Error clicking button:', error);
            }
        }, 1000);
    }
    
    // Main function
    function attemptAutoSignin() {
        if (!shouldTriggerAutoSignin()) {
            console.log('Auto-signin not triggered');
            return;
        }
        
        console.log('Attempting auto Google sign-in...');
        
        let attempts = 0;
        const maxAttempts = CONFIG.maxWaitTime / CONFIG.checkInterval;
        
        const checkForButton = setInterval(function() {
            attempts++;
            console.log('Looking for Google button (attempt ' + attempts + '/' + maxAttempts + ')');
            
            const googleButton = findGoogleButton();
            
            if (googleButton) {
                clearInterval(checkForButton);
                clickGoogleButton(googleButton);
                return;
            }
            
            if (attempts >= maxAttempts) {
                clearInterval(checkForButton);
                console.log('Auto-signin timeout - Google button not found');
            }
        }, CONFIG.checkInterval);
    }
    
    // Initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', attemptAutoSignin);
    } else {
        attemptAutoSignin();
    }
    
    setTimeout(attemptAutoSignin, 2000);
})();
</script>
