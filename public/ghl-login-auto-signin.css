/**
 * GHL Login Page Styling with Auto-Signin Support
 * This CSS should be added to the GHL Custom CSS section
 */

/* Import the auto-signin JavaScript */
@import url('https://your-domain.com/ghl-auto-google-signin.js');

/* Base styling for the login page */
.hl_login {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Arial', sans-serif;
}

.hl_login--body {
    background: rgba(30, 30, 30, 0.95);
    border: 1px solid #444;
    border-radius: 12px;
    padding: 40px;
    max-width: 400px;
    width: 100%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    position: relative;
}

/* Add subtle glow effect */
.hl_login--body::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #dc2626, #ef4444, #dc2626);
    border-radius: 12px;
    z-index: -1;
    opacity: 0.3;
}

/* Form styling */
.hl_login--form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Input field styling */
.hl_login input[type="email"],
.hl_login input[type="password"] {
    background: #2a2a2a;
    border: 1px solid #555;
    border-radius: 8px;
    padding: 12px 16px;
    color: #fff;
    font-size: 14px;
    transition: all 0.3s ease;
}

.hl_login input[type="email"]:focus,
.hl_login input[type="password"]:focus {
    outline: none;
    border-color: #dc2626;
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
}

/* Button styling */
.hl_login button,
.hl_login .btn {
    background: #dc2626;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.hl_login button:hover,
.hl_login .btn:hover {
    background: #b91c1c;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

/* Google sign-in button specific styling */
.hl_login button[class*="google"],
.hl_login .google-signin-button,
.hl_login button:contains("Google") {
    background: #4285f4;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    position: relative;
    overflow: hidden;
}

.hl_login button[class*="google"]:hover,
.hl_login .google-signin-button:hover {
    background: #3367d6;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
}

/* Auto-signin loading state */
.hl_login button[class*="google"].auto-clicking,
.hl_login .google-signin-button.auto-clicking {
    background: #34d399;
    pointer-events: none;
}

.hl_login button[class*="google"].auto-clicking::after,
.hl_login .google-signin-button.auto-clicking::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Labels and text styling */
.hl_login label {
    color: #ccc;
    font-size: 14px;
    margin-bottom: 5px;
    display: block;
}

.hl_login .forgot-password,
.hl_login a {
    color: #dc2626;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.hl_login .forgot-password:hover,
.hl_login a:hover {
    color: #ef4444;
    text-decoration: underline;
}

/* Divider styling */
.hl_login .divider {
    display: flex;
    align-items: center;
    margin: 20px 0;
    color: #666;
    font-size: 14px;
}

.hl_login .divider::before,
.hl_login .divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: #444;
}

.hl_login .divider::before {
    margin-right: 15px;
}

.hl_login .divider::after {
    margin-left: 15px;
}

/* Signup link styling */
.hl_login .signup-link {
    text-align: center;
    margin-top: 20px;
    padding: 15px;
    background: transparent;
    border: 2px solid #dc2626;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.hl_login .signup-link:hover {
    background: #dc2626;
    color: white;
}

.hl_login .signup-link a {
    color: inherit;
    text-decoration: none;
    font-weight: 600;
}

/* Auto-signin notification */
.auto-signin-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #10b981;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    z-index: 9999;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive design */
@media (max-width: 480px) {
    .hl_login--body {
        margin: 20px;
        padding: 30px 20px;
    }
    
    .hl_login input[type="email"],
    .hl_login input[type="password"],
    .hl_login button {
        padding: 14px 16px;
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Dark theme enhancements */
.hl_login {
    color: #fff;
}

.hl_login h1,
.hl_login h2,
.hl_login h3 {
    color: #fff;
    margin-bottom: 20px;
    text-align: center;
}

/* Loading overlay styling (for the auto-signin script) */
#auto-signin-overlay {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

#auto-signin-overlay h3 {
    font-size: 18px;
    font-weight: 600;
}

#auto-signin-overlay p {
    line-height: 1.5;
}
