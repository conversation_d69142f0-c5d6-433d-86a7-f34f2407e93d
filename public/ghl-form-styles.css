/* GHL Form Custom Styling - Implementation based on GHL_FORM_IMPLEMENTATION.md */

/* Form container */
.form-container {
  background-color: transparent !important;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
  backdrop-filter: none;
  border: none;
  max-width: 100%;
}

/* Glass effect for the form container */
.glass-effect {
  background: rgba(13, 18, 23, 0.7) !important;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

/* Hide side columns completely - more aggressive targeting */
.col-md-3, .col-lg-3, .col-xl-3, .col-3,
.offset-md-3, .offset-lg-3, .offset-xl-3,
.col-sm-3, .col-xs-3,
div[class*="col-md-3"], div[class*="col-lg-3"], div[class*="col-3"],
div[class*="col-sm-3"], div[class*="col-xs-3"],
.col:first-child, .col:last-child,
.row > div:first-child, .row > div:last-child {
  display: none !important;
  width: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  max-width: 0 !important;
  flex: 0 0 0% !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

/* Make the form column take full width - more aggressive targeting */
.col-md-6, .col-lg-6, .col-xl-6, .col-6,
.col-sm-6, .col-xs-6,
div[class*="col-md-6"], div[class*="col-lg-6"], div[class*="col-6"],
div[class*="col-sm-6"], div[class*="col-xs-6"],
.row > div:nth-child(2),
.container > .row > div {
  flex: 0 0 100% !important;
  max-width: 100% !important;
  width: 100% !important;
  padding: 0 !important;
  margin: 0 auto !important;
  float: none !important;
}

/* Style the form box */
.form-box, .form-box-wrapper {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* Input Fields */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
textarea,
select,
.form-control,
.form-select {
  width: 100%;
  padding: 10px 12px;
  background-color: rgba(30, 41, 59, 0.8) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 6px;
  color: #fff !important;
  font-size: 14px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

/* Mobile responsive input fields */
@media (min-width: 640px) {
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="password"],
  textarea,
  select,
  .form-control,
  .form-select {
    padding: 12px 16px;
    font-size: 16px;
  }
}

/* Labels */
label, .form-label {
  color: #fff !important;
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}

/* Submit Button */
button[type="submit"],
.btn-primary {
  background-color: #ea384c !important;
  color: white !important;
  border: none !important;
  border-radius: 6px;
  padding: 12px 24px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 16px;
}

button[type="submit"]:hover,
.btn-primary:hover {
  background-color: #c4162a !important;
}

/* Fix any overflow issues */
.overflow-hidden {
  overflow: visible !important;
}

/* Remove any borders or outlines */
* {
  outline: none !important;
}

/* Target the form content area */
.form-content {
  background-color: transparent !important;
  padding: 20px !important;
}

/* Target the form container */
.hightouch-form-container {
  background-color: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* Target the form wrapper */
.form-wrapper {
  background-color: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* Target the form itself */
form {
  background-color: transparent !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* Target the form fields */
.form-group {
  background-color: transparent !important;
  padding: 0 !important;
  margin: 0 0 15px 0 !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* Target the form field labels */
.form-group label {
  background-color: transparent !important;
  padding: 0 !important;
  margin: 0 0 5px 0 !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* Target the form field inputs */
.form-group input, .form-group select, .form-group textarea {
  background-color: rgba(26, 31, 44, 0.8) !important; /* Semi-transparent bg-aha-dark color */
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  padding: 12px !important;
  margin: 0 !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* Target the form submit button */
.form-group button {
  background-color: #ea384c !important;
  padding: 12px 20px !important;
  margin: 0 auto !important;
  width: 100% !important;
  max-width: 400px !important;
  display: block !important;
}

/* Target the form submit button hover */
.form-group button:hover {
  background-color: #c4162a !important;
}

/* Target the form submit button active */
.form-group button:active {
  background-color: #c4162a !important;
}

/* Target the form submit button focus */
.form-group button:focus {
  background-color: #c4162a !important;
}

/* Target the form submit button disabled */
.form-group button:disabled {
  background-color: #ea384c !important;
  opacity: 0.5 !important;
}

/* Target the form submit button loading */
.form-group button.loading {
  background-color: #ea384c !important;
  opacity: 0.5 !important;
}

/* Target the form submit button success */
.form-group button.success {
  background-color: #ea384c !important;
  opacity: 0.5 !important;
}

/* Target the form submit button error */
.form-group button.error {
  background-color: #ea384c !important;
  opacity: 0.5 !important;
}

/* Additional background color fixes */
iframe, iframe body, iframe html, iframe div, iframe form, iframe .container, iframe .row, iframe .col {
  background-color: transparent !important;
  background: transparent !important;
}

/* Force transparent background on all elements */
* {
  background-color: transparent !important;
}

/* Target specific white background elements - super aggressive */
[style*="background-color: rgb(255, 255, 255)"],
[style*="background-color:#fff"],
[style*="background-color: #fff"],
[style*="background-color:white"],
[style*="background-color: white"],
[style*="background: white"],
[style*="background:white"],
[style*="background: #fff"],
[style*="background:#fff"],
[style*="background-color: rgb(248, 249, 250)"],
[style*="background-color:#f8f9fa"],
[style*="background-color: #f8f9fa"],
[style*="background-color: rgb(240, 240, 240)"],
[style*="background-color:#f0f0f0"],
[style*="background-color: #f0f0f0"],
[style*="background-color: rgb(245, 245, 245)"],
[style*="background-color:#f5f5f5"],
[style*="background-color: #f5f5f5"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
}

/* Target any element with a background color */
[style*="background-color"],
[style*="background:"],
[style*="background-image"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
}

/* Ensure the form is visible with proper spacing */
.form-group {
  margin-bottom: 15px !important;
}

/* Make sure inputs have proper padding */
input, select, textarea {
  padding: 12px !important;
}

/* Ensure text is visible */
input, select, textarea, label, p, span, a, button {
  color: white !important;
}

/* Fix any potential z-index issues */
.glass-effect {
  position: relative;
  z-index: 10;
}

/* Ensure the iframe content is properly styled */
iframe {
  min-height: 500px !important;
  background-color: transparent !important;
  background: transparent !important;
}

/* Target the iframe document directly */
iframe html,
iframe body {
  background-color: transparent !important;
  background: transparent !important;
}

/* Target the iframe with ghl-form-iframe class specifically */
.ghl-form-iframe {
  background-color: transparent !important;
}

/* Target the form container inside the iframe */
.ghl-form-iframe body,
.ghl-form-iframe html,
.ghl-form-iframe .container,
.ghl-form-iframe .row,
.ghl-form-iframe .col,
.ghl-form-iframe form,
.ghl-form-iframe .form-group,
.ghl-form-iframe .form-control {
  background-color: transparent !important;
  background: transparent !important;
}

/* Fix for GHL form specific elements */
.hightouch-form-container, .hightouch-form-container * {
  background-color: transparent !important;
}

/* Fix for any white backgrounds in the form */
.form-box, .form-box-wrapper, .form-content, .form-wrapper, .form-group, .form-control,
.card, .card-body, .card-header, .card-footer, .modal, .modal-content, .modal-body, .modal-header, .modal-footer,
.container, .row, .col, .col-md-6, .col-md-12 {
  background-color: transparent !important;
  box-shadow: none !important;
  border: none !important;
}

/* Fix for any white backgrounds in the form inputs */
input, select, textarea {
  background-color: rgba(26, 31, 44, 0.8) !important; /* Semi-transparent bg-aha-dark color */
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  border-radius: 8px !important;
  padding: 12px !important;
}

/* Fix for any white text in the form */
label, p, span, a, button {
  color: white !important;
}

/* Fix for any white backgrounds in the form buttons */
button, .btn, .btn-primary {
  background-color: #e11d48 !important;
  border: none !important;
  color: white !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 6px rgba(225, 29, 72, 0.25) !important;
}

/* Super aggressive targeting for white containers */
.container, .container-fluid, .row, main, section, article, aside, header, footer,
.col-md-3, .col-lg-3, .col-3, .col-sm-3, .col-xs-3,
div[class*="col-md-3"], div[class*="col-lg-3"], div[class*="col-3"],
div[class*="col-sm-3"], div[class*="col-xs-3"],
.row > div:first-child, .row > div:last-child,
.row > .col:first-child, .row > .col:last-child,
.row > [class*="col-"]:first-child, .row > [class*="col-"]:last-child {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
}

/* Hide all side columns */
.row > div:first-child, .row > div:last-child,
.row > .col:first-child, .row > .col:last-child,
.row > [class*="col-"]:first-child, .row > [class*="col-"]:last-child,
.col-md-3, .col-lg-3, .col-3, .col-sm-3, .col-xs-3,
div[class*="col-md-3"], div[class*="col-lg-3"], div[class*="col-3"],
div[class*="col-sm-3"], div[class*="col-xs-3"] {
  display: none !important;
  width: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  max-width: 0 !important;
  flex: 0 0 0% !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  /* Container adjustments */
  .form-container, .form-wrapper, .ghl-form, iframe {
    padding: 16px !important;
    margin: 0 !important;
    max-width: 100% !important;
  }

  /* Form grid adjustments for mobile */
  .form-row, .row {
    display: flex !important;
    flex-direction: column !important;
    gap: 12px !important;
  }

  .form-group, .col, .col-md-6, .col-sm-6 {
    width: 100% !important;
    margin-bottom: 16px !important;
    padding: 0 !important;
  }

  /* Button adjustments */
  button, .btn, .btn-primary {
    padding: 14px 20px !important;
    font-size: 16px !important;
    margin-top: 16px !important;
    width: 100% !important;
  }

  /* Label adjustments */
  label, .form-label {
    font-size: 14px !important;
    margin-bottom: 6px !important;
  }

  /* Input field adjustments */
  input, select, textarea {
    font-size: 16px !important; /* Prevents zoom on iOS */
    padding: 12px !important;
    min-height: 44px !important; /* Touch target size */
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .form-container, .form-wrapper, .ghl-form {
    padding: 12px !important;
  }

  input, select, textarea {
    padding: 10px !important;
  }

  button, .btn, .btn-primary {
    padding: 12px 16px !important;
  }
}
