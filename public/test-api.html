<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test GHL Integration</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input {
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      cursor: pointer;
    }
    #result {
      margin-top: 20px;
      padding: 15px;
      border: 1px solid #ddd;
      background-color: #f9f9f9;
      white-space: pre-wrap;
      display: none;
    }
  </style>
</head>
<body>
  <h1>Test GHL Integration</h1>
  
  <div class="form-group">
    <label for="email">Email:</label>
    <input type="email" id="email" value="<EMAIL>">
  </div>
  
  <div class="form-group">
    <label for="firstName">First Name:</label>
    <input type="text" id="firstName" value="Test">
  </div>
  
  <div class="form-group">
    <label for="lastName">Last Name:</label>
    <input type="text" id="lastName" value="User">
  </div>
  
  <div class="form-group">
    <label for="companyName">Company Name:</label>
    <input type="text" id="companyName" value="Test Company">
  </div>
  
  <button id="testButton">Test Integration</button>
  
  <div id="result"></div>
  
  <script>
    document.getElementById('testButton').addEventListener('click', async function() {
      const resultDiv = document.getElementById('result');
      resultDiv.style.display = 'block';
      resultDiv.textContent = 'Testing...';
      
      try {
        const response = await fetch('https://ahasignup.vercel.app/api/test-ghl-integration', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: document.getElementById('email').value,
            firstName: document.getElementById('firstName').value,
            lastName: document.getElementById('lastName').value,
            companyName: document.getElementById('companyName').value
          })
        });
        
        const data = await response.json();
        
        resultDiv.textContent = JSON.stringify(data, null, 2);
      } catch (error) {
        resultDiv.textContent = 'Error: ' + error.message;
      }
    });
  </script>
</body>
</html>
