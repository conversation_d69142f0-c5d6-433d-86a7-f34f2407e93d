<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test GHL Form Submission</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        .info-box {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #dc2626;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #b91c1c;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        .error {
            background-color: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        .warning {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test GHL Contact Creation & Automation</h1>
        <p>This page tests the GHL Contact Creation API to trigger your automation pipeline. When a contact is created in GHL, it should trigger your existing automation.</p>
        
        <form id="testForm">
            <div class="info-box">
                <p><strong>🔒 Security Note:</strong> GHL Private Integration Token is now handled server-side for security.</p>
            </div>

            <div class="form-group">
                <label for="locationId">Location ID (Required):</label>
                <input type="text" id="locationId" name="locationId" placeholder="Enter GHL Location ID" required>
                <small>This should be the locationId from a created GHL account</small>
            </div>

            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" placeholder="<EMAIL>">
            </div>

            <div class="form-group">
                <label for="firstName">First Name:</label>
                <input type="text" id="firstName" name="firstName" placeholder="John">
            </div>

            <div class="form-group">
                <label for="lastName">Last Name:</label>
                <input type="text" id="lastName" name="lastName" placeholder="Doe">
            </div>

            <div class="form-group">
                <label for="companyName">Company Name:</label>
                <input type="text" id="companyName" name="companyName" placeholder="Acme Corp">
            </div>

            <div class="form-group">
                <label for="phone">Phone:</label>
                <input type="tel" id="phone" name="phone" placeholder="+1234567890">
            </div>

            <div class="form-group">
                <label for="city">City:</label>
                <input type="text" id="city" name="city" placeholder="New York">
            </div>

            <div class="form-group">
                <label for="state">State:</label>
                <input type="text" id="state" name="state" placeholder="NY">
            </div>

            <div class="form-group">
                <label for="country">Country:</label>
                <input type="text" id="country" name="country" placeholder="US">
            </div>

            <div class="form-group">
                <label for="postalCode">Postal Code:</label>
                <input type="text" id="postalCode" name="postalCode" placeholder="90210">
            </div>

            <div class="form-group">
                <label for="goal">Goal:</label>
                <textarea id="goal" name="goal" rows="3" placeholder="What do you want to achieve?"></textarea>
            </div>

            <button type="button" onclick="submitForm(false)">Create GHL Contact</button>
            <button type="button" onclick="submitForm(true)">Test with Sample Data</button>
        </form>

        <div id="result"></div>
    </div>

    <script>
        async function submitForm(testMode) {
            const resultDiv = document.getElementById('result');
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            
            // Disable buttons during submission
            const buttons = document.querySelectorAll('button');
            buttons.forEach(btn => btn.disabled = true);

            try {
                const requestBody = {
                    testMode,
                    locationId: formData.get('locationId'),
                };

                // Only include locationId (token is handled server-side)
                requestBody.locationId = formData.get('locationId');

                // Only include other fields if not in test mode
                if (!testMode) {
                    requestBody.email = formData.get('email');
                    requestBody.firstName = formData.get('firstName');
                    requestBody.lastName = formData.get('lastName');
                    requestBody.companyName = formData.get('companyName');
                    requestBody.phone = formData.get('phone');
                    requestBody.city = formData.get('city');
                    requestBody.state = formData.get('state');
                    requestBody.country = formData.get('country');
                    requestBody.postalCode = formData.get('postalCode');
                    requestBody.goal = formData.get('goal');
                }

                console.log('Sending request:', requestBody);

                const response = await fetch('/api/test-ghl-form', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                let data;
                const responseText = await response.text();
                console.log('Raw response:', responseText);

                try {
                    data = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error(`Failed to parse JSON response: ${responseText}`);
                }

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Success!\n\n${JSON.stringify(data, null, 2)}`;
                    
                    if (data.warnings && data.warnings.length > 0) {
                        resultDiv.className = 'result warning';
                        resultDiv.textContent = `⚠️ Partial Success (with warnings)\n\n${JSON.stringify(data, null, 2)}`;
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Error!\n\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Network Error!\n\n${error.message}`;
            } finally {
                // Re-enable buttons
                buttons.forEach(btn => btn.disabled = false);
            }
        }
    </script>
</body>
</html>
