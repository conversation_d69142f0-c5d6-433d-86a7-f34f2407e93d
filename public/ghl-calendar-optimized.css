/* AHA-Innovations GHL Calendar - Optimized Styles */

/* Primary brand color - AHA Red */
:root {
  --primary-color: #ea384c !important;
  --primary-hover: #d32f2f !important;
}

/* Selected date styling - this is working! */
.react-calendar__tile--active,
.react-calendar__tile--now,
.selected-date,
.active-date {
  background-color: #ea384c !important;
  color: white !important;
  border-radius: 50% !important;
}

/* Time slot buttons */
.time-slot-button,
.appointment-time,
.time-button,
button[class*="time"] {
  border: 2px solid #ea384c !important;
  color: #ea384c !important;
  background-color: transparent !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
}

.time-slot-button:hover,
.appointment-time:hover,
.time-button:hover,
button[class*="time"]:hover {
  background-color: #ea384c !important;
  color: white !important;
}

/* Selected time slot */
.time-slot-button.selected,
.appointment-time.selected,
.time-button.selected,
button[class*="time"].selected {
  background-color: #ea384c !important;
  color: white !important;
}

/* Primary action buttons */
.btn-primary,
.primary-btn,
.book-btn,
.submit-btn,
.continue-btn,
button[type="submit"] {
  background-color: #ea384c !important;
  border-color: #ea384c !important;
  color: white !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  padding: 12px 24px !important;
}

.btn-primary:hover,
.primary-btn:hover,
.book-btn:hover,
.submit-btn:hover,
.continue-btn:hover,
button[type="submit"]:hover {
  background-color: #d32f2f !important;
  border-color: #d32f2f !important;
}

/* Calendar navigation arrows */
.react-calendar__navigation button,
.calendar-nav-btn,
.nav-arrow {
  color: #ea384c !important;
  font-weight: bold !important;
}

.react-calendar__navigation button:hover,
.calendar-nav-btn:hover,
.nav-arrow:hover {
  background-color: rgba(234, 56, 76, 0.1) !important;
  color: #d32f2f !important;
}

/* Form inputs */
input[type="text"],
input[type="email"],
input[type="tel"],
textarea,
select {
  border: 2px solid rgba(234, 56, 76, 0.3) !important;
  border-radius: 8px !important;
  padding: 12px !important;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
textarea:focus,
select:focus {
  border-color: #ea384c !important;
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(234, 56, 76, 0.1) !important;
}

/* Calendar header styling */
.react-calendar__month-view__weekdays,
.calendar-header {
  font-weight: 600 !important;
  color: #333 !important;
}

/* Today's date highlight */
.react-calendar__tile--now {
  background-color: rgba(234, 56, 76, 0.1) !important;
  color: #ea384c !important;
  font-weight: bold !important;
}

/* Disabled dates */
.react-calendar__tile:disabled,
.disabled-date {
  color: #ccc !important;
  background-color: #f5f5f5 !important;
}

/* Success/confirmation styling */
.success-message,
.confirmation-message {
  background-color: rgba(76, 175, 80, 0.1) !important;
  border: 2px solid #4caf50 !important;
  color: #2e7d32 !important;
  border-radius: 8px !important;
  padding: 16px !important;
}

/* Loading spinner */
.loading-spinner,
.spinner {
  border-top-color: #ea384c !important;
}

/* Links */
a {
  color: #ea384c !important;
  text-decoration: none !important;
}

a:hover {
  color: #d32f2f !important;
  text-decoration: underline !important;
}

/* Calendar month/year display */
.react-calendar__navigation__label,
.month-year-display {
  font-weight: bold !important;
  color: #333 !important;
}

/* Time zone display */
.timezone-display,
.time-zone {
  color: #666 !important;
  font-size: 14px !important;
}

/* Custom checkbox styling */
input[type="checkbox"] {
  accent-color: #ea384c !important;
}

/* Radio button styling */
input[type="radio"] {
  accent-color: #ea384c !important;
}

/* Progress indicators */
.progress-bar,
.step-indicator {
  background-color: #ea384c !important;
}

/* Error messages */
.error-message,
.validation-error {
  color: #d32f2f !important;
  background-color: rgba(211, 47, 47, 0.1) !important;
  border: 1px solid #d32f2f !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
}
