// Simple script to test the n8n webhook directly
import fetch from 'node-fetch';

async function testN8nWebhook() {
  try {
    // Try both webhook URLs
    const webhookUrls = [
      'https://n8n-1-i8dz.onrender.com/webhook/aha-signup',
      'https://n8n-1-i8dz.onrender.com/webhook-aha-signup', // Try without the slash
      'https://ahasignup.vercel.app/api/webhook' // Try the Vercel webhook
    ];

    // Test data
    const testData = {
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
      company_name: 'Test Agency',
      password: 'TestPassword123!',
      // Format location data exactly as GHL API expects it
      name: 'Test Agency',
      companyId: "gPkTndcx94O3r573TOMx",
      phone: '************',
      address: '123 Main Street',
      city: 'Default City',
      state: 'Default State',
      country: 'United States',
      postalCode: '12345',
      website: 'https://example.com',
      timezone: 'US/Central',
      prospectInfo: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>'
      },
      settings: {
        allowDuplicateContact: false,
        allowDuplicateOpportunity: false,
        allowFacebookNameMerge: false,
        disableContactTimezone: false
      },
      social: {
        facebookUrl: '',
        googlePlus: '',
        linkedIn: '',
        foursquare: '',
        twitter: '',
        yelp: '',
        instagram: '',
        youtube: '',
        pinterest: '',
        blogRss: '',
        googlePlacesId: ''
      },
      // Additional user data
      role: 'admin',
      company_size: 'small',
      referral_source: 'website'
    };

    console.log('Sending test data to webhooks:', JSON.stringify(testData, null, 2));

    // Try each webhook URL
    for (const webhookUrl of webhookUrls) {
      console.log(`\nTrying webhook URL: ${webhookUrl}`);

      try {
        // Send the request to webhook
        const response = await fetch(webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(testData),
        });

        console.log('Response status:', response.status);
        console.log('Response status text:', response.statusText);

        const responseText = await response.text();
        console.log('Response body:', responseText);

        try {
          const responseJson = JSON.parse(responseText);
          console.log('Response JSON:', JSON.stringify(responseJson, null, 2));
        } catch (e) {
          console.log('Response is not valid JSON');
        }
      } catch (error) {
        console.error(`Error with webhook ${webhookUrl}:`, error.message);
      }
    }

  } catch (error) {
    console.error('Error testing n8n webhook:', error);
  }
}

// Run the test
testN8nWebhook();
