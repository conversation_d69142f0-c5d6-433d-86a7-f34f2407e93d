# AHA-Innovations Launchpad

## Project Overview

AHA-Innovations Launchpad is a modern web application that serves as an all-in-one platform for creating and managing GoHighLevel (GHL) subaccounts. It provides a seamless onboarding experience for new users, automating the process of creating GHL accounts, and connecting them to the AHA-Innovations ecosystem.

**Repository**: https://github.com/StephenLovino/easy-ghl-launchpad

## Key Features

- **Modern UI**: Glassy elements, parallax effects, and playful animations
- **Enhanced Onboarding**: Multi-step wizard for collecting user information
- **GHL Integration**: Automated creation of GoHighLevel subaccounts and users
- **Supabase Authentication**: Social login and user management
- **Edge Functions**: Serverless functions for secure API integrations
- **Responsive Design**: Mobile-friendly interface

## Architecture Overview

The application follows a modern architecture with the following components:

1. **Frontend**: React application with TypeScript, Vite, and Tailwind CSS
2. **Authentication**: Supabase Auth for user management
3. **Database**: Supabase PostgreSQL for storing user profiles and settings
4. **Edge Functions**: Serverless functions for secure API integrations with GoHighLevel
5. **Webhooks**: Fallback integration with n8n for workflow automation

## Edge Functions

Edge functions are a critical part of this application, providing secure server-side functionality without requiring a traditional backend. They handle sensitive operations like:

### 1. `create-ghl-full-account`
Creates a complete GHL account including location and user in one operation. Uses the GHL Agency API to create subaccounts under the main agency.

### 2. `check-ghl-user-exists`
Checks if a user already exists in GHL to prevent duplicate accounts.

### 3. `exchange-ghl-auth-code`
Exchanges OAuth authorization codes for access tokens.

### 4. `get-ghl-credentials`
Securely retrieves GHL API credentials without exposing them to the client.

## Prerequisites

Before running this project, you'll need:

1. **Node.js**: v16 or higher
2. **npm** or **yarn**
3. **Supabase Account**: For authentication and database
4. **GoHighLevel Agency Account**: With API access
5. **Environment Variables**: See the Environment Setup section

## Getting Started

### Installation

```sh
# Step 1: Clone the repository
git clone https://github.com/StephenLovino/easy-ghl-launchpad.git

# Step 2: Navigate to the project directory
cd easy-ghl-launchpad

# Step 3: Install dependencies
npm install

# Step 4: Start the development server
npm run dev
```

### Environment Setup

Create a `.env` file in the root directory with the following variables:

```
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# GoHighLevel API Configuration (for local development)
VITE_GHL_CLIENT_ID=your_ghl_client_id
VITE_GHL_CLIENT_SECRET=your_ghl_client_secret
VITE_GHL_AGENCY_TOKEN=your_ghl_agency_token
VITE_GHL_COMPANY_ID=your_ghl_company_id

# n8n Webhook URLs (fallback integration)
VITE_N8N_WEBHOOK_URL=your_n8n_webhook_url
VITE_N8N_TEST_WEBHOOK_URL=your_n8n_test_webhook_url
```

For production, these environment variables should be set in your hosting platform.

### Supabase Setup

1. Create a new Supabase project
2. Set up authentication providers (Email, Google, etc.)
3. Run the SQL migrations in the `supabase/migrations` directory
4. Deploy the Edge Functions in the `supabase/functions` directory

### Edge Functions Deployment

To deploy the Edge Functions to your Supabase project:

```sh
# Install Supabase CLI if you haven't already
npm install -g supabase

# Login to Supabase
supabase login

# Link your project
supabase link --project-ref your-project-ref

# Deploy all functions
supabase functions deploy

# Or deploy a specific function
supabase functions deploy create-ghl-full-account
```

## Project Structure

```
easy-ghl-launchpad/
├── public/                  # Static assets
├── src/
│   ├── components/          # React components
│   │   ├── ui/              # UI components (shadcn-ui)
│   │   ├── onboarding/      # Onboarding wizard components
│   │   └── ...
│   ├── hooks/               # Custom React hooks
│   ├── lib/                 # Utility libraries
│   ├── pages/               # Page components
│   ├── utils/               # Utility functions
│   │   ├── ghlClient.ts     # GHL API client
│   │   ├── ghlPrivateAuth.ts # GHL private integration
│   │   ├── supabaseClient.ts # Supabase client
│   │   └── ...
│   ├── App.tsx              # Main App component
│   └── main.tsx             # Entry point
├── supabase/
│   ├── functions/           # Edge Functions
│   │   ├── create-ghl-full-account/
│   │   ├── check-ghl-user-exists/
│   │   └── ...
│   └── migrations/          # Database migrations
├── .env.example             # Example environment variables
├── package.json             # Project dependencies
└── README.md                # Project documentation
```

## Key Workflows

### User Signup and Onboarding

1. User signs up with email or social login
2. User completes the enhanced onboarding wizard
3. System creates a GHL subaccount and user via Edge Function
4. User profile is updated with GHL account information
5. User is redirected to the dashboard

### GHL Account Creation

The application uses two methods for creating GHL accounts:

1. **Primary Method**: Direct API integration via Edge Functions
   - More secure as API keys are stored server-side
   - Faster and more reliable

2. **Fallback Method**: n8n webhook integration
   - Used if the primary method fails
   - Provides redundancy for critical operations

## Troubleshooting

### Common Issues

1. **Edge Function Errors**:
   - Check Supabase logs for detailed error messages
   - Verify environment variables are correctly set
   - Ensure GHL API credentials are valid

2. **Onboarding Wizard Issues**:
   - Check browser console for JavaScript errors
   - Verify user object is properly passed to components
   - Ensure form data is correctly formatted

3. **Country Field Problems**:
   - GHL API requires specific country format (2-letter code or full name)
   - Check the country conversion logic in the code

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/my-feature`
3. Commit your changes: `git commit -am 'Add new feature'`
4. Push to the branch: `git push origin feature/my-feature`
5. Submit a pull request

## Deployment

This project is configured for deployment on Vercel:

```sh
# Install Vercel CLI
npm install -g vercel

# Deploy to production
vercel --prod
```

You can also deploy to other platforms like Netlify, AWS Amplify, or GitHub Pages.

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.
