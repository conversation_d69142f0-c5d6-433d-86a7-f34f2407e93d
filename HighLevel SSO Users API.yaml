openapi: 3.0.0
paths:
  /users/search:
    get:
      operationId: search-users
      summary: Search Users
      description: Search Users
      parameters:
        - name: Authorization
          in: header
          description: Access Token
          required: true
          schema:
            type: string
            example: Bearer 9c48df2694a849b6089f9d0d3513efe
        - name: Version
          in: header
          description: API Version
          required: true
          schema:
            type: string
            enum:
              - '2021-07-28'
        - name: companyId
          required: true
          in: query
          example: 5DP41231LkQsiKESj6rh
          description: Company ID in which the search needs to be performed
          schema:
            type: string
        - name: query
          required: false
          in: query
          example: John
          description: The search term for the user is matched based on the user full name, email or phone
          schema:
            type: string
        - name: skip
          required: false
          in: query
          example: '1'
          description: No of results to be skipped before returning the result
          schema:
            default: '0'
            type: string
        - name: limit
          required: false
          in: query
          example: '10'
          description: No of results to be limited before returning the result
          schema:
            default: '25'
            type: string
        - name: locationId
          required: false
          in: query
          example: 5DP41231LkQsiKESj6rh
          description: Location ID in which the search needs to be performed
          schema:
            type: string
        - name: type
          required: false
          in: query
          example: agency
          description: Type of the users to be filtered in the search
          schema:
            type: string
        - name: role
          required: false
          in: query
          example: admin
          description: Role of the users to be filtered in the search
          schema:
            type: string
        - name: ids
          required: false
          in: query
          example: 5DP4iH6HLkQsiKESj6rh,5DP4iH6HLkQsiKESj34h
          description: List of User IDs to be filtered in the search
          schema:
            type: string
        - name: sort
          required: false
          in: query
          example: dateAdded
          description: The field on which sort is applied in which the results need to be sorted. Default is based on the first and last name
          schema:
            type: string
        - name: sortDirection
          required: false
          in: query
          example: asc
          description: The direction in which the results need to be sorted
          schema:
            type: string
        - name: enabled2waySync
          required: false
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchUserSuccessfulResponseDto'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestDTO'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnauthorizedDTO'
        '422':
          description: Unprocessable Entity
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnprocessableDTO'
      tags:
        - Search
      security:
        - Agency-Access: []
        - Location-Access: []
  /users/{userId}:
    get:
      operationId: get-user
      summary: Get User
      description: Get User
      parameters:
        - name: Authorization
          in: header
          description: Access Token
          required: true
          schema:
            type: string
            example: Bearer 9c48df2694a849b6089f9d0d3513efe
        - name: Version
          in: header
          description: API Version
          required: true
          schema:
            type: string
            enum:
              - '2021-07-28'
        - name: userId
          required: true
          in: path
          description: User Id
          example: ve9EPM428h8vShlRW1KT
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSuccessfulResponseDto'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestDTO'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnauthorizedDTO'
        '422':
          description: Unprocessable Entity
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnprocessableDTO'
      tags:
        - Users
      security:
        - Agency-Access: []
        - Location-Access: []
    put:
      operationId: update-user
      summary: Update User
      description: Update User
      parameters:
        - name: Authorization
          in: header
          description: Access Token
          required: true
          schema:
            type: string
            example: Bearer 9c48df2694a849b6089f9d0d3513efe
        - name: Version
          in: header
          description: API Version
          required: true
          schema:
            type: string
            enum:
              - '2021-07-28'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserDto'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSuccessfulResponseDto'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestDTO'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnauthorizedDTO'
        '422':
          description: Unprocessable Entity
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnprocessableDTO'
      tags:
        - Users
      security:
        - Agency-Access: []
        - Location-Access: []
    delete:
      operationId: delete-user
      summary: Delete User
      description: Delete User
      parameters:
        - name: Authorization
          in: header
          description: Access Token
          required: true
          schema:
            type: string
            example: Bearer 9c48df2694a849b6089f9d0d3513efe
        - name: Version
          in: header
          description: API Version
          required: true
          schema:
            type: string
            enum:
              - '2021-07-28'
        - name: userId
          required: true
          in: path
          description: User Id
          example: ve9EPM428h8vShlRW1KT
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteUserSuccessfulResponseDto'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestDTO'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnauthorizedDTO'
        '422':
          description: Unprocessable Entity
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnprocessableDTO'
      tags:
        - Users
      security:
        - Agency-Access: []
        - Location-Access: []
  /users/:
    get:
      operationId: get-user-by-location
      summary: Get User by Location
      description: Get User by Location
      parameters:
        - name: Authorization
          in: header
          description: Access Token
          required: true
          schema:
            type: string
            example: Bearer 9c48df2694a849b6089f9d0d3513efe
        - name: Version
          in: header
          description: API Version
          required: true
          schema:
            type: string
            enum:
              - '2021-07-28'
        - name: locationId
          required: true
          in: query
          example: ve9EPM428h8vShlRW1KT
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LocationSuccessfulResponseDto'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestDTO'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnauthorizedDTO'
      tags:
        - Users
      security:
        - Location-Access: []
    post:
      operationId: create-user
      summary: Create User
      description: Create User
      parameters:
        - name: Authorization
          in: header
          description: Access Token
          required: true
          schema:
            type: string
            example: Bearer 9c48df2694a849b6089f9d0d3513efe
        - name: Version
          in: header
          description: API Version
          required: true
          schema:
            type: string
            enum:
              - '2021-07-28'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserDto'
      responses:
        '201':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSuccessfulResponseDto'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestDTO'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnauthorizedDTO'
        '422':
          description: Unprocessable Entity
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnprocessableDTO'
      tags:
        - Users
      security:
        - Agency-Access: []
        - Location-Access: []
info:
  title: Users API
  description: Documentation for users API
  version: '1.0'
  contact: {}
tags:
  - name: Users
    description: Documentation for users API
  - name: Search
    description: Documentation for users API
servers:
  - url: https://services.leadconnectorhq.com
components:
  securitySchemes:
    bearer:
      scheme: bearer
      bearerFormat: JWT
      name: Authorization
      in: header
      description: Use the Access Token generated with user type as Sub-Account (OR) Private Integration Token of Sub-Account.
      type: http
    Location-Access:
      scheme: bearer
      bearerFormat: JWT
      name: Authorization
      in: header
      description: Use the Access Token generated with user type as Sub-Account (OR) Private Integration Token of Sub-Account.
      type: http
    Agency-Access:
      scheme: bearer
      bearerFormat: JWT
      name: Authorization
      in: header
      description: Use the Access Token generated with user type as Agency (OR) Private Integration Token of Agency.
      type: http
  schemas:
    BadRequestDTO:
      type: object
      properties:
        statusCode:
          type: number
          example: 400
        message:
          type: string
          example: Bad Request
    UnauthorizedDTO:
      type: object
      properties:
        statusCode:
          type: number
          example: 401
        message:
          type: string
          example: 'Invalid token: access token is invalid'
        error:
          type: string
          example: Unauthorized
    PermissionsDto:
      type: object
      properties:
        campaignsEnabled:
          type: boolean
          example: true
          default: true
        campaignsReadOnly:
          type: boolean
          example: false
          default: false
        contactsEnabled:
          type: boolean
          example: true
          default: true
        workflowsEnabled:
          type: boolean
          example: true
          default: true
        workflowsReadOnly:
          type: boolean
          example: true
          default: false
        triggersEnabled:
          type: boolean
          example: true
          default: true
        funnelsEnabled:
          type: boolean
          example: true
          default: true
        websitesEnabled:
          type: boolean
          example: false
          default: false
        opportunitiesEnabled:
          type: boolean
          example: true
          default: true
        dashboardStatsEnabled:
          type: boolean
          example: true
          default: true
        bulkRequestsEnabled:
          type: boolean
          example: true
          default: true
        appointmentsEnabled:
          type: boolean
          example: true
          default: true
        reviewsEnabled:
          type: boolean
          example: true
          default: true
        onlineListingsEnabled:
          type: boolean
          example: true
          default: true
        phoneCallEnabled:
          type: boolean
          example: true
          default: true
        conversationsEnabled:
          type: boolean
          example: true
          default: true
        assignedDataOnly:
          type: boolean
          example: false
          default: false
        adwordsReportingEnabled:
          type: boolean
          example: false
          default: false
        membershipEnabled:
          type: boolean
          example: false
          default: false
        facebookAdsReportingEnabled:
          type: boolean
          example: false
          default: false
        attributionsReportingEnabled:
          type: boolean
          example: false
          default: false
        settingsEnabled:
          type: boolean
          example: true
          default: true
        tagsEnabled:
          type: boolean
          example: true
          default: true
        leadValueEnabled:
          type: boolean
          example: true
          default: true
        marketingEnabled:
          type: boolean
          example: true
          default: true
        agentReportingEnabled:
          type: boolean
          example: true
          default: true
        botService:
          type: boolean
          example: false
          default: false
        socialPlanner:
          type: boolean
          example: true
          default: true
        bloggingEnabled:
          type: boolean
          example: true
          default: true
        invoiceEnabled:
          type: boolean
          example: true
          default: true
        affiliateManagerEnabled:
          type: boolean
          example: true
          default: true
        contentAiEnabled:
          type: boolean
          example: true
          default: true
        refundsEnabled:
          type: boolean
          example: true
          default: true
        recordPaymentEnabled:
          type: boolean
          example: true
          default: true
        cancelSubscriptionEnabled:
          type: boolean
          example: true
          default: true
        paymentsEnabled:
          type: boolean
          example: true
          default: true
        communitiesEnabled:
          type: boolean
          example: true
          default: true
        exportPaymentsEnabled:
          type: boolean
          example: true
          default: true
    RoleSchema:
      type: object
      properties:
        type:
          type: string
          example: account
        role:
          type: string
          example: admin
        locationIds:
          example:
            - ve9EPM428h8vShlRW1KT
          type: array
          items:
            type: string
    UserSchema:
      type: object
      properties:
        id:
          type: string
          example: 0IHuJvc2ofPAAA8GzTRi
        name:
          type: string
          example: John Deo
        firstName:
          type: string
          example: John
        lastName:
          type: string
          example: Deo
        email:
          type: string
          example: <EMAIL>
        phone:
          type: string
          example: ******-868-8888
        extension:
          type: string
          example: ''
        permissions:
          $ref: '#/components/schemas/PermissionsDto'
        roles:
          $ref: '#/components/schemas/RoleSchema'
        deleted:
          type: boolean
          example: false
        externalUserId:
          type: string
          example: '1234567890'
          required: false
    SearchUserSuccessfulResponseDto:
      type: object
      properties:
        users:
          type: array
          items:
            $ref: '#/components/schemas/UserSchema'
        count:
          type: number
          example: 1231
    UnprocessableDTO:
      type: object
      properties:
        statusCode:
          type: number
          example: 422
        message:
          example:
            - Unprocessable Entity
          type: array
          items:
            type: string
        error:
          type: string
          example: Unprocessable Entity
    LocationSuccessfulResponseDto:
      type: object
      properties:
        users:
          type: array
          items:
            $ref: '#/components/schemas/UserSchema'
    UserSuccessfulResponseDto:
      type: object
      properties:
        id:
          type: string
          example: 0IHuJvc2ofPAAA8GzTRi
        name:
          type: string
          example: John Deo
        firstName:
          type: string
          example: John
        lastName:
          type: string
          example: Deo
        email:
          type: string
          example: <EMAIL>
        phone:
          type: string
          example: ******-868-8888
        extension:
          type: string
          example: ''
        permissions:
          $ref: '#/components/schemas/PermissionsDto'
        roles:
          $ref: '#/components/schemas/RoleSchema'
        externalUserId:
          type: string
          example: '1234567890'
          required: false
    CreateUserDto:
      type: object
      properties:
        companyId:
          type: string
          example: ve9EPM428h8vShlRW1KT
        firstName:
          type: string
          example: John
        lastName:
          type: string
          example: Deo
        email:
          type: string
          example: <EMAIL>
        password:
          type: string
          example: '*******'
        phone:
          type: string
          example: '+***********'
        type:
          type: string
          example: account
        role:
          type: string
          example: admin
        locationIds:
          example:
            - C2QujeCh8ZnC7al2InWR
          type: array
          items:
            type: string
        permissions:
          $ref: '#/components/schemas/PermissionsDto'
        scopes:
          type: array
          example:
            - contacts.write
            - campaigns.readonly
          description: Scopes allowed for users. If passed empty all the scopes will be get disabled
          items:
            type: string
            enum:
              - campaigns.readonly
              - campaigns.write
              - calendars/events.write
              - calendars/events.readonly
              - contacts.write
              - contacts/bulkActions.write
              - workflows.readonly
              - workflows.write
              - triggers.write
              - funnels.write
              - websites.write
              - opportunities.write
              - opportunities/leadValue.readonly
              - reporting/phone.readonly
              - reporting/adwords.readonly
              - reporting/facebookAds.readonly
              - reporting/attributions.readonly
              - reporting/agent.readonly
              - payments.write
              - payments/refunds.write
              - payments/records.write
              - payments/exports.write
              - payments/subscriptionsCancel.write
              - invoices.write
              - invoices.readonly
              - invoices/schedule.readonly
              - invoices/schedule.write
              - invoices/template.readonly
              - invoices/template.write
              - reputation/review.write
              - reputation/listing.write
              - conversations.write
              - conversations.readonly
              - conversations/message.readonly
              - conversations/message.write
              - contentAI.write
              - dashboard/stats.readonly
              - locations/tags.write
              - locations/tags.readonly
              - marketing.write
              - eliza.write
              - settings.write
              - socialplanner/post.write
              - marketing/affiliate.write
              - blogs.write
              - membership.write
              - communities.write
              - certificates.write
              - certificates.readonly
              - adPublishing.write
              - adPublishing.write
              - adPublishing.readonly
              - adPublishing.readonly
        scopesAssignedToOnly:
          type: array
          example:
            - contacts.write
            - campaigns.readonly
          description: Assigned Scopes allowed for users. If passed empty all the assigned scopes will be get disabled
          items:
            type: string
            enum:
              - campaigns.readonly
              - campaigns.write
              - calendars/events.write
              - calendars/events.readonly
              - contacts.write
              - contacts/bulkActions.write
              - workflows.readonly
              - workflows.write
              - triggers.write
              - funnels.write
              - websites.write
              - opportunities.write
              - opportunities/leadValue.readonly
              - reporting/phone.readonly
              - reporting/adwords.readonly
              - reporting/facebookAds.readonly
              - reporting/attributions.readonly
              - reporting/agent.readonly
              - payments.write
              - payments/refunds.write
              - payments/records.write
              - payments/exports.write
              - payments/subscriptionsCancel.write
              - invoices.write
              - invoices.readonly
              - invoices/schedule.readonly
              - invoices/schedule.write
              - invoices/template.readonly
              - invoices/template.write
              - reputation/review.write
              - reputation/listing.write
              - conversations.write
              - conversations.readonly
              - conversations/message.readonly
              - conversations/message.write
              - contentAI.write
              - dashboard/stats.readonly
              - locations/tags.write
              - locations/tags.readonly
              - marketing.write
              - eliza.write
              - settings.write
              - socialplanner/post.write
              - marketing/affiliate.write
              - blogs.write
              - membership.write
              - communities.write
              - certificates.write
              - certificates.readonly
              - adPublishing.write
              - adPublishing.write
              - adPublishing.readonly
              - adPublishing.readonly
        profilePhoto:
          type: string
          example: https://img.png
        externalUserId:
          type: string
          example: '1234567890'
          required: false
      required:
        - companyId
        - firstName
        - lastName
        - email
        - password
        - type
        - role
        - locationIds
        - permissions
    UpdateUserDto:
      type: object
      properties:
        firstName:
          type: string
          example: John
        lastName:
          type: string
          example: Deo
        email:
          type: string
          example: <EMAIL>
        emailChangeOTP:
          type: string
          description: OTP to change the email ID of the user
          example: '191344'
        password:
          type: string
          example: '*******'
        phone:
          type: string
          example: '+***********'
        type:
          type: string
          example: account
        role:
          type: string
          example: admin
        companyId:
          type: string
          description: Company/Agency Id. Required for Agency Level access
          example: UAXssdawIWAWD
        locationIds:
          example:
            - C2QujeCh8ZnC7al2InWR
          type: array
          items:
            type: string
        permissions:
          $ref: '#/components/schemas/PermissionsDto'
        scopes:
          type: array
          example:
            - contacts.write
            - campaigns.readonly
          description: Scopes allowed for users. If passed empty all the scopes will be get disabled
          items:
            type: string
            enum:
              - campaigns.readonly
              - campaigns.write
              - calendars/events.write
              - calendars/events.readonly
              - contacts.write
              - contacts/bulkActions.write
              - workflows.readonly
              - workflows.write
              - triggers.write
              - funnels.write
              - websites.write
              - opportunities.write
              - opportunities/leadValue.readonly
              - reporting/phone.readonly
              - reporting/adwords.readonly
              - reporting/facebookAds.readonly
              - reporting/attributions.readonly
              - reporting/agent.readonly
              - payments.write
              - payments/refunds.write
              - payments/records.write
              - payments/exports.write
              - payments/subscriptionsCancel.write
              - invoices.write
              - invoices.readonly
              - invoices/schedule.readonly
              - invoices/schedule.write
              - invoices/template.readonly
              - invoices/template.write
              - reputation/review.write
              - reputation/listing.write
              - conversations.write
              - conversations.readonly
              - conversations/message.readonly
              - conversations/message.write
              - contentAI.write
              - dashboard/stats.readonly
              - locations/tags.write
              - locations/tags.readonly
              - marketing.write
              - eliza.write
              - settings.write
              - socialplanner/post.write
              - marketing/affiliate.write
              - blogs.write
              - membership.write
              - communities.write
              - certificates.write
              - certificates.readonly
              - adPublishing.write
              - adPublishing.write
              - adPublishing.readonly
              - adPublishing.readonly
        scopesAssignedToOnly:
          type: array
          example:
            - contacts.write
            - campaigns.readonly
          description: Assigned Scopes allowed for users. If passed empty all the assigned scopes will be get disabled
          items:
            type: string
            enum:
              - campaigns.readonly
              - campaigns.write
              - calendars/events.write
              - calendars/events.readonly
              - contacts.write
              - contacts/bulkActions.write
              - workflows.readonly
              - workflows.write
              - triggers.write
              - funnels.write
              - websites.write
              - opportunities.write
              - opportunities/leadValue.readonly
              - reporting/phone.readonly
              - reporting/adwords.readonly
              - reporting/facebookAds.readonly
              - reporting/attributions.readonly
              - reporting/agent.readonly
              - payments.write
              - payments/refunds.write
              - payments/records.write
              - payments/exports.write
              - payments/subscriptionsCancel.write
              - invoices.write
              - invoices.readonly
              - invoices/schedule.readonly
              - invoices/schedule.write
              - invoices/template.readonly
              - invoices/template.write
              - reputation/review.write
              - reputation/listing.write
              - conversations.write
              - conversations.readonly
              - conversations/message.readonly
              - conversations/message.write
              - contentAI.write
              - dashboard/stats.readonly
              - locations/tags.write
              - locations/tags.readonly
              - marketing.write
              - eliza.write
              - settings.write
              - socialplanner/post.write
              - marketing/affiliate.write
              - blogs.write
              - membership.write
              - communities.write
              - certificates.write
              - certificates.readonly
              - adPublishing.write
              - adPublishing.write
              - adPublishing.readonly
              - adPublishing.readonly
        profilePhoto:
          type: string
          example: https://img.png
        externalUserId:
          type: string
          example: '1234567890'
          required: false
    DeleteUserSuccessfulResponseDto:
      type: object
      properties:
        succeded:
          type: boolean
          example: true
        message:
          type: string
          example: Queued deleting user with e-mail <EMAIL> and name John Deo. Will take effect in a few minutes.
