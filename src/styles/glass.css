/* Glass UI Effects */

/* Base glass effect */
.glass {
  background: rgba(255, 255, 255, 0.07);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
}

/* Glass with more opacity */
.glass-medium {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
}

/* Glass with even more opacity */
.glass-heavy {
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.25);
}

/* Dark glass effect */
.glass-dark {
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.3);
}

/* Colored glass effects */
.glass-red {
  background: rgba(234, 56, 76, 0.12);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(234, 56, 76, 0.2);
  box-shadow: 0 8px 32px 0 rgba(234, 56, 76, 0.25);
}

.glass-blue {
  background: rgba(59, 130, 246, 0.08);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.15);
  box-shadow: 0 8px 32px 0 rgba(59, 130, 246, 0.15);
}

.glass-purple {
  background: rgba(139, 92, 246, 0.08);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(139, 92, 246, 0.15);
  box-shadow: 0 8px 32px 0 rgba(139, 92, 246, 0.15);
}

/* Hover effects */
.glass-hover {
  transition: all 0.3s ease;
}

.glass-hover:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px 0 rgba(0, 0, 0, 0.25);
}

.glass-red-hover:hover {
  background: rgba(234, 56, 76, 0.12);
  border-color: rgba(234, 56, 76, 0.25);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px 0 rgba(234, 56, 76, 0.2);
}

.glass-blue-hover:hover {
  background: rgba(59, 130, 246, 0.12);
  border-color: rgba(59, 130, 246, 0.25);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px 0 rgba(59, 130, 246, 0.2);
}

.glass-purple-hover:hover {
  background: rgba(139, 92, 246, 0.12);
  border-color: rgba(139, 92, 246, 0.25);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px 0 rgba(139, 92, 246, 0.2);
}

/* Glass card with inner glow */
.glass-card-glow {
  position: relative;
  overflow: hidden;
}

.glass-card-glow::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.glass-card-glow:hover::before {
  opacity: 1;
}

/* Glass with border glow */
.glass-border-glow {
  position: relative;
}

.glass-border-glow::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

/* Red border glow */
.glass-border-glow-red::after {
  background: linear-gradient(
    135deg,
    rgba(234, 56, 76, 0.5) 0%,
    rgba(234, 56, 76, 0.2) 50%,
    rgba(234, 56, 76, 0) 100%
  );
}

/* Blue border glow */
.glass-border-glow-blue::after {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.5) 0%,
    rgba(59, 130, 246, 0.2) 50%,
    rgba(59, 130, 246, 0) 100%
  );
}

/* Purple border glow */
.glass-border-glow-purple::after {
  background: linear-gradient(
    135deg,
    rgba(139, 92, 246, 0.5) 0%,
    rgba(139, 92, 246, 0.2) 50%,
    rgba(139, 92, 246, 0) 100%
  );
}

/* Frost effect for glass cards */
.glass-frost {
  position: relative;
  overflow: hidden;
}

.glass-frost::before {
  content: '';
  position: absolute;
  inset: 0;
  background: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  opacity: 0.02;
  z-index: -1;
  pointer-events: none;
}

/* Glass with light reflection */
.glass-reflection {
  position: relative;
  overflow: hidden;
}

.glass-reflection::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: skewX(-25deg);
  transition: all 0.75s ease;
}

.glass-reflection:hover::before {
  left: 150%;
}
