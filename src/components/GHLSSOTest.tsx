// Test component for GHL SSO functionality
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import GHLSSOService from '../services/ghlSSOService';

const GHLSSOTest = () => {
  const [loading, setLoading] = useState(false);
  const [testEmail, setTestEmail] = useState('');
  const [testResults, setTestResults] = useState<any[]>([]);
  const { toast } = useToast();

  const addTestResult = (test: string, success: boolean, data?: any, error?: string) => {
    const result = {
      test,
      success,
      data,
      error,
      timestamp: new Date().toISOString()
    };
    setTestResults(prev => [...prev, result]);
    return result;
  };

  const testSearchUsers = async () => {
    try {
      setLoading(true);
      console.log('Testing GHL SSO user search...');

      const users = await GHLSSOService.searchUsers({ 
        email: testEmail,
        limit: 5 
      });

      addTestResult('Search Users', true, { 
        count: users.length, 
        users: users.slice(0, 2) // Show first 2 users only
      });

      toast({
        title: "Search Test Successful",
        description: `Found ${users.length} users`,
      });

    } catch (error: any) {
      console.error('Search test failed:', error);
      addTestResult('Search Users', false, null, error.message);
      
      toast({
        title: "Search Test Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const testCheckUserExists = async () => {
    try {
      setLoading(true);
      console.log('Testing GHL SSO user existence check...');

      const result = await GHLSSOService.checkUserExists(testEmail);

      addTestResult('Check User Exists', true, result);

      toast({
        title: "Existence Check Successful",
        description: result.exists ? "User exists in AHA-Innovations" : "User does not exist in AHA-Innovations",
      });

    } catch (error: any) {
      console.error('Existence check test failed:', error);
      addTestResult('Check User Exists', false, null, error.message);
      
      toast({
        title: "Existence Check Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const testCreateUser = async () => {
    if (!testEmail.trim()) {
      toast({
        title: "Email Required",
        description: "Please enter an email address for testing",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      console.log('Testing GHL SSO user creation...');

      // Generate test data
      const emailParts = testEmail.split('@');
      const firstName = emailParts[0].charAt(0).toUpperCase() + emailParts[0].slice(1);
      
      const testUserData = {
        firstName: firstName,
        lastName: 'TestUser',
        email: testEmail,
        phone: '+**********',
        locationIds: ['test-location-id'], // This would be a real location ID
        externalUserId: 'test-external-' + Date.now(),
        type: 'account' as const,
        role: 'admin' as const
      };

      const user = await GHLSSOService.createUser(testUserData);

      addTestResult('Create User', true, { 
        userId: user.id,
        email: user.email,
        externalUserId: testUserData.externalUserId
      });

      toast({
        title: "User Creation Successful",
        description: `Created user: ${user.email}`,
      });

    } catch (error: any) {
      console.error('User creation test failed:', error);
      addTestResult('Create User', false, null, error.message);
      
      toast({
        title: "User Creation Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="space-y-6">
      <Card className="bg-gray-900 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white">AHA-Innovations SSO Service Test</CardTitle>
          <CardDescription className="text-gray-400">
            Test the AHA-Innovations SSO User Management functionality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              type="email"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
              placeholder="Enter test email address"
              className="bg-gray-800 border-gray-600 text-white flex-1"
            />
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={testSearchUsers}
              disabled={loading}
              variant="outline"
              className="border-gray-600 text-white hover:bg-gray-800"
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Test Search
            </Button>
            
            <Button
              onClick={testCheckUserExists}
              disabled={loading}
              variant="outline"
              className="border-gray-600 text-white hover:bg-gray-800"
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Test Exists Check
            </Button>
            
            <Button
              onClick={testCreateUser}
              disabled={loading || !testEmail.trim()}
              variant="outline"
              className="border-gray-600 text-white hover:bg-gray-800"
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Test Create User
            </Button>
            
            <Button
              onClick={clearResults}
              variant="outline"
              className="border-gray-600 text-white hover:bg-gray-800"
            >
              Clear Results
            </Button>
          </div>
        </CardContent>
      </Card>

      {testResults.length > 0 && (
        <Card className="bg-gray-900 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="border border-gray-700 rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-2">
                    {result.success ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span className="text-white font-medium">{result.test}</span>
                    <span className="text-xs text-gray-400">
                      {new Date(result.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  
                  {result.success && result.data && (
                    <div className="bg-gray-800 rounded p-2 mt-2">
                      <pre className="text-xs text-green-400 overflow-x-auto">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </div>
                  )}
                  
                  {!result.success && result.error && (
                    <div className="bg-red-900/20 border border-red-800 rounded p-2 mt-2">
                      <p className="text-xs text-red-400">{result.error}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default GHLSSOTest;
