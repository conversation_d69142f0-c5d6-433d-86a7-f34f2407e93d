import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { supabase } from '@/utils/supabaseClient';
import { createGHLAccountOnSignUp, updateSupabaseProfileWithGHL, authenticateGHLUser } from '@/utils/ghlPrivateAuth';
import { useToast } from '@/hooks/use-toast';

const AuthCallback = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { toast } = useToast();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        console.log('AuthCallback: Starting auth callback process');
        console.log('AuthCallback: Current URL:', window.location.href);
        console.log('AuthCallback: Search params:', searchParams.toString());
        console.log('AuthCallback: Hash:', window.location.hash);

        // Check what parameters we have
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        console.log('AuthCallback: URL parameters:', { code: !!code, state: !!state });

        // Handle the OAuth callback by exchanging the code for a session
        let data, error;

        // Try the standard exchange first
        const exchangeResult = await supabase.auth.exchangeCodeForSession(window.location.href);
        data = exchangeResult.data;
        error = exchangeResult.error;

        // If that fails, try getting the current session
        if (error) {
          console.error('AuthCallback: Exchange code error:', error);
          console.log('AuthCallback: Trying to get current session instead...');

          const sessionResult = await supabase.auth.getSession();
          if (sessionResult.data.session) {
            console.log('AuthCallback: Found existing session');
            data = { user: sessionResult.data.session.user, session: sessionResult.data.session };
            error = null;
          } else {
            console.error('AuthCallback: No session found either');
            throw error;
          }
        }

        console.log('AuthCallback: Auth callback successful:', data);

        if (data.user && data.user.email) {
          const userMetadata = data.user.user_metadata;
          const email = data.user.email;
          const fullName = userMetadata?.full_name || userMetadata?.name || email?.split('@')[0] || 'User';

          // Check if user already exists in our GHL system
          console.log('AuthCallback: Checking if user exists in GHL:', email);
          console.log('AuthCallback: User metadata:', userMetadata);
          let ghlAuthResult;
          try {
            ghlAuthResult = await authenticateGHLUser(email);
            console.log('AuthCallback: GHL auth result:', ghlAuthResult);
            console.log('AuthCallback: GHL auth success:', ghlAuthResult.success);
            console.log('AuthCallback: GHL locationId:', ghlAuthResult.locationId);
          } catch (ghlError) {
            console.error('AuthCallback: Error checking GHL user:', ghlError);
            // If GHL check fails, treat as new user
            ghlAuthResult = { success: false, message: 'GHL check failed' };
          }

          if (ghlAuthResult.success) {
            // User exists - redirect to GHL dashboard
            console.log('AuthCallback: User exists, redirecting to GHL dashboard');
            console.log('AuthCallback: GHL locationId for redirect:', ghlAuthResult.locationId);
            console.log('AuthCallback: Production deployment check - v1.1');
            toast({
              title: "Found your account!",
              description: "Redirecting to your GHL dashboard...",
            });

            // Update Supabase profile with existing GHL data
            if (ghlAuthResult.locationId) {
              try {
                await updateSupabaseProfileWithGHL(data.user.id, {
                  locationId: ghlAuthResult.locationId,
                  userId: ghlAuthResult.userId
                });
              } catch (updateError) {
                console.error('AuthCallback: Error updating profile:', updateError);
              }
            }

            setTimeout(() => {
              console.log('AuthCallback: Redirecting to app.aha-innovations.com with email prefill and auto-signin trigger');
              const encodedEmail = encodeURIComponent(email);
              // Add auto_google parameter to trigger automatic Google sign-in
              window.location.href = `https://app.aha-innovations.com/?email=${encodedEmail}&auto_google=true&from_signup=true`;
            }, 1500);
          } else {
            // User doesn't exist - create new GHL account
            console.log('AuthCallback: User does not exist, creating new GHL account');
            toast({
              title: "Creating your account...",
              description: "Setting up your GHL account and enabling features...",
            });

            try {
              // Parse full name into first and last name
              const nameParts = fullName.split(' ');
              const firstName = nameParts[0] || 'User';
              const lastName = nameParts.slice(1).join(' ') || '';

              console.log('AuthCallback: Creating GHL account with data:', {
                email,
                firstName,
                lastName,
                companyName: `${firstName}'s Business`
              });

              const ghlResult = await createGHLAccountOnSignUp({
                email: email,
                firstName,
                lastName,
                companyName: `${firstName}'s Business`
              });

              console.log('AuthCallback: GHL account creation result:', ghlResult);

              if (ghlResult.success) {
                await updateSupabaseProfileWithGHL(data.user.id, {
                  locationId: ghlResult.locationId,
                  userId: ghlResult.userId
                });

                // Create GHL contact for tracking/marketing
                try {
                  const contactResponse = await supabase.functions.invoke('create-ghl-contact', {
                    body: {
                      locationId: ghlResult.locationId || 'LL7TmGrkL72EOf8O0FKA',
                      email: email,
                      firstName: firstName,
                      lastName: lastName || 'User',
                      companyName: `${firstName}'s Business`,
                      phone: `+1${Math.floor(Math.random() * **********) + **********}`, // Generate fake phone
                      city: 'Unknown',
                      state: 'Unknown',
                      country: 'US'
                    }
                  });

                  if (!contactResponse.error) {
                    console.log('GHL contact created successfully for Google signup:', contactResponse.data);
                  } else {
                    console.warn('Contact creation failed for Google signup:', contactResponse.error);
                  }
                } catch (contactError) {
                  console.warn('Contact creation error for Google signup:', contactError);
                }

                toast({
                  title: "Account Created Successfully!",
                  description: "Now choose your plan to continue.",
                });

                // Redirect to plan selection page with user info
                setTimeout(() => {
                  navigate('/plan-selection', {
                    state: {
                      userInfo: {
                        email,
                        name: fullName
                      }
                    },
                    replace: true
                  });
                }, 1500);
              } else {
                toast({
                  title: "Account Created",
                  description: "Now choose your plan to continue.",
                  variant: "warning",
                });

                setTimeout(() => {
                  navigate('/plan-selection', {
                    state: {
                      userInfo: {
                        email,
                        name: fullName
                      }
                    },
                    replace: true
                  });
                }, 2000);
              }
            } catch (ghlError) {
              console.error('GHL account creation error:', ghlError);
              toast({
                title: "Account Created",
                description: "Now choose your plan to continue. Please contact support if you need assistance.",
                variant: "warning",
              });

              setTimeout(() => {
                navigate('/plan-selection', {
                  state: {
                    userInfo: {
                      email,
                      name: fullName
                    }
                  },
                  replace: true
                });
              }, 2000);
            }
          }
        } else {
          // No user data - redirect to home
          const redirectTo = searchParams.get('redirect_to') || '/';
          setTimeout(() => {
            navigate(redirectTo, { replace: true });
          }, 100);
        }

      } catch (err: any) {
        console.error('Auth callback error:', err);
        setError(err.message);

        // Redirect to home on error
        setTimeout(() => {
          navigate('/', { replace: true });
        }, 3000);
      } finally {
        setLoading(false);
      }
    };

    handleAuthCallback();
  }, [navigate, searchParams, toast]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500 mx-auto mb-4"></div>
          <p className="text-white">Completing authentication...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <p className="text-white mb-4">Authentication error:</p>
          <p className="text-red-400 mb-4">{error}</p>
          <p className="text-gray-400">Redirecting to home...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
      <p className="text-white">Authentication successful. Redirecting...</p>
    </div>
  );
};

export default AuthCallback;
