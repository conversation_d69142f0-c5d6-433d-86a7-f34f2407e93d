
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { generateGHLOAuthURL } from '@/utils/ghlClient';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';

interface ConnectGHLButtonProps {
  className?: string;
  useDirectRedirect?: boolean;
}

const ConnectGHLButton = ({ className, useDirectRedirect = false }: ConnectGHLButtonProps) => {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleConnect = async () => {
    try {
      setLoading(true);
      
      if (useDirectRedirect) {
        // Generate OAuth URL and redirect directly
        const oauthURL = await generateGHLOAuthURL();
        window.location.href = oauthURL;
      } else {
        // Navigate to the initiate page which will handle the redirect
        navigate('/initiate');
      }
    } catch (error: any) {
      console.error('Error connecting to GHL:', error);
      toast({
        title: "Connection Error",
        description: error.message || "Failed to connect to AHA-Innovations. Please try again.",
        variant: "destructive",
      });
      setLoading(false);
    }
  };

  return (
    <Button 
      onClick={handleConnect} 
      disabled={loading}
      className={className}
    >
      {loading ? "Connecting..." : "Connect AHA-Innovations"}
    </Button>
  );
};

export default ConnectGHLButton;
