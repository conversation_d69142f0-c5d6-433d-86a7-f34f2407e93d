import React, { useState } from 'react';
import axios from 'axios';
import { motion } from 'framer-motion';

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  companyName: string;
  companySize: string;
  goal: string;
}

const CustomSignUpForm = () => {
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    companyName: '',
    companySize: '',
    goal: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    // Validate company name
    if (!formData.companyName.trim()) {
      setError('Company name is required. This will be your business name in your account.');
      setIsSubmitting(false);
      return;
    }

    try {
      // Format data for n8n webhook
      const webhookData = {
        contact: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone
        },
        company: {
          name: formData.companyName,
          size: formData.companySize
        },
        goal: formData.goal,
        source: 'website_signup',
        timestamp: new Date().toISOString()
      };

      console.log('Submitting data to proxy webhook:', webhookData);

      // Send data to our proxy endpoint instead of directly to n8n
      // This avoids CORS issues
      const response = await axios.post('/api/proxy-webhook', webhookData);

      if (response.status === 200) {
        console.log('Form submission successful:', response.data);
        setSuccess(true);
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          companyName: '',
          companySize: '',
          goal: ''
        });
      } else {
        console.error('Form submission returned non-200 status:', response.status);
        setError('Something went wrong. Please try again.');
      }
    } catch (err) {
      console.error('Form submission error:', err);
      setError('Failed to submit form. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const companySizeOptions = [
    { value: '', label: 'Select company size' },
    { value: 'solo', label: 'Solo entrepreneur' },
    { value: '2-10', label: '2-10 employees' },
    { value: '11-50', label: '11-50 employees' },
    { value: '51-200', label: '51-200 employees' },
    { value: '201+', label: '201+ employees' }
  ];

  if (success) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="bg-aha-dark/70 backdrop-blur-xl p-8 rounded-xl border border-aha-red/30 text-center shadow-2xl relative overflow-hidden"
      >
        {/* Decorative elements */}
        <div className="absolute -top-20 -right-20 w-40 h-40 bg-aha-red/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-aha-red/20 rounded-full blur-3xl"></div>

        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5, type: "spring" }}
          className="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6 border border-green-500/30"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </motion.div>

        <motion.h3
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="text-3xl font-bold text-white mb-2"
        >
          Thank You!
        </motion.h3>

        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="text-gray-300 mb-3"
        >
          Your free account is being created. We'll send you an email with next steps.
        </motion.p>

        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.5 }}
          className="text-gray-400 text-sm mb-8"
        >
          Get ready to automate your business with AHA Innovations!
        </motion.p>

        <motion.a
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.5 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          href="https://app.aha-innovations.com"
          className="inline-block px-8 py-4 bg-aha-red hover:bg-aha-darkred text-white font-medium rounded-lg transition-colors shadow-lg shadow-aha-red/20"
        >
          Go to Dashboard
        </motion.a>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="bg-aha-dark/70 backdrop-blur-xl p-8 rounded-xl border border-gray-800/50 shadow-2xl relative overflow-hidden"
    >
      {/* Decorative elements */}
      <div className="absolute -top-20 -right-20 w-40 h-40 bg-aha-red/20 rounded-full blur-3xl"></div>
      <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-aha-red/20 rounded-full blur-3xl"></div>
      <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-10 z-0"></div>

      <div className="relative z-10">
        <motion.div
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="text-center mb-1"
        >
          <h3 className="text-2xl font-bold text-white inline-flex items-center gap-2">
            Create Your AHA Account
            <span className="bg-aha-red/90 text-white text-xs font-medium px-2 py-0.5 rounded-full">Free</span>
          </h3>
        </motion.div>

        <motion.p
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="text-gray-300 text-sm text-center mb-6"
        >
          Fill out the form below to get started
        </motion.p>

        {error && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-500/20 border border-red-500/50 text-red-100 px-4 py-3 rounded-lg mb-6"
          >
            {error}
          </motion.div>
        )}

        <form onSubmit={handleSubmit} className="space-y-5">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <label htmlFor="firstName" className="block text-white mb-1 text-sm font-medium">First Name *</label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                required
                className="w-full bg-aha-dark/80 border border-gray-700 rounded-lg px-3 py-3 sm:px-4 text-white focus:outline-none focus:ring-2 focus:ring-aha-red/50 focus:border-aha-red/50 transition-all text-sm sm:text-base"
                placeholder="First Name"
              />
            </motion.div>

            <motion.div
              initial={{ x: 20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <label htmlFor="lastName" className="block text-white mb-1 text-sm font-medium">Last Name *</label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                required
                className="w-full bg-aha-dark/80 border border-gray-700 rounded-lg px-3 py-3 sm:px-4 text-white focus:outline-none focus:ring-2 focus:ring-aha-red/50 focus:border-aha-red/50 transition-all text-sm sm:text-base"
                placeholder="Last Name"
              />
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <label htmlFor="email" className="block text-white mb-1 text-sm font-medium">Email *</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="w-full bg-aha-dark/80 border border-gray-700 rounded-lg px-3 py-3 sm:px-4 text-white focus:outline-none focus:ring-2 focus:ring-aha-red/50 focus:border-aha-red/50 transition-all text-sm sm:text-base"
                placeholder="Email"
              />
            </motion.div>

            <motion.div
              initial={{ x: 20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <label htmlFor="phone" className="block text-white mb-1 text-sm font-medium">Phone *</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                required
                className="w-full bg-aha-dark/80 border border-gray-700 rounded-lg px-3 py-3 sm:px-4 text-white focus:outline-none focus:ring-2 focus:ring-aha-red/50 focus:border-aha-red/50 transition-all text-sm sm:text-base"
                placeholder="Phone"
              />
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
            <motion.div
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <label htmlFor="companyName" className="block text-white mb-1 text-sm font-medium">Company Name *</label>
              <input
                type="text"
                id="companyName"
                name="companyName"
                value={formData.companyName}
                onChange={handleChange}
                required
                className="w-full bg-aha-dark/80 border border-gray-700 rounded-lg px-3 py-3 sm:px-4 text-white focus:outline-none focus:ring-2 focus:ring-aha-red/50 focus:border-aha-red/50 transition-all text-sm sm:text-base"
                placeholder="Company Name"
              />
              <p className="text-xs text-gray-400 mt-1">This will be your business name in your account</p>
            </motion.div>

            <motion.div
              initial={{ x: 20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <label htmlFor="companySize" className="block text-white mb-1 text-sm font-medium">Company Size</label>
              <select
                id="companySize"
                name="companySize"
                value={formData.companySize}
                onChange={handleChange}
                className="w-full bg-aha-dark/80 border border-gray-700 rounded-lg px-3 py-3 sm:px-4 text-white focus:outline-none focus:ring-2 focus:ring-aha-red/50 focus:border-aha-red/50 transition-all appearance-none text-sm sm:text-base"
                style={{ backgroundImage: "url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\")", backgroundPosition: "right 0.5rem center", backgroundRepeat: "no-repeat", backgroundSize: "1.5em 1.5em", paddingRight: "2.5rem" }}
              >
                {companySizeOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </motion.div>
          </div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <label htmlFor="goal" className="block text-white mb-1 text-sm font-medium">What do you want to achieve? *</label>
            <textarea
              id="goal"
              name="goal"
              value={formData.goal}
              onChange={handleChange}
              required
              className="w-full bg-aha-dark/80 border border-gray-700 rounded-lg px-3 py-3 sm:px-4 text-white focus:outline-none focus:ring-2 focus:ring-aha-red/50 focus:border-aha-red/50 transition-all min-h-[100px] text-sm sm:text-base"
              placeholder="Write down what your goal is according to your answer above"
            />
          </motion.div>

          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            className="flex items-center"
          >
            <input
              type="checkbox"
              id="terms"
              required
              className="w-5 h-5 bg-aha-dark border border-gray-700 rounded mr-3 accent-aha-red"
            />
            <label htmlFor="terms" className="text-sm text-gray-300">
              I agree to the <a href="/terms" className="text-aha-red hover:underline font-medium">terms & conditions</a> provided by AHA Innovations
            </label>
          </motion.div>

          <motion.button
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.8 }}
            whileHover={{ scale: 1.02, boxShadow: "0 10px 25px -5px rgba(225, 29, 72, 0.4)" }}
            whileTap={{ scale: 0.98 }}
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-aha-red hover:bg-aha-darkred text-white font-semibold py-4 px-6 rounded-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed shadow-lg shadow-aha-red/20"
          >
            {isSubmitting ? 'Processing...' : 'Create Your Free Account'}
          </motion.button>
        </form>
      </div>
    </motion.div>
  );
};

export default CustomSignUpForm;
