import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Mail, Eye, EyeOff } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { FcGoogle } from 'react-icons/fc';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/utils/supabaseClient';
import { authenticateGHLUser, updateSupabaseProfileWithGHL } from '@/utils/ghlPrivateAuth';

const SignInForm = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      if (!supabase) {
        throw new Error("Supabase client not initialized. Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY environment variables.");
      }

      // Sign in with Supabase
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      // Verify user with GHL using private integration
      const ghlAuth = await authenticateGHLUser(email);

      if (ghlAuth.success && ghlAuth.locationId && data.user) {
        // Update Supabase profile with GHL information if needed
        await updateSupabaseProfileWithGHL(data.user.id, {
          locationId: ghlAuth.locationId,
          userId: ghlAuth.userId
        });

        toast({
          title: "Success!",
          description: "Redirecting to your AHA-Innovations dashboard...",
        });

        // Redirect to GHL dashboard
        setTimeout(() => {
          window.location.href = 'https://app.aha-innovations.com/';
        }, 1500);
      } else {
        console.warn('GHL authentication warning:', ghlAuth.message);
        // Still allow login but with a warning if GHL auth fails
        toast({
          title: "Signed in with limited access",
          description: "Your account was authenticated but AHA-Innovations connection may be limited. Redirecting...",
          variant: "warning",
        });

        // Still redirect to GHL dashboard even with limited access
        setTimeout(() => {
          window.location.href = 'https://app.aha-innovations.com/';
        }, 2000);
      }
    } catch (error: any) {
      toast({
        title: "Error signing in",
        description: error.message || "Please check your credentials and try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSocialSignIn = async (provider: 'github' | 'google') => {
    try {
      if (!supabase) {
        throw new Error("Supabase client not initialized. Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY environment variables.");
      }

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent'
          }
        },
      });

      if (error) throw error;

    } catch (error: any) {
      toast({
        title: "Error with social login",
        description: error.message || `Failed to sign in with ${provider}.`,
        variant: "destructive",
      });
    }
  };

  return (
    <form onSubmit={handleSignIn} className="space-y-4">
      <div className="space-y-2">
        <Input
          type="email"
          placeholder="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="bg-gray-800 border-gray-700 text-white"
        />
        <div className="relative">
          <Input
            type={showPassword ? "text" : "password"}
            placeholder="Password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className="bg-gray-800 border-gray-700 text-white pr-10"
          />
          <button
            type="button"
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
          </button>
        </div>
      </div>

      <div className="flex justify-end">
        <Button
          variant="link"
          className="text-sm text-gray-400 hover:text-white p-0"
          onClick={() => navigate('/forgot-password')}
          type="button"
        >
          Forgot password?
        </Button>
      </div>

      <Button
        type="submit"
        className="w-full bg-aha-red hover:bg-aha-darkred text-white"
        disabled={isLoading}
      >
        {isLoading ? 'Signing in...' : 'Sign In'}
      </Button>

      <div className="flex items-center gap-2 mt-4">
        <Separator className="flex-1 bg-gray-700" />
        <span className="text-xs text-gray-400">OR</span>
        <Separator className="flex-1 bg-gray-700" />
      </div>

      <Button
        onClick={() => handleSocialSignIn('google')}
        variant="outline"
        className="bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 flex items-center justify-center gap-3 w-full"
      >
        <FcGoogle className="h-5 w-5" />
        Continue with Google
      </Button>

      <div className="text-center mt-4">
        <span className="text-gray-400 text-sm">Don't have an account? </span>
        <Button
          variant="link"
          className="text-aha-red hover:text-aha-darkred p-0 text-sm"
          onClick={() => navigate('/signup')}
        >
          Sign up
        </Button>
      </div>
    </form>
  );
};

export default SignInForm;
