
import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';

const ForgotPasswordForm = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // This is where you would integrate with Supabase auth password reset
    // For now, we'll just simulate a successful submission
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setIsSubmitted(true);
      toast({
        title: "Reset link sent",
        description: "If an account exists with this email, you'll receive a password reset link.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "There was a problem sending the reset link. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="text-center space-y-4">
        <h3 className="text-lg font-medium text-white">Check your email</h3>
        <p className="text-gray-400">
          We've sent a password reset link to <span className="text-white">{email}</span>
        </p>
        <p className="text-gray-400 text-sm">
          Didn't receive an email? Check your spam folder or try again.
        </p>
        <div className="pt-2">
          <Button
            variant="outline"
            onClick={() => setIsSubmitted(false)}
            className="bg-transparent border-gray-700 text-white hover:bg-gray-800 mr-2"
          >
            Try again
          </Button>
          <Button
            onClick={() => { window.location.href = 'https://app.aha-innovations.com'; }}
            className="bg-aha-red hover:bg-aha-darkred text-white"
          >
            Back to sign in
          </Button>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <p className="text-gray-400 mb-4">
        Enter your email address and we'll send you a link to reset your password.
      </p>
      
      <Input
        type="email"
        placeholder="Email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        required
        className="bg-gray-800 border-gray-700 text-white"
      />
      
      <Button 
        type="submit" 
        className="w-full bg-aha-red hover:bg-aha-darkred text-white"
        disabled={isLoading}
      >
        {isLoading ? 'Sending...' : 'Send Reset Link'}
      </Button>
      
      <Button 
        type="button" 
        variant="link" 
        className="w-full text-gray-400 hover:text-white"
        onClick={() => navigate('/signin')}
      >
        Back to sign in
      </Button>
    </form>
  );
};

export default ForgotPasswordForm;
