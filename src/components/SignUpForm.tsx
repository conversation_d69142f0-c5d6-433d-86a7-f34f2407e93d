
import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Mail, Loader2 } from 'lucide-react';
import { FcGoogle } from 'react-icons/fc';
import { useToast } from '@/hooks/use-toast';
import SocialButton from './SocialButton';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/utils/supabaseClient';
import { createGHLAccountOnSignUp, updateSupabaseProfileWithGHL } from '@/utils/ghlPrivateAuth';

const SignUpForm = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  // Enhanced signup form with existing user detection
  console.log('Enhanced SignUpForm v2.0 component loaded successfully');

  const handleEmailSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) {
      toast({
        title: "Missing Information",
        description: "Please enter your email address.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      if (!supabase) {
        throw new Error("Supabase client not initialized");
      }

      // Create account with Supabase Auth (no GHL account creation yet)
      const { data, error } = await supabase.auth.signUp({
        email,
        password: Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8), // Generate random password
        options: {
          data: {
            email_signup: true, // Flag to indicate this was email signup
          }
        }
      });

      if (error) throw error;

      if (data.user) {
        // Create GHL contact immediately for tracking/marketing
        try {
          // Generate contact data from email
          const emailParts = email.split('@');
          const emailName = emailParts[0];
          const firstName = emailName.charAt(0).toUpperCase() + emailName.slice(1);
          const lastName = 'User'; // Default last name

          // Call our contact creation edge function
          const contactResponse = await supabase.functions.invoke('create-ghl-contact', {
            body: {
              locationId: 'LL7TmGrkL72EOf8O0FKA', // Your location ID
              email: email,
              firstName: firstName,
              lastName: lastName,
              companyName: `${firstName}'s Business`,
              phone: `+1${Math.floor(Math.random() * 9000000000) + 1000000000}`, // Generate fake phone
              city: 'Unknown',
              state: 'Unknown',
              country: 'US'
            }
          });

          if (!contactResponse.error) {
            console.log('GHL contact created successfully for email signup:', contactResponse.data);
          } else {
            console.warn('Contact creation failed, but continuing with signup:', contactResponse.error);
          }
        } catch (contactError) {
          console.warn('Contact creation error, but continuing with signup:', contactError);
        }

        toast({
          title: "Account Created Successfully!",
          description: "Now choose your plan to continue.",
        });

        // Redirect to plan selection page with user info
        navigate('/plan-selection', {
          state: {
            userInfo: {
              email,
              name: email.split('@')[0] // Extract name from email
            }
          }
        });
      }
    } catch (error: any) {
      console.error('Sign up error:', error);
      console.log('Enhanced SignUpForm v2.0 - Error details:', {
        message: error.message,
        code: error.code,
        status: error.status
      });

      // Check if user already exists
      if (error.message?.includes('User already registered') ||
          error.message?.includes('already registered') ||
          error.message?.includes('already exists')) {

        console.log('Enhanced SignUpForm v2.0 - Existing user detected, showing Account Found message');

        // Show redirect message similar to Google login
        toast({
          title: "Account Found! 🎉",
          description: "We found your account! Redirecting you to sign in...",
          duration: 2000,
        });

        // Redirect to dashboard with email prefilled and auto-signin trigger after a short delay
        setTimeout(() => {
          const encodedEmail = encodeURIComponent(email);
          window.location.href = `https://app.aha-innovations.com/?email=${encodedEmail}&auto_google=true&from_signup=true`;
        }, 2000);

        return;
      }

      // Handle other errors
      toast({
        title: "Sign Up Failed",
        description: error.message || "An error occurred during sign up. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignUp = async () => {
    setIsGoogleLoading(true);

    try {
      if (!supabase) {
        throw new Error("Supabase client not initialized");
      }

      console.log('SignUpForm: Starting Google OAuth with redirect to:', `${window.location.origin}/auth/callback`);

      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent'
          }
        }
      });

      if (error) throw error;
    } catch (error: any) {
      console.error('Google sign up error:', error);
      toast({
        title: "Google Sign Up Failed",
        description: error.message || "An error occurred with Google sign up. Please try again.",
        variant: "destructive",
      });
      setIsGoogleLoading(false);
    }
  };

  return (
    <div className="space-y-6" data-component="enhanced-signup-v2" data-timestamp="2025-07-08-v2">
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-white">Sign up now it's free</h2>
        <p className="text-gray-400">Create your AHA-Innovations account and get started</p>
      </div>

      {/* Google Sign Up Button */}
      <Button
        onClick={handleGoogleSignUp}
        disabled={isGoogleLoading || isLoading}
        className="w-full bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 flex items-center justify-center gap-3"
        variant="outline"
      >
        {isGoogleLoading ? (
          <>
            <Loader2 className="h-5 w-5 animate-spin" />
            Signing up with Google...
          </>
        ) : (
          <>
            <FcGoogle className="h-5 w-5" />
            Continue with Google
          </>
        )}
      </Button>

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <Separator className="w-full bg-gray-700" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-aha-dark px-2 text-gray-400">Or continue with email</span>
        </div>
      </div>

      {/* Email Sign Up Form */}
      <form onSubmit={handleEmailSignUp} className="space-y-4">
        <div className="space-y-3">
          <Input
            type="email"
            placeholder="Email Address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="bg-gray-800 border-gray-700 text-white placeholder-gray-400"
          />
        </div>

        <Button
          type="submit"
          disabled={isLoading || isGoogleLoading}
          className="w-full bg-aha-red hover:bg-aha-darkred text-white"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating Account...
            </>
          ) : (
            <>
              <Mail className="mr-2 h-4 w-4" />
              Create Account
            </>
          )}
        </Button>
      </form>

      {/* Sign In Link */}
      <div className="text-center">
        <p className="text-gray-400">
          Already have an account?{' '}
          <button
            onClick={() => { window.location.href = 'https://app.aha-innovations.com'; }}
            className="text-aha-red hover:text-aha-darkred font-medium"
          >
            Sign in
          </button>
        </p>
      </div>
    </div>
  );
};

export default SignUpForm;
