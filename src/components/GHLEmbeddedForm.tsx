import { useEffect, useRef } from "react";

interface GHLEmbeddedFormProps {
  formId?: string;
  height?: number;
  className?: string;
}

const GHLEmbeddedForm = ({
  formId = "YN86bt2SlADr1BfnDEkq",
  height = 750,
  className = ""
}: GHLEmbeddedFormProps) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Load the GHL form script after the component mounts
  useEffect(() => {
    // Create and load the GHL script
    const script = document.createElement("script");
    script.src = "https://calendar.aha-innovations.com/js/form_embed.js";
    script.async = true;
    document.body.appendChild(script);

    // Add a link to our custom CSS for the GHL form
    const styleLink = document.createElement("link");
    styleLink.rel = "stylesheet";
    styleLink.href = "/ghl-form-styles.css";
    document.head.appendChild(styleLink);

    // Create a div to hold the form
    const formContainer = document.createElement('div');
    formContainer.id = 'ghl-form-container';
    formContainer.style.width = '100%';
    formContainer.style.maxWidth = '600px';
    formContainer.style.height = `${height}px`;
    formContainer.style.backgroundColor = 'transparent';
    formContainer.style.border = 'none';
    formContainer.style.boxShadow = 'none';
    formContainer.style.padding = '0';
    formContainer.style.margin = '0 auto';
    formContainer.style.display = 'block';
    formContainer.style.borderRadius = '8px';
    formContainer.style.overflow = 'hidden';

    // Create the iframe directly
    const iframe = document.createElement('iframe');
    iframe.src = `https://calendar.aha-innovations.com/widget/form/${formId}`;
    iframe.id = `inline-${formId}`;
    iframe.style.width = '100%';
    iframe.style.height = '100%';
    iframe.style.border = 'none';
    iframe.style.backgroundColor = 'transparent';
    iframe.style.boxShadow = 'none';
    iframe.style.padding = '0';
    iframe.style.margin = '0';
    iframe.style.borderRadius = '8px';
    iframe.style.overflow = 'hidden';
    iframe.setAttribute('data-layout', "{'id':'INLINE'}");
    iframe.setAttribute('data-trigger-type', 'alwaysShow');
    iframe.setAttribute('data-activation-type', 'alwaysActivated');
    iframe.setAttribute('data-deactivation-type', 'neverDeactivate');
    iframe.setAttribute('data-form-name', 'Sign Up form AHA embedded to site');
    iframe.setAttribute('data-height', height.toString());
    iframe.setAttribute('data-layout-iframe-id', `inline-${formId}`);
    iframe.setAttribute('data-form-id', formId);
    iframe.setAttribute('data-custom-css-url', '/ghl-form-styles.css');
    iframe.title = 'Sign Up form AHA embedded to site';

    // Append the iframe to the container
    formContainer.appendChild(iframe);

    // Find the form section and append the container
    if (containerRef.current) {
      containerRef.current.appendChild(formContainer);
    }

    // Cleanup function to remove the script and style when component unmounts
    return () => {
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
      if (document.head.contains(styleLink)) {
        document.head.removeChild(styleLink);
      }
      if (containerRef.current && containerRef.current.contains(formContainer)) {
        containerRef.current.removeChild(formContainer);
      }
    };
  }, [formId, height]);

  return (
    <div
      ref={containerRef}
      className={`flex justify-center items-center ${className} relative`}
      style={{ minHeight: `${height}px` }}
    >
      {/* Loading animation that fades out when form loads */}
      <div className="absolute inset-0 flex items-center justify-center animate-pulse transition-opacity duration-1000 opacity-0">
        <div className="w-16 h-16 border-4 border-aha-red/30 border-t-aha-red rounded-full animate-spin"></div>
      </div>
      {/* The form will be dynamically inserted here */}
    </div>
  );
};

export default GHLEmbeddedForm;
