import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { useNavigate } from 'react-router-dom';
import { ArrowR<PERSON>, Sparkles, Check } from 'lucide-react';
import { CalendarPopup } from '../ui/calendar-popup';

const ModernCTA: React.FC = () => {
  const navigate = useNavigate();
  const [showCalendar, setShowCalendar] = useState(false);

  return (
    <section className="py-32 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute -top-1/4 -right-1/4 w-1/2 h-1/2 bg-gradient-to-br from-aha-red/20 via-purple-500/10 to-transparent rounded-full blur-[150px]"
          animate={{
            rotate: 360,
            scale: [1, 1.1, 1],
            opacity: [0.2, 0.3, 0.2],
          }}
          transition={{
            rotate: {
              duration: 40,
              repeat: Infinity,
              ease: "linear"
            },
            scale: {
              duration: 10,
              repeat: Infinity,
              repeatType: "reverse"
            },
            opacity: {
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse"
            }
          }}
        />
        <motion.div
          className="absolute -bottom-1/4 -left-1/4 w-1/2 h-1/2 bg-gradient-to-tr from-blue-500/20 via-purple-500/10 to-transparent rounded-full blur-[150px]"
          animate={{
            rotate: -360,
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.3, 0.2],
          }}
          transition={{
            rotate: {
              duration: 50,
              repeat: Infinity,
              ease: "linear"
            },
            scale: {
              duration: 12,
              repeat: Infinity,
              repeatType: "reverse"
            },
            opacity: {
              duration: 10,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 2
            }
          }}
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-5xl mx-auto">
          <motion.div
            className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8 md:p-16 shadow-2xl relative overflow-hidden hover:bg-white/8 hover:border-white/20 transition-all duration-500"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            {/* Decorative elements */}
            <motion.div
              className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-aha-red/10 via-purple-500/10 to-blue-500/5 rounded-full blur-[80px] opacity-60"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.5, 0.3],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            />

            <div className="relative z-10">
              <div className="flex flex-col md:flex-row gap-8 md:gap-16 items-center">
                <div className="md:w-3/5">
                  <div className="flex items-center gap-2 mb-6">
                    <Badge className="bg-aha-red/20 hover:bg-aha-red/30 text-aha-red border-none py-1.5 px-4 font-medium">
                      Limited Time Offer
                    </Badge>
                    <motion.div
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <Sparkles className="text-yellow-400 w-5 h-5" />
                    </motion.div>
                  </div>

                  <motion.h2
                    className="text-3xl md:text-5xl font-extrabold mb-6 tracking-tight"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                    viewport={{ once: true }}
                  >
                    <span className="text-white">You're just one click away from </span>
                    <span className="text-aha-red">Growth</span>
                  </motion.h2>

                  <motion.p
                    className="text-lg text-white/90 mb-8 font-medium leading-relaxed tracking-wide font-gotham"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    viewport={{ once: true }}
                  >
                    Start building with AHA-Innovations—no coding, no overwhelm. Just the tools you need to succeed.
                  </motion.p>

                  <motion.div
                    className="flex flex-col sm:flex-row gap-4"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                    viewport={{ once: true }}
                  >
                    <Button
                      size="lg"
                      variant="outline"
                      className="border-white/20 text-white hover:bg-white/5 px-8 py-5 rounded-full"
                      onClick={() => setShowCalendar(true)}
                    >
                      <span className="font-medium">Contact Sales</span>
                    </Button>
                  </motion.div>
                </div>

                <div className="md:w-2/5">
                  <motion.div
                    className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6 relative hover:bg-white/10 hover:border-white/20 transition-all duration-300 shadow-xl shadow-black/20"
                    initial={{ opacity: 0, scale: 0.95 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                    viewport={{ once: true }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-aha-red/8 via-purple-500/5 to-blue-500/3 rounded-2xl"></div>

                    <div className="relative z-10">
                      <div className="flex justify-between items-center mb-6">
                        <h3 className="text-xl font-bold text-white">Agency Plan</h3>
                        <Badge className="bg-aha-red/20 text-aha-red border-none">Popular</Badge>
                      </div>

                      <div className="mb-6">
                        <div className="flex items-end gap-1">
                          <span className="text-4xl font-bold text-white">$25</span>
                          <span className="text-white/90 mb-1 font-medium">/month</span>
                        </div>
                        <p className="text-sm text-white/90 font-medium">Billed annually ($285 total)</p>
                      </div>

                      <ul className="space-y-3 mb-6">
                        {[
                          "All essential features",
                          "Up to 5,000 contacts",
                          "Unlimited sales funnels",
                          "Email & SMS marketing",
                          "24/7 customer support"
                        ].map((feature, index) => (
                          <motion.li
                            key={index}
                            className="flex items-start gap-2"
                            initial={{ opacity: 0, x: -10 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.3, delay: 0.6 + (index * 0.1) }}
                            viewport={{ once: true }}
                          >
                            <div className="w-5 h-5 rounded-full bg-aha-red/20 flex items-center justify-center mt-0.5">
                              <Check size={12} className="text-aha-red" />
                            </div>
                            <span className="text-gray-300">{feature}</span>
                          </motion.li>
                        ))}
                      </ul>

                      <Button
                        className="w-full bg-white hover:bg-gray-100 text-black rounded-full py-5"
                        onClick={() => window.open('https://buy.stripe.com/fZu3cw7Za0jQ6LN37U4801H', '_blank', 'noopener,noreferrer')}
                      >
                        <span className="font-medium">Start 14-day free trial</span>
                      </Button>
                    </div>
                  </motion.div>
                </div>
              </div>

              {/* Free Plan CTA below Agency Plan */}
              <div className="mt-8 text-center">
                <Button
                  className="bg-aha-red hover:bg-aha-darkred text-white px-8 py-5 rounded-full font-bold mb-4"
                  onClick={() => navigate('/signup')}
                >
                  Try Free Plan
                </Button>
                <p className="text-white/80 text-sm">
                  Not ready to commit? try our Free Plan...
                </p>
              </div>

              <motion.div
                className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.7 }}
                viewport={{ once: true }}
              >
                {[
                  {
                    value: "14-day",
                    label: "Free Trial",
                    color: "aha-red"
                  },
                  {
                    value: "24/7",
                    label: "Customer Support",
                    color: "blue"
                  },
                  {
                    value: "100%",
                    label: "Satisfaction Guarantee",
                    color: "purple"
                  }
                ].map((item, index) => {
                  const bgColor =
                    item.color === "blue" ? "bg-blue-500/10" :
                    item.color === "purple" ? "bg-purple-500/10" :
                    "bg-aha-red/10";

                  const textColor =
                    item.color === "blue" ? "text-blue-400" :
                    item.color === "purple" ? "text-purple-400" :
                    "text-aha-red";

                  return (
                    <motion.div
                      key={index}
                      className={`glass glass-reflection rounded-xl p-6 text-center`}
                      whileHover={{ y: -5 }}
                      transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    >
                      <div className={`text-3xl font-bold ${textColor} mb-1`}>{item.value}</div>
                      <div className="text-white/90 text-sm font-medium">{item.label}</div>
                    </motion.div>
                  );
                })}
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Calendar Popup */}
      <CalendarPopup
        isOpen={showCalendar}
        onClose={() => setShowCalendar(false)}
      />
    </section>
  );
};

export default ModernCTA;
