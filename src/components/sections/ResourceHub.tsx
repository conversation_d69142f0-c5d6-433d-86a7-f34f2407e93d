import React from 'react';
import { motion } from 'framer-motion';
import { Badge } from '../ui/badge';
import { TextEffect } from '../ui/text-effect';
import Tilt3D from '../anim/Tilt3D';
import {
  BookOpen,
  FileText,
  Video,
} from 'lucide-react';

interface ResourceCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay?: number;
}

const ResourceCard: React.FC<ResourceCardProps> = ({ icon, title, description, delay = 0 }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: delay * 0.1 }}
      viewport={{ once: true, margin: "-50px" }}
    >
      <Tilt3D className="h-full" intensity={5}>
        <div className="bg-gradient-to-br from-aha-red/10 to-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-6 h-full transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/20 hover:border-aha-red/30 group">
          <div className="w-14 h-14 rounded-lg bg-aha-red/20 flex items-center justify-center mb-4 text-white transition-transform duration-300 group-hover:scale-110 group-hover:bg-aha-red/30">
            {icon}
          </div>
          <h3 className="text-xl font-bold mb-2 text-white group-hover:text-aha-red transition-colors duration-300">{title}</h3>
          <p className="text-white/90 font-medium">{description}</p>
        </div>
      </Tilt3D>
    </motion.div>
  );
};

const ResourceHub: React.FC = () => {
  const resources = [
    {
      icon: <BookOpen size={28} />,
      title: "Quick Guides",
      description: "Step-by-step setup tips",
    },
    {
      icon: <FileText size={28} />,
      title: "Templates",
      description: "Ready-to-use designs",
    },
    {
      icon: <Video size={28} />,
      title: "Video Tutorials",
      description: "Learn fast, build faster",
    },
  ];

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-[#0F0F0F]"></div>

      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute top-1/4 right-0 w-1/2 h-1/2 bg-gradient-to-l from-blue-500/10 via-purple-500/10 to-transparent rounded-full blur-[120px]"
          animate={{
            x: [0, 50, 0],
            opacity: [0.4, 0.6, 0.4],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
        <motion.div
          className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-gradient-to-r from-aha-red/10 via-purple-500/10 to-transparent rounded-full blur-[120px]"
          animate={{
            x: [0, -50, 0],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            repeatType: "reverse",
            delay: 5
          }}
        />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[120%] h-[120%] bg-gradient-radial from-aha-red/10 via-transparent to-transparent opacity-40"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="bg-aha-red/20 hover:bg-aha-red/30 text-aha-red border-none mb-6 py-1.5 px-4 mx-auto block w-fit font-medium">
            Resources
          </Badge>
          <TextEffect
            per="word"
            as="h2"
            className="text-4xl md:text-5xl font-extrabold mb-6 tracking-tight font-gotham"
            variants={{
              container: {
                hidden: { opacity: 0 },
                visible: {
                  opacity: 1,
                  transition: { staggerChildren: 0.06, delayChildren: 0.4 },
                },
                exit: {
                  transition: { staggerChildren: 0.04, staggerDirection: -1 },
                },
              },
              item: {
                hidden: {
                  opacity: 0,
                  scale: 0,
                  rotate: -180,
                },
                visible: {
                  opacity: 1,
                  scale: 1,
                  rotate: 0,
                  transition: {
                    type: "spring",
                    damping: 10,
                    stiffness: 150,
                    duration: 0.8,
                  },
                },
                exit: {
                  opacity: 0,
                  scale: 0.3,
                  rotate: 180,
                  transition: {
                    duration: 0.5,
                    ease: "easeInOut",
                  },
                },
              },
            }}
          >
            Need help getting started?
          </TextEffect>
          <p className="text-center text-white/90 max-w-4xl mx-auto text-lg md:text-xl lg:text-xl font-medium leading-relaxed tracking-wide">
            We've got everything you need to get up and running quickly with AHA-Innovations.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8">
          {resources.map((resource, index) => (
            <ResourceCard
              key={index}
              icon={resource.icon}
              title={resource.title}
              description={resource.description}
              delay={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ResourceHub;
