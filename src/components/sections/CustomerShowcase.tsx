import React, { useEffect, useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowRight, ExternalLink } from 'lucide-react';
import Tilt3D from '@/components/anim/Tilt3D';

// Project interface
interface Project {
  name: string;
  url: string;
  domain: string;
  image: string;
}

// Project card component
const ProjectCard: React.FC<{ project: Project; index: number }> = ({ project, index }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true, margin: "-50px" }}
      className="flex flex-col h-full"
    >
      <Tilt3D className="h-full" intensity={5}>
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden h-full transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/5 hover:border-aha-red/20 group">
          {/* Browser mockup header */}
          <div className="bg-black/50 backdrop-blur-sm h-8 flex items-center px-4">
            <div className="flex gap-2">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
            </div>
            <div className="text-xs text-white/90 mx-auto truncate max-w-[150px] md:max-w-[200px] font-medium">{project.domain}</div>
          </div>

          {/* Website screenshot */}
          <div className="relative overflow-hidden">
            <img
              src={project.image}
              alt={project.name}
              className="w-full aspect-[16/10] object-cover object-top transition-transform duration-500 group-hover:scale-105"
            />

            {/* Overlay on hover */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-between p-4">
              <span className="text-white font-medium">{project.name}</span>
              <Button
                size="icon"
                variant="ghost"
                className="h-8 w-8 rounded-full bg-white/20 hover:bg-white/30 text-white"
                onClick={() => window.open(project.url, '_blank')}
              >
                <ExternalLink size={14} />
              </Button>
            </div>
          </div>

          {/* Site name and domain */}
          <div className="p-4">
            <h4 className="font-bold text-white">{project.name}</h4>
            <p className="text-sm text-white/90 font-medium">{project.domain}</p>
          </div>
        </div>
      </Tilt3D>
    </motion.div>
  );
};

const CustomerShowcase: React.FC = () => {
  // Projects data
  const projects: Project[] = [
    {
      name: "Launchpad Website Craft",
      url: "https://launchpad-website-craft.vercel.app/",
      domain: "launchpad-website-craft.vercel.app",
      image: "/launchpad.jpg"
    },
    {
      name: "Image Source Finder",
      url: "https://image-source-finder.vercel.app/",
      domain: "image-source-finder.vercel.app",
      image: "/image-source-finder.jpg"
    },
    {
      name: "TimePiece",
      url: "https://timepiece.site/",
      domain: "timepiece.site",
      image: "/timepiece.jpg"
    },
    {
      name: "Millennial Business Academy",
      url: "https://start.millennialbusinessacademy.net/",
      domain: "start.millennialbusinessacademy.net",
      image: "/millennial.jpg"
    },
    {
      name: "Millennial Business Innovations",
      url: "https://millennialbusinessinnovations.com/",
      domain: "millennialbusinessinnovations.com",
      image: "/MilllennialBusinessAcademy.png"
    },
    {
      name: "AHA Innovations",
      url: "https://aha-innovations.com/",
      domain: "aha-innovations.com",
      image: "/aha-innovations.jpg"
    },
    {
      name: "RR Twins",
      url: "https://rrtwins.com/",
      domain: "rrtwins.com",
      image: "/rrtwins.jpg"
    },
    {
      name: "Undertake PH",
      url: "https://undertakeph.com/",
      domain: "undertakeph.com",
      image: "/undertake.jpg"
    }
  ];

  // Refs for scroll animations
  const sectionRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);

  // Scroll-based animations
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });

  // Transform values based on scroll
  const bgOpacity = useTransform(scrollYProgress, [0, 0.2, 0.8], [0, 1, 1]);
  const titleScale = useTransform(scrollYProgress, [0, 0.2, 0.8], [0.8, 1, 1]);
  const titleY = useTransform(scrollYProgress, [0, 0.2, 0.8], [50, 0, 0]);

  return (
    <section
      ref={sectionRef}
      className="py-32 md:py-40 relative overflow-hidden"
      style={{ background: '#0F0F0F' }}
    >
      {/* Animated background elements */}
      <motion.div
        className="absolute inset-0 overflow-hidden"
        style={{ opacity: bgOpacity }}
      >
        <div className="absolute top-1/3 left-0 w-1/2 h-1/2 bg-gradient-to-r from-aha-red/20 via-blue-500/10 to-transparent rounded-full blur-[150px]" />
        <div className="absolute bottom-1/3 right-0 w-1/2 h-1/2 bg-gradient-to-l from-aha-red/20 via-blue-500/10 to-transparent rounded-full blur-[150px]" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-1/2 bg-gradient-to-t from-aha-red/5 to-transparent rounded-full blur-[200px]" />
      </motion.div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-8 py-1.5 px-4 mx-auto block w-fit">
            Customer Success
          </Badge>

          <motion.div
            style={{
              scale: titleScale,
              y: titleY
            }}
          >
            <motion.h2
              ref={titleRef}
              className="text-4xl md:text-6xl lg:text-7xl font-extrabold mb-8 tracking-tight"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <span className="text-white">
                See what we help our
              </span> <br />
              <span className="text-aha-red">
                customers build
              </span>
            </motion.h2>
          </motion.div>

          <motion.p
            className="text-center text-gray-300 max-w-3xl mx-auto text-lg md:text-xl"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
          >
            Discover the diverse range of projects and ideas that our customers have built with AHA-Innovations.
          </motion.p>
        </motion.div>

        {/* Projects grid */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true, margin: "-100px" }}
        >
          {projects.map((project, index) => (
            <ProjectCard key={index} project={project} index={index} />
          ))}
        </motion.div>

        {/* Explore full showcase button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <Button
            className="bg-aha-red hover:bg-aha-darkred text-white rounded-full px-8 py-6 text-lg group relative overflow-hidden"
            onClick={() => window.location.href = "/showcase"}
          >
            <span className="relative z-10 flex items-center gap-2 font-medium">
              Explore full showcase
              <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
            </span>
            <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
          </Button>
        </motion.div>
      </div>
    </section>
  );
};

export default CustomerShowcase;
