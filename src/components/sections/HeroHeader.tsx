import React from 'react'
import { useNavigate } from 'react-router-dom'
import { Menu, X } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import Logo from '@/components/Logo'
import { useScroll, useTransform } from 'framer-motion'

const menuItems = [
    { name: 'Features', href: '/features' },
    { name: 'Pricing', href: '/pricing' },
    { name: 'Contact', href: '/contact' },
    { name: 'Community', href: 'https://tutorials.aha-university.org/communities/groups/aha-community/home?invite=684a52e768e341175153da67', external: true },
]

const HeroHeader = () => {
    const navigate = useNavigate()
    const [menuState, setMenuState] = React.useState(false)
    const [scrollState, setScrollState] = React.useState(false)

    // Use framer-motion's useScroll for better scroll detection
    const { scrollY } = useScroll()

    // Convert the motion value to a boolean state
    React.useEffect(() => {
        return scrollY.onChange((latest) => {
            setScrollState(latest > 50)
        })
    }, [scrollY])

    return (
        <header>
            <nav
                data-state={menuState && 'active'}
                className="fixed z-20 w-full px-2 group transition-all duration-500">
                <div className={cn(
                    'mx-auto transition-all duration-500 ease-out',
                    scrollState
                        ? 'mt-3 max-w-4xl px-4 py-2 bg-white/5 backdrop-blur-xl rounded-2xl border border-white/10 shadow-lg shadow-black/20'
                        : 'mt-0 max-w-6xl px-6 py-4 lg:px-12'
                )}>
                    <div className="relative flex flex-wrap items-center justify-between gap-6 lg:gap-0">
                        <div className="flex w-full justify-between lg:w-auto">
                            <button
                                onClick={() => navigate('/')}
                                aria-label="home"
                                className="flex items-center space-x-2">
                                <Logo />
                                <span className="font-bold text-white text-lg tracking-tight hidden sm:inline-block">AHA-Innovations</span>
                            </button>

                            <button
                                onClick={() => setMenuState(!menuState)}
                                aria-label={menuState == true ? 'Close Menu' : 'Open Menu'}
                                className="relative z-20 -m-2.5 -mr-4 block cursor-pointer p-2.5 lg:hidden">
                                <Menu className="in-data-[state=active]:rotate-180 group-data-[state=active]:scale-0 group-data-[state=active]:opacity-0 m-auto size-6 duration-200" />
                                <X className="group-data-[state=active]:rotate-0 group-data-[state=active]:scale-100 group-data-[state=active]:opacity-100 absolute inset-0 m-auto size-6 -rotate-180 scale-0 opacity-0 duration-200" />
                            </button>
                        </div>

                        <div className="absolute inset-0 m-auto hidden size-fit lg:block">
                            <ul className="flex gap-8 text-sm">
                                {menuItems.map((item, index) => (
                                    <li key={index}>
                                        <button
                                            onClick={() => {
                                                if (item.external) {
                                                    window.open(item.href, '_blank', 'noopener,noreferrer');
                                                } else {
                                                    navigate(item.href);
                                                }
                                            }}
                                            className="text-muted-foreground hover:text-accent-foreground block duration-150">
                                            <span>{item.name}</span>
                                        </button>
                                    </li>
                                ))}
                            </ul>
                        </div>

                        <div className="bg-white/5 backdrop-blur-xl group-data-[state=active]:block lg:group-data-[state=active]:flex mb-6 hidden w-full flex-wrap items-center justify-end space-y-8 rounded-3xl border border-white/10 p-6 shadow-2xl shadow-black/20 md:flex-nowrap lg:m-0 lg:flex lg:w-fit lg:gap-6 lg:space-y-0 lg:border-transparent lg:bg-transparent lg:p-0 lg:shadow-none dark:shadow-none dark:lg:bg-transparent">
                            <div className="lg:hidden">
                                <ul className="space-y-6 text-base">
                                    {menuItems.map((item, index) => (
                                        <li key={index}>
                                            <button
                                                onClick={() => {
                                                    if (item.external) {
                                                        window.open(item.href, '_blank', 'noopener,noreferrer');
                                                    } else {
                                                        navigate(item.href);
                                                    }
                                                    setMenuState(false);
                                                }}
                                                className="text-muted-foreground hover:text-accent-foreground block duration-150">
                                                <span>{item.name}</span>
                                            </button>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                            <div className="flex w-full flex-col space-y-3 sm:flex-row sm:gap-3 sm:space-y-0 md:w-fit">
                                <Button
                                    asChild
                                    variant="outline"
                                    size="sm"
                                    className={cn(
                                        'transition-all duration-300',
                                        scrollState ? 'lg:hidden opacity-0 scale-95' : 'opacity-100 scale-100'
                                    )}>
                                    <button onClick={() => window.location.href = 'https://app.aha-innovations.com'}>
                                        <span>Sign In</span>
                                    </button>
                                </Button>
                                <Button
                                    asChild
                                    size="sm"
                                    className={cn(
                                        'transition-all duration-300',
                                        scrollState ? 'lg:hidden opacity-0 scale-95' : 'opacity-100 scale-100'
                                    )}>
                                    <button onClick={() => navigate('/signup')}>
                                        <span>Sign Up</span>
                                    </button>
                                </Button>
                                <Button
                                    asChild
                                    size="sm"
                                    className={cn(
                                        'transition-all duration-300',
                                        scrollState ? 'lg:inline-flex opacity-100 scale-100' : 'hidden opacity-0 scale-95'
                                    )}>
                                    <button onClick={() => navigate('/signup')}>
                                        <span>Get Started</span>
                                    </button>
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        </header>
    )
}

export default HeroHeader
