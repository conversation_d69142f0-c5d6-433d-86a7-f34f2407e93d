import React from 'react';
import { motion } from 'framer-motion';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { useNavigate } from 'react-router-dom';
import Tilt3D from '../anim/Tilt3D';
import {
  Settings,
  Globe,
  Code,
  MessageSquare,
  Users,
  Calendar,
  Mail,
  Phone,
  BarChart3,
  Zap,
  LineChart,
  Layers,
  Megaphone,
  Building,
  Rocket,
} from 'lucide-react';

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay?: number;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description, delay = 0 }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: delay * 0.1 }}
      viewport={{ once: true, margin: "-50px" }}
    >
      <Tilt3D className="h-full" intensity={5}>
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 h-full transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/5 hover:border-aha-red/20 group">
          <div className="w-12 h-12 rounded-lg bg-aha-red/10 flex items-center justify-center mb-4 text-aha-red transition-transform duration-300 group-hover:scale-110">
            {icon}
          </div>
          <h3 className="text-xl font-bold mb-2 text-white group-hover:text-aha-red transition-colors duration-300">{title}</h3>
          <p className="text-white/90 font-medium">{description}</p>
        </div>
      </Tilt3D>
    </motion.div>
  );
};

const ModernFeatures: React.FC = () => {
  const navigate = useNavigate();
  const features = [
    {
      icon: <Users size={24} className="text-aha-red" />,
      title: "Manage All Your Customers",
      description: "Keep track of every lead, customer, and deal in one organized system. Never lose another opportunity.",
      color: "red"
    },
    {
      icon: <Zap size={24} className="text-aha-red" />,
      title: "Automate Repetitive Tasks",
      description: "Set up smart workflows that handle follow-ups, scheduling, and marketing automatically.",
      color: "red"
    },
    {
      icon: <Rocket size={24} className="text-aha-red" />,
      title: "Convert More Visitors",
      description: "Build high-converting sales funnels and websites that turn visitors into paying customers.",
      color: "red"
    },
    {
      icon: <BarChart3 size={24} className="text-aha-red" />,
      title: "Grow Your Revenue",
      description: "Track what's working with clear analytics and scale the strategies that bring in more money.",
      color: "red"
    }
  ];

  // Function to get the background color based on the color prop
  const getBgColor = (color: string) => {
    const colorMap: {[key: string]: string} = {
      red: "bg-aha-red/10",
      blue: "bg-blue-500/10",
      green: "bg-green-500/10",
      purple: "bg-purple-500/10",
      cyan: "bg-cyan-500/10",
      amber: "bg-amber-500/10",
      yellow: "bg-yellow-500/10",
      indigo: "bg-indigo-500/10",
      rose: "bg-rose-500/10"
    };
    return colorMap[color] || "bg-gray-500/10";
  };

  // Updated FeatureCard component with color prop
  const EnhancedFeatureCard: React.FC<FeatureCardProps & {color: string}> = ({
    icon,
    title,
    description,
    delay = 0,
    color
  }) => {
    // Map color to glass variant
    const glassVariant = color === 'red' ? 'red' :
                         color === 'blue' ? 'blue' :
                         color === 'purple' ? 'purple' : 'default';

    return (
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: delay * 0.1 }}
        viewport={{ once: true, margin: "-50px" }}
      >
        <Tilt3D className="h-full" intensity={5}>
          <div className="bg-gradient-to-br from-aha-red/10 to-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-6 h-full transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/20 hover:border-aha-red/30 group">
            <div className={`w-12 h-12 rounded-lg ${getBgColor(color)} flex items-center justify-center mb-4 transition-transform duration-300 group-hover:scale-110`}>
              {icon}
            </div>
            <h3 className="text-xl font-bold mb-2 text-white transition-colors duration-300 group-hover:text-aha-red">{title}</h3>
            <p className="text-white/90 font-medium">{description}</p>
          </div>
        </Tilt3D>
      </motion.div>
    );
  };

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Remove competing background - let hero background show through */}

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="bg-aha-red/20 hover:bg-aha-red/30 text-aha-red border-none mb-6 py-1.5 px-4 mx-auto block w-fit font-medium">
            Our Solution
          </Badge>
          <h2 className="text-4xl md:text-6xl font-bold mb-6 tracking-tight font-gotham">
            Everything you need to <span className="text-aha-red">grow</span>
          </h2>
          <p className="text-center text-white/90 max-w-4xl mx-auto text-lg md:text-xl lg:text-xl font-medium leading-relaxed tracking-wide">
            Replace 10+ expensive tools with one simple platform. Focus on growing your business,
            not managing software.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <EnhancedFeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
              delay={index}
              color={feature.color}
            />
          ))}
        </div>

        <motion.div
          className="flex flex-col sm:flex-row justify-center gap-4 mt-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Button
            size="lg"
            className="bg-gradient-to-br from-aha-red/90 to-aha-darkred text-white text-lg px-8 py-6 rounded-full border border-white/10 hover:border-aha-red/30 transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/20 group"
            onClick={() => navigate('/signup')}
          >
            <span className="flex items-center gap-2 font-medium">
              Book a Free Call
            </span>
          </Button>

          <Button
            size="lg"
            variant="outline"
            className="bg-white/5 backdrop-blur-sm border border-white/10 text-white hover:bg-white/10 text-lg px-8 py-6 rounded-full transition-all duration-300 hover:border-aha-red/30 hover:shadow-xl hover:shadow-aha-red/10"
            onClick={() => navigate('/signup')}
          >
            <span className="flex items-center gap-2 font-medium">
              Start Free Trial
            </span>
          </Button>
        </motion.div>

        {/* Feature highlight */}
        <motion.div
          className="mt-20 bg-gradient-to-br from-aha-red/10 to-black/40 backdrop-blur-sm border border-white/10 rounded-2xl p-8 md:p-12 overflow-hidden relative hover:shadow-xl hover:shadow-aha-red/20 hover:border-aha-red/30"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          {/* Animated background elements */}
          <motion.div
            className="absolute -top-20 -right-20 w-64 h-64 bg-aha-red/10 rounded-full blur-[80px]"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.5, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className="absolute -bottom-20 -left-20 w-64 h-64 bg-blue-500/10 rounded-full blur-[80px]"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.4, 0.2],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 2
            }}
          />

          <div className="grid md:grid-cols-2 gap-8 items-center relative z-10">
            <div>
              <Badge className="bg-blue-500/20 text-blue-400 border-none mb-4 py-1 px-3">
                AI-Powered
              </Badge>
              <h3 className="text-3xl font-bold mb-4 font-gotham">Smart automation that works for you</h3>
              <p className="text-white/90 mb-6 text-base md:text-lg font-medium leading-relaxed">
                Our AI-powered platform learns from your business patterns and automatically suggests optimizations
                to improve your workflows, customer engagement, and conversion rates.
              </p>
              <ul className="space-y-3">
                {[
                  "Intelligent workflow automation",
                  "Smart content generation",
                  "Predictive analytics and insights",
                  "Personalized customer journeys"
                ].map((item, index) => (
                  <motion.li
                    key={index}
                    className="flex items-start gap-2"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <div className="w-5 h-5 rounded-full bg-blue-500/20 flex items-center justify-center mt-1">
                      <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                    </div>
                    <span className="text-white/90 font-medium">{item}</span>
                  </motion.li>
                ))}
              </ul>
            </div>
            <div className="relative">
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl blur-md"
                animate={{
                  opacity: [0.5, 0.8, 0.5],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              />
              <motion.div
                className="relative z-10 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:shadow-xl hover:shadow-blue-500/20 hover:border-blue-500/30"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                {/* AI Dashboard Visualization */}
                <div className="rounded-lg border border-white/10 w-full bg-gradient-to-br from-gray-900/80 to-black/80 backdrop-blur-sm p-6 overflow-hidden">
                  {/* Dashboard Header */}
                  <div className="flex justify-between items-center mb-6">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-blue-500/30 flex items-center justify-center">
                        <div className="w-4 h-4 bg-blue-500/60 rounded-sm"></div>
                      </div>
                      <div className="space-y-1">
                        <div className="w-32 h-3 bg-white/20 rounded-full"></div>
                        <div className="w-20 h-2 bg-white/10 rounded-full"></div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <div className="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center">
                        <div className="w-4 h-4 bg-white/20 rounded-sm"></div>
                      </div>
                      <div className="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center">
                        <div className="w-4 h-4 bg-white/20 rounded-sm"></div>
                      </div>
                    </div>
                  </div>

                  {/* AI Insights */}
                  <div className="mb-6 p-3 bg-gradient-to-r from-blue-500/15 to-blue-500/5 backdrop-blur-sm rounded-lg border border-blue-500/20 hover:border-blue-500/30 transition-all duration-300">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-5 h-5 rounded-full bg-blue-500/30 flex items-center justify-center">
                        <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                      </div>
                      <div className="text-sm text-blue-400 font-medium">AI Recommendation</div>
                    </div>
                    <div className="space-y-2">
                      <div className="w-full h-2 bg-white/10 rounded-full"></div>
                      <div className="w-5/6 h-2 bg-white/10 rounded-full"></div>
                      <div className="w-4/6 h-2 bg-white/10 rounded-full"></div>
                    </div>
                  </div>

                  {/* Automation Flows */}
                  <div className="grid grid-cols-2 gap-3 mb-4">
                    <motion.div
                      className="p-3 bg-gradient-to-br from-purple-500/15 to-purple-500/5 backdrop-blur-sm rounded-lg border border-purple-500/20 hover:border-purple-500/30 hover:shadow-purple-500/20 hover:shadow-sm"
                      whileHover={{ scale: 1.03 }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-4 h-4 rounded-full bg-purple-500/30"></div>
                        <div className="text-xs text-white/90 font-medium">Email Sequence</div>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="text-lg font-bold text-white">86%</div>
                        <div className="text-xs text-green-400">+12%</div>
                      </div>
                    </motion.div>

                    <motion.div
                      className="p-3 bg-gradient-to-br from-aha-red/15 to-aha-red/5 backdrop-blur-sm rounded-lg border border-aha-red/20 hover:border-aha-red/30 hover:shadow-aha-red/20 hover:shadow-sm"
                      whileHover={{ scale: 1.03 }}
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-4 h-4 rounded-full bg-aha-red/30"></div>
                        <div className="text-xs text-white/90 font-medium">Lead Scoring</div>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="text-lg font-bold text-white">92%</div>
                        <div className="text-xs text-green-400">+8%</div>
                      </div>
                    </motion.div>
                  </div>

                  {/* Animated AI elements */}
                  <motion.div
                    className="absolute top-1/4 right-1/4 w-20 h-20 rounded-full bg-blue-500/10 blur-xl"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.3, 0.5, 0.3]
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                  />

                  <motion.div
                    className="absolute bottom-1/4 left-1/3 w-3 h-3 rounded-full bg-blue-500/50"
                    animate={{
                      y: [0, -20, 0],
                      opacity: [0.5, 1, 0.5]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                  />

                  <motion.div
                    className="absolute top-1/3 left-1/4 w-2 h-2 rounded-full bg-purple-500/50"
                    animate={{
                      x: [0, 20, 0],
                      opacity: [0.5, 1, 0.5]
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      repeatType: "reverse",
                      delay: 1
                    }}
                  />
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ModernFeatures;
