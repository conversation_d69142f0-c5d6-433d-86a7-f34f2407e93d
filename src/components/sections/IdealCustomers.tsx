import React from 'react';
import { motion } from 'framer-motion';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { useNavigate } from 'react-router-dom';
import {
  Briefcase,
  Users,
  Building,
  Zap,
  ArrowRight
} from 'lucide-react';

interface CustomerTypeProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  features: string[];
  delay?: number;
}

const CustomerTypeCard: React.FC<CustomerTypeProps> = ({ 
  icon, 
  title, 
  description, 
  features, 
  delay = 0 
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: delay * 0.1 }}
      viewport={{ once: true, margin: "-50px" }}
      className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 h-full transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/5 hover:border-aha-red/20 group"
    >
      <div className="w-12 h-12 rounded-lg bg-aha-red/10 flex items-center justify-center mb-4 text-aha-red transition-transform duration-300 group-hover:scale-110">
        {icon}
      </div>
      <h3 className="text-xl font-bold mb-3 text-aha-red">{title}</h3>
      <p className="text-white/90 mb-4 text-sm leading-relaxed font-medium">{description}</p>
      <ul className="space-y-2">
        {features.map((feature, index) => (
          <li key={index} className="flex items-start gap-2 text-sm">
            <div className="w-1.5 h-1.5 rounded-full bg-aha-red mt-2 flex-shrink-0"></div>
            <span className="text-gray-300">{feature}</span>
          </li>
        ))}
      </ul>
    </motion.div>
  );
};

const IdealCustomers: React.FC = () => {
  const navigate = useNavigate();
  
  const customerTypes = [
    {
      icon: <Briefcase size={24} />,
      title: "Solopreneurs",
      description: "All-in-one business management.",
      features: [
        "Client management & communication",
        "Automated follow-ups & scheduling",
        "Simple website & funnel builder",
        "Payment processing & invoicing"
      ]
    },
    {
      icon: <Users size={24} />,
      title: "Coaches & Consultants",
      description: "Client-focused automation.",
      features: [
        "Online appointment scheduling",
        "Client portal & course delivery",
        "Automated email sequences",
        "Payment & subscription management"
      ]
    },
    {
      icon: <Building size={24} />,
      title: "Small Agencies",
      description: "Scale efficiently.",
      features: [
        "Multi-client dashboard",
        "Team collaboration tools",
        "White-label client portals",
        "Advanced reporting & analytics"
      ]
    },
    {
      icon: <Zap size={24} />,
      title: "Local Businesses",
      description: "Enterprise-level tools.",
      features: [
        "Local SEO & Google My Business",
        "Review management system",
        "SMS marketing for customers",
        "Social media scheduling"
      ]
    }
  ];

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Subtle background elements that blend with hero */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.04),transparent_50%)]"></div>
        <div className="absolute bottom-0 left-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.03),transparent_50%)]"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="bg-aha-red/20 hover:bg-aha-red/30 text-aha-red border-none mb-6 py-1.5 px-4 mx-auto block w-fit font-medium">
            Perfect For
          </Badge>
          <h2 className="text-4xl md:text-5xl font-extrabold mb-6 tracking-tight font-gotham">
            Be one step ahead, whether you're<span className="text-aha-red">...</span>
          </h2>
          <p className="text-center text-white/90 max-w-3xl mx-auto text-lg md:text-xl font-medium leading-relaxed tracking-wide font-gotham">
            AHA-Innovations adapts to your business needs.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {customerTypes.map((type, index) => (
            <CustomerTypeCard
              key={index}
              icon={type.icon}
              title={type.title}
              description={type.description}
              features={type.features}
              delay={index}
            />
          ))}
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h2 className="text-white/90 mb-6 text-2xl md:text-3xl lg:text-4xl font-bold leading-relaxed tracking-wide font-gotham">
            READY TO SEE HOW AHA INNOVATIONS CAN TRANSFORM YOUR BUSINESS?
          </h2>
          <Button
            size="lg"
            className="bg-gradient-to-br from-aha-red/90 to-aha-darkred text-white text-lg px-8 py-6 rounded-full border border-white/10 hover:border-aha-red/30 transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/20 group"
            onClick={() => navigate('/signup')}
          >
            <span className="flex items-center gap-2 font-medium">
              Start Your Free Trial
              <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
            </span>
          </Button>
        </motion.div>
      </div>
    </section>
  );
};

export default IdealCustomers;
