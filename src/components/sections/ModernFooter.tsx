import React from 'react';
import { motion } from 'framer-motion';
import Logo from '../Logo';
import { Link } from 'react-router-dom';
import { Facebook, Instagram, Linkedin, Youtube } from 'lucide-react';
import { DotPattern } from '../ui/dot-pattern';

const ModernFooter: React.FC = () => {
  return (
    <footer className="py-16 px-4 relative overflow-hidden">
      <div className="container mx-auto relative z-10">
        {/* Floating glass container */}
        <motion.div
          className="glass-heavy rounded-3xl p-8 md:p-12 relative overflow-hidden border border-white/10"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          {/* Dot pattern texture overlay inside glass container */}
          <DotPattern
            width={26}
            height={26}
            cx={1}
            cy={1}
            cr={0.6}
            className="fill-white/[0.02] [mask-image:radial-gradient(600px_circle_at_center,white,transparent)]"
          />

          {/* Subtle animated glow */}
          <motion.div
            className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-aha-red/20 via-blue-500/20 to-purple-500/20"
            animate={{
              opacity: [0.2, 0.4, 0.2],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <div className="grid md:grid-cols-4 gap-12 relative z-10">
            <div className="md:col-span-1">
              <motion.div
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <Logo />
              </motion.div>
              <p className="text-white/80 mt-4 text-sm font-gotham leading-relaxed">
                AHA-Innovations – The all-in-one platform for business automation, growth, and productivity.
              </p>
            </div>

          <div className="md:col-span-3 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4 text-white font-gotham">Product</h3>
              <ul className="space-y-3">
                {[
                  { label: "Features", path: "/features" },
                  { label: "Pricing", path: "/pricing" },
                  { label: "Integrations", path: "/integrations" },
                  { label: "Case Studies", path: "/case-studies" },
                  { label: "Documentation", path: "/docs" }
                ].map((link, index) => (
                  <motion.li
                    key={index}
                    whileHover={{ x: 5 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <Link
                      to={link.path}
                      className="text-white/70 hover:text-white transition-colors duration-300 text-sm font-gotham"
                    >
                      {link.label}
                    </Link>
                  </motion.li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4 text-white font-gotham">Company</h3>
              <ul className="space-y-3">
                {[
                  { label: "Community", path: "https://tutorials.aha-university.org/communities/groups/aha-community/home?invite=684a52e768e341175153da67", external: true },
                  { label: "Careers", path: "/careers" },
                  { label: "Blog", path: "/blog" },
                  { label: "Press", path: "/press" },
                  { label: "Contact", path: "/contact" }
                ].map((link, index) => (
                  <motion.li
                    key={index}
                    whileHover={{ x: 5 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    {link.external ? (
                      <a
                        href={link.path}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-white/70 hover:text-white transition-colors duration-300 text-sm font-gotham"
                      >
                        {link.label}
                      </a>
                    ) : (
                      <Link
                        to={link.path}
                        className="text-white/70 hover:text-white transition-colors duration-300 text-sm font-gotham"
                      >
                        {link.label}
                      </Link>
                    )}
                  </motion.li>
                ))}
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-4 text-white font-gotham">Legal</h3>
              <ul className="space-y-3">
                {[
                  { label: "Terms of Service", path: "/terms" },
                  { label: "Privacy Policy", path: "/privacy" },
                  { label: "Cookie Policy", path: "/cookies" },
                  { label: "GDPR", path: "/gdpr" },
                  { label: "Security", path: "/security" }
                ].map((link, index) => (
                  <motion.li
                    key={index}
                    whileHover={{ x: 5 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <Link
                      to={link.path}
                      className="text-white/70 hover:text-white transition-colors duration-300 text-sm font-gotham"
                    >
                      {link.label}
                    </Link>
                  </motion.li>
                ))}
              </ul>
            </div>
            </div>
          </div>

          <div className="mt-16 pt-8 border-t border-white/10 flex flex-col md:flex-row justify-between items-center gap-6 relative z-10">
            <div className="text-white/60 text-sm font-gotham">
              © {new Date().getFullYear()} AHA-Innovations. All rights reserved.
            </div>

          <div className="flex gap-4">
            {[
              { icon: <Facebook size={16} />, label: "Facebook", url: "https://www.facebook.com/ahainnovationsofficial/" },
              { icon: <Youtube size={16} />, label: "YouTube", url: "https://www.youtube.com/@AHA-Innovations-Official" },
              { icon: <Instagram size={16} />, label: "Instagram", url: "https://www.instagram.com/ahainnovationsofficial/" },
              { icon: <Linkedin size={16} />, label: "LinkedIn", url: "https://www.linkedin.com/showcase/aha-innovations" },
              { icon: <div className="text-xs font-bold">T</div>, label: "TikTok", url: "https://www.tiktok.com/@aha.innovations" }
            ].map((social, index) => (
              <motion.a
                key={index}
                href={social.url}
                target="_blank"
                rel="noopener noreferrer"
                aria-label={social.label}
                className="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center text-white/70 hover:text-white border border-white/10"
                whileHover={{
                  scale: 1.2,
                  backgroundColor: "rgba(255,255,255,0.1)"
                }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 10
                }}
              >
                {social.icon}
              </motion.a>
            ))}
          </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
};

export default ModernFooter;
