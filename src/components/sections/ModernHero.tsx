import React, { useRef, useEffect, useState } from 'react';
import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { DotPattern } from '../ui/dot-pattern';
import { FlipWords } from '../ui/flip-words';
import { useNavigate } from 'react-router-dom';
import { ArrowRight, Code, Zap, Sparkles } from 'lucide-react';
import { useIsMobile } from '../../hooks/use-mobile';
import AHALiquidButton from '../ui/aha-liquid-button';
import VideoPlayer from '../ui/video-player';

interface ModernHeroProps {
  title: string;
  subtitle: React.ReactNode;
  description: string;
  ctaText: string;
  ctaLink: string;
}

const ModernHero: React.FC<ModernHeroProps> = ({
  title,
  subtitle,
  description,
  ctaText,
  ctaLink,
}) => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const containerRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const [hasScrolled, setHasScrolled] = useState(false);

  // Customer avatars for hero social proof
  const heroAvatars = [
    "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
  ];

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  // Enhanced parallax effects - subtle movement without aggressive fading
  const y = useTransform(scrollYProgress, [0, 1], [0, 100]);
  const opacity = useTransform(scrollYProgress, [0, 0.9], [1, 0.95]); // Keep content visible much longer
  const scale = useTransform(scrollYProgress, [0, 0.8], [1, 0.98]);
  const blur = useTransform(scrollYProgress, [0, 0.8], [0, 2]);

  // Typography scaling effects - subtle movement without aggressive fading
  const titleScale = useTransform(scrollYProgress, [0, 0.3], [1, 1.05]);
  const titleY = useTransform(scrollYProgress, [0, 0.3], [0, -20]);
  const titleOpacity = useTransform(scrollYProgress, [0, 0.8], [1, 0.9]);

  // Background elements movement with enhanced effects
  const bgCircle1Y = useTransform(scrollYProgress, [0, 1], [0, -100]);
  const bgCircle2Y = useTransform(scrollYProgress, [0, 1], [0, -150]);
  const bgCircle3Y = useTransform(scrollYProgress, [0, 1], [0, -50]);

  // Background color accent intensity
  const bgAccentOpacity = useTransform(scrollYProgress, [0, 0.2], [0.05, 0.3]);

  // Additional transform values for elements
  const centerGradientOpacity = useTransform(scrollYProgress, [0, 0.2], [0.1, 0.4]);
  const rightGradientOpacity = useTransform(scrollYProgress, [0, 0.2], [0.4, 0.7]);
  const leftGradientOpacity = useTransform(scrollYProgress, [0, 0.2], [0.4, 0.7]);

  // Additional accent elements transforms
  const accentOpacity = useTransform(scrollYProgress, [0, 0.1, 0.2], [0, 0.3, 0.5]);
  const accentScale = useTransform(scrollYProgress, [0, 0.2], [0.8, 1.2]);

  // Typography and paragraph transforms - keep text readable
  const paragraphOpacity = useTransform(scrollYProgress, [0, 0.7], [1, 0.95]);
  const paragraphY = useTransform(scrollYProgress, [0, 0.3], [0, 10]);

  // Dashboard preview transforms
  const dashboardScale = useTransform(scrollYProgress, [0, 0.2], [1, hasScrolled ? 1.1 : 1]);
  const dashboardY = useTransform(scrollYProgress, [0, 0.2], [0, hasScrolled ? -20 : 0]);
  const dashboardGlowOpacity = useTransform(scrollYProgress, [0, 0.15], [0.6, 1]);
  const dashboardImageScale = useTransform(scrollYProgress, [0, 0.2], [1, hasScrolled ? 1.05 : 1]);

  // Feature card transforms
  const featureCardScale = useTransform(scrollYProgress, [0, 0.15], [1, 1.05]);
  const featureCard1Scale = useTransform(scrollYProgress, [0, 0.1], [1, hasScrolled ? 1.05 : 1]);
  const featureCard2Scale = useTransform(scrollYProgress, [0, 0.1], [1, hasScrolled ? 1.08 : 1]);
  const featureCard3Scale = useTransform(scrollYProgress, [0, 0.1], [1, hasScrolled ? 1.05 : 1]);

  // Additional glow effects
  const topGlowOpacity = useTransform(scrollYProgress, [0, 0.15], [0.3, 0.7]);
  const bottomGlowOpacity = useTransform(scrollYProgress, [0, 0.15], [0.2, 0.6]);
  const centerGlowOpacity = useTransform(scrollYProgress, [0, 0.15], [0, 0.5]);

  // Listen for scroll to trigger animations
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50 && !hasScrolled) {
        setHasScrolled(true);
      } else if (window.scrollY <= 50 && hasScrolled) {
        setHasScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasScrolled]);

  return (
    <section
      ref={containerRef}
      className="relative overflow-hidden min-h-[90vh] flex items-center bg-black"
    >
      {/* Enhanced background elements with dynamic color accents */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Dot pattern texture overlay */}
        <DotPattern
          width={32}
          height={32}
          cx={1}
          cy={1}
          cr={1}
          className="fill-white/[0.02] [mask-image:radial-gradient(800px_circle_at_center,white,transparent)]"
        />

        {/* Soft ambient glow from upper left - like Baidu reference */}
        <motion.div
          className="absolute -top-[25%] -left-[25%] w-[100%] h-[100%]"
          style={{
            background: `radial-gradient(ellipse 800px 600px at 25% 25%,
              rgba(255, 255, 255, 0.08) 0%,
              rgba(255, 255, 255, 0.04) 30%,
              rgba(255, 255, 255, 0.02) 50%,
              transparent 70%)`
          }}
          animate={{
            opacity: [0.8, 1, 0.8]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />

        {/* Main background gradients with scroll-based intensity */}
        <motion.div
          className="absolute top-1/4 left-1/4 w-64 md:w-96 h-64 md:h-96 bg-gradient-to-br from-aha-red to-transparent rounded-full blur-3xl"
          style={{ opacity: bgAccentOpacity }}
        ></motion.div>

        <motion.div
          className="absolute bottom-1/4 right-1/4 w-64 md:w-96 h-64 md:h-96 bg-gradient-to-tr from-aha-red to-transparent rounded-full blur-3xl"
          style={{ opacity: bgAccentOpacity }}
        ></motion.div>

        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[120%] h-[120%] bg-gradient-radial from-aha-red via-transparent to-transparent"
          style={{ opacity: centerGradientOpacity }}
        ></motion.div>

        {/* Additional animated elements with enhanced effects */}
        <motion.div
          className="absolute -top-[30%] -right-[10%] w-[70%] h-[70%] rounded-full bg-gradient-to-r from-aha-red/30 via-aha-red/10 to-blue-500/5 blur-[120px]"
          style={{
            opacity: rightGradientOpacity
          }}
          animate={{
            rotate: 360,
            scale: [1, 1.1, 1],
          }}
          transition={{
            rotate: {
              duration: isMobile ? 60 : 40,
              repeat: Infinity,
              ease: "linear"
            },
            scale: {
              duration: 15,
              repeat: Infinity,
              repeatType: "reverse"
            }
          }}
        />

        <motion.div
          className="absolute -bottom-[30%] -left-[10%] w-[70%] h-[70%] rounded-full bg-gradient-to-r from-blue-500/5 via-aha-red/10 to-aha-red/30 blur-[120px]"
          style={{
            opacity: leftGradientOpacity
          }}
          animate={{
            rotate: -360,
            scale: [1, 1.2, 1],
          }}
          transition={{
            rotate: {
              duration: isMobile ? 70 : 50,
              repeat: Infinity,
              ease: "linear"
            },
            scale: {
              duration: 20,
              repeat: Infinity,
              repeatType: "reverse"
            }
          }}
        />

        {/* Additional accent elements that appear on scroll */}
        <motion.div
          className="absolute top-1/3 right-1/3 w-40 h-40 md:w-60 md:h-60 bg-aha-red/20 rounded-full blur-3xl"
          style={{
            opacity: accentOpacity,
            scale: accentScale
          }}
        />
      </div>

      {/* Content with enhanced scroll effects */}
      <motion.div
        className="container mx-auto px-4 relative z-10 pt-24 pb-12 md:pt-32 md:pb-20"
        style={{
          y,
          opacity,
          scale
        }}
      >
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-6 md:mb-8"
          >
            <Badge className="bg-white/10 hover:bg-white/20 text-white border-none py-1.5 px-6 mb-6 md:mb-8 font-medium mx-auto block w-fit animate-fade-in-up text-sm">
              {subtitle}
            </Badge>
          </motion.div>

          {/* Enhanced typography with dramatic zoom effect on scroll */}
          <motion.div
            style={{
              scale: titleScale,
              y: titleY,
              opacity: titleOpacity
            }}
          >
            <motion.h1
              ref={titleRef}
              className="text-xl xs:text-2xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-ultra text-center mb-8 md:mb-10 tracking-tight font-gotham leading-tight"
              style={{
                fontWeight: 950,
                letterSpacing: '-0.01em',
                lineHeight: '1.1'
              }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <div className="w-fit mx-auto text-center">
                <span className="flex flex-row whitespace-nowrap justify-center items-center">
                  <span className="text-white">Build</span>
                  <span className="text-aha-red font-ultra font-gotham ml-2 flex-shrink">
                    <span className="inline-block w-[90px] xs:w-[110px] sm:w-[200px] md:w-[260px] lg:w-[320px] xl:w-[350px]">
                      <FlipWords
                        words={["Websites", "Funnels", "Workflows", "Automations"]}
                        duration={2500}
                        className="text-aha-red font-ultra font-gotham"
                        style={{
                          fontWeight: 950,
                          letterSpacing: '-0.01em',
                          margin: 0,
                          left: 'unset',
                          transform: 'unset',
                          position: 'unset',
                          display: 'inline-block',
                          textAlign: 'center'
                        }}
                      />
                    </span>
                  </span>
                </span>
                <span className="block text-white mt-2">with AHA-Innovations</span>
              </div>
            </motion.h1>
          </motion.div>

          <motion.p
            className="text-center text-white/90 mb-12 md:mb-16 max-w-4xl mx-auto text-lg md:text-2xl lg:text-2xl font-medium leading-relaxed tracking-wide font-gotham"
            style={{
              opacity: paragraphOpacity,
              y: paragraphY
            }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {description}
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row justify-center gap-4 animate-fade-in-up"
            style={{ animationDelay: '0.5s' }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
              <AHALiquidButton
                onClick={() => navigate(ctaLink)}
                size={isMobile ? "md" : "lg"}
              >
                <span className="text-sm sm:text-base font-bold whitespace-nowrap">{ctaText}</span>
              </AHALiquidButton>

              <Button
                size={isMobile ? "default" : "lg"}
                variant="outline"
                className={`bg-white/5 backdrop-blur-sm border border-white/10 text-white hover:bg-white/10 rounded-full transition-all duration-300 hover:border-aha-red/30 hover:shadow-xl hover:shadow-aha-red/10 ${
                  isMobile ? 'px-8 py-5 text-base min-w-[140px]' : 'text-lg px-8 py-6'
                }`}
                onClick={() => navigate('/features')}
              >
                <span className="flex items-center justify-center font-medium">
                  Learn More
                </span>
              </Button>
            </div>
          </motion.div>

          {/* Dashboard Preview with Enhanced Zoom Effects */}
          <motion.div
            className="mt-12 md:mt-16 relative"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            style={{
              scale: dashboardScale,
              y: dashboardY
            }}
          >
            <VideoPlayer src="/landing-page-video-web.mp4" />
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
};

export default ModernHero;
