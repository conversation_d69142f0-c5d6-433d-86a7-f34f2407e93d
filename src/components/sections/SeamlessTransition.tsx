import React from 'react';
import { motion } from 'framer-motion';

interface SeamlessTransitionProps {
  className?: string;
}

const SeamlessTransition: React.FC<SeamlessTransitionProps> = ({ className = "" }) => {
  return (
    <div className={`relative ${className}`}>
      {/* Seamless gradient transition from hero to trusted section */}
      <div className="absolute inset-0 bg-gradient-to-b from-aha-dark via-aha-dark/95 to-aha-dark/90" />
      
      {/* Subtle dot pattern overlay */}
      <div className="absolute inset-0 opacity-10">
        <div 
          className="absolute inset-0" 
          style={{
            backgroundImage: 'radial-gradient(circle, #ffffff 1px, transparent 1px)',
            backgroundSize: '40px 40px'
          }}
        />
      </div>
      
      {/* Animated connecting line */}
      <motion.div
        initial={{ scaleX: 0 }}
        whileInView={{ scaleX: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 1.5, ease: "easeInOut" }}
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-px h-24 bg-gradient-to-b from-transparent via-white/20 to-transparent origin-center"
      />
      
      {/* Floating particles for depth */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 0.3, y: 0 }}
            viewport={{ once: true }}
            transition={{ 
              duration: 2, 
              delay: i * 0.2,
              repeat: Infinity,
              repeatType: "reverse",
              repeatDelay: 3
            }}
            className="absolute w-1 h-1 bg-white/30 rounded-full"
            style={{
              left: `${20 + i * 12}%`,
              top: `${30 + (i % 2) * 40}%`,
            }}
          />
        ))}
      </div>
      
      {/* Content spacer */}
      <div className="h-16 md:h-24" />
    </div>
  );
};

export default SeamlessTransition;
