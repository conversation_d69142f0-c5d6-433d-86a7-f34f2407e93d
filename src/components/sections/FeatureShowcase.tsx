import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Badge } from '../ui/badge';
import { TextEffect } from '../ui/text-effect';
import { Button } from '../ui/button';
import { ArrowRight, Check, Users, Settings, Globe, Calendar, Mail, Kanban } from 'lucide-react';

interface FeatureTab {
  id: string;
  title: string;
  icon: React.ReactNode;
  description: string;
  image: string;
  color: string;
  features: string[];
}

const FeatureShowcase: React.FC = () => {
  const features: FeatureTab[] = [
    {
      id: "crm",
      title: "CRM & Contact Management",
      icon: <Users size={24} />,
      description: "Manage all your contacts in one place with our powerful CRM. Track interactions, set follow-ups, and never miss an opportunity.",
      image: "/CRM.png",
      color: "aha-red",
      features: [
        "Visual pipeline management",
        "Contact organization with custom fields",
        "Automated follow-up sequences",
        "Deal tracking and forecasting"
      ]
    },
    {
      id: "automation",
      title: "Marketing Automation",
      icon: <Settings size={24} />,
      description: "Create sophisticated automation workflows that nurture leads and convert customers without manual intervention.",
      image: "/Automation.png",
      color: "blue-500",
      features: [
        "Visual workflow builder",
        "Trigger-based automations",
        "Multi-channel campaigns",
        "Performance analytics"
      ]
    },
    {
      id: "email",
      title: "Email Marketing",
      icon: <Mail size={24} />,
      description: "Design and send beautiful email campaigns that engage your audience and drive conversions.",
      image: "/Email marketing.png",
      color: "purple-500",
      features: [
        "Drag-and-drop email builder",
        "List segmentation",
        "A/B testing",
        "Detailed analytics"
      ]
    },
    {
      id: "calendar",
      title: "Calendar & Appointments",
      icon: <Calendar size={24} />,
      description: "Let clients book appointments directly into your calendar with automated reminders and follow-ups.",
      image: "/Calendar app.png",
      color: "green-500",
      features: [
        "Online booking pages",
        "Calendar sync",
        "Automated reminders",
        "Group scheduling"
      ]
    },
    {
      id: "kanban",
      title: "Project Management",
      icon: <Kanban size={24} />,
      description: "Organize your projects and tasks with our intuitive Kanban boards and task management tools.",
      image: "/Kanban.png",
      color: "amber-500",
      features: [
        "Kanban boards",
        "Task assignments",
        "Progress tracking",
        "File attachments"
      ]
    },
    {
      id: "website",
      title: "Website & Funnel Builder",
      icon: <Globe size={24} />,
      description: "Build beautiful, high-converting websites and funnels with our drag-and-drop builder. No coding required.",
      image: "/Website Builder.png",
      color: "cyan-500",
      features: [
        "Drag-and-drop builder",
        "Mobile-responsive templates",
        "A/B testing",
        "Custom domains"
      ]
    }
  ];

  const [activeTab, setActiveTab] = useState(features[0].id);
  const activeFeature = features.find(f => f.id === activeTab) || features[0];

  return (
    <section className="py-24 relative overflow-hidden" style={{ background: '#0F0F0F' }}>
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-[#0F0F0F] via-[#0F0F0F] to-[#0F0F0F]"></div>
        <motion.div
          className="absolute -top-[30%] -right-[10%] w-[70%] h-[70%] rounded-full bg-gradient-to-r from-aha-red/10 via-purple-600/5 to-blue-500/5 blur-[120px] opacity-60"
          animate={{
            rotate: 360,
            scale: [1, 1.1, 1],
          }}
          transition={{
            rotate: {
              duration: 40,
              repeat: Infinity,
              ease: "linear"
            },
            scale: {
              duration: 15,
              repeat: Infinity,
              repeatType: "reverse"
            }
          }}
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="bg-aha-red/20 hover:bg-aha-red/30 text-aha-red border-none py-1 md:py-1.5 px-3 md:px-4 mb-4 md:mb-6 font-medium mx-auto block w-fit text-xs md:text-sm">
            Powerful Features
          </Badge>
          <TextEffect
            per="word"
            as="h2"
            className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 md:mb-6 tracking-tight font-gotham"
            variants={{
              container: {
                hidden: { opacity: 0 },
                visible: {
                  opacity: 1,
                  transition: { staggerChildren: 0.07, delayChildren: 0.7 },
                },
                exit: {
                  transition: { staggerChildren: 0.05, staggerDirection: -1 },
                },
              },
              item: {
                hidden: {
                  opacity: 0,
                  filter: 'blur(12px)',
                  y: 25,
                  rotateX: -45,
                },
                visible: {
                  opacity: 1,
                  filter: 'blur(0px)',
                  y: 0,
                  rotateX: 0,
                  transition: {
                    duration: 0.8,
                    ease: [0.25, 0.46, 0.45, 0.94],
                  },
                },
                exit: {
                  opacity: 0,
                  filter: 'blur(8px)',
                  y: -15,
                  rotateX: 45,
                  transition: {
                    duration: 0.5,
                  },
                },
              },
            }}
          >
            Everything you need to grow your business
          </TextEffect>
          <p className="text-center text-white/90 max-w-4xl mx-auto text-base md:text-xl lg:text-xl font-medium leading-relaxed tracking-wide px-4">
            AHA-Innovations brings together all the tools you need in one seamless platform.
            No more juggling between different software solutions.
          </p>
        </motion.div>

        {/* Feature tabs */}
        <div className="grid md:grid-cols-12 gap-8 items-start">
          {/* Tab navigation */}
          <div className="md:col-span-4 lg:col-span-3 space-y-2">
            {features.map((feature) => (
              <motion.button
                key={feature.id}
                className={`w-full text-left p-2 md:p-4 rounded-lg transition-all duration-300 flex items-center gap-2 md:gap-3 ${
                  activeTab === feature.id
                    ? `bg-${feature.color}/10 border border-${feature.color}/20`
                    : 'bg-white/5 hover:bg-white/10 border border-transparent'
                }`}
                onClick={() => setActiveTab(feature.id)}
                whileHover={{ x: 5 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <div className={`w-8 h-8 md:w-10 md:h-10 rounded-lg bg-${feature.color}/20 flex items-center justify-center text-${feature.color}`}>
                  <span className="md:hidden">{React.cloneElement(feature.icon as React.ReactElement, { size: 16 })}</span>
                  <span className="hidden md:block">{feature.icon}</span>
                </div>
                <span className="font-medium text-sm md:text-base">{feature.title}</span>
              </motion.button>
            ))}
          </div>

          {/* Feature content */}
          <div className="md:col-span-8 lg:col-span-9">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
                className="bg-black/20 backdrop-blur-sm border border-white/10 rounded-xl p-6 md:p-8"
              >
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div>
                    <h3 className="text-xl md:text-2xl font-bold mb-2 md:mb-4">{activeFeature.title}</h3>
                    <p className="text-white/90 mb-4 md:mb-6 text-base md:text-lg font-medium leading-relaxed">{activeFeature.description}</p>

                    <ul className="space-y-2 md:space-y-3 mb-6 md:mb-8">
                      {activeFeature.features.map((feature, index) => (
                        <motion.li
                          key={index}
                          className="flex items-start gap-1.5 md:gap-2"
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                        >
                          <div className={`w-4 h-4 md:w-5 md:h-5 rounded-full bg-${activeFeature.color}/20 flex items-center justify-center flex-shrink-0 mt-0.5 md:mt-1`}>
                            <Check size={10} className={`text-${activeFeature.color} md:hidden`} />
                            <Check size={12} className={`text-${activeFeature.color} hidden md:block`} />
                          </div>
                          <span className="text-sm md:text-base">{feature}</span>
                        </motion.li>
                      ))}
                    </ul>

                    <Button
                      className={`bg-${activeFeature.color} hover:bg-${activeFeature.color}/90 text-white rounded-full px-4 md:px-6 py-1.5 md:py-2 text-sm md:text-base group relative overflow-hidden`}
                      onClick={() => window.location.href = "/features"}
                    >
                      <span className="relative z-10 flex items-center gap-1 md:gap-2">
                        Learn More
                        <ArrowRight className="w-3 h-3 md:w-4 md:h-4 transition-transform duration-300 group-hover:translate-x-1" />
                      </span>
                      <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
                    </Button>
                  </div>

                  <div className="relative">
                    <div className={`absolute -inset-4 bg-gradient-to-r from-${activeFeature.color}/10 via-${activeFeature.color}/5 to-transparent rounded-2xl blur-xl opacity-70`}></div>
                    <div className="relative rounded-xl border border-white/10 overflow-hidden shadow-xl">
                      {/* Mockup frame overlay */}
                      <div className="absolute top-0 left-0 right-0 h-8 bg-black/50 backdrop-blur-sm z-20 flex items-center px-4">
                        <div className="flex gap-2">
                          <div className="w-3 h-3 rounded-full bg-red-500"></div>
                          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                          <div className="w-3 h-3 rounded-full bg-green-500"></div>
                        </div>
                        <div className="text-xs text-white/90 mx-auto font-medium">AHA-Innovations {activeFeature.title}</div>
                      </div>
                      {/* Abstract Feature Visualization */}
                      <div className="w-full relative z-10 pt-8 bg-gradient-to-br from-gray-900 to-black p-6 min-h-[250px]">
                        {activeTab === "crm" && (
                          <div className="relative h-full">
                            {/* CRM Pipeline visualization */}
                            <div className="grid grid-cols-4 gap-2 mb-4">
                              <motion.div
                                className="h-20 bg-gradient-to-b from-aha-red/20 to-aha-red/5 rounded-lg p-2"
                                animate={{ y: [0, -3, 0] }}
                                transition={{ duration: 3, repeat: Infinity, repeatType: "reverse" }}
                              >
                                <div className="text-xs text-white/90 mb-1 font-medium">New</div>
                                <div className="w-full h-3 bg-white/10 rounded-sm mb-1"></div>
                                <div className="w-3/4 h-3 bg-white/10 rounded-sm"></div>
                              </motion.div>

                              <motion.div
                                className="h-20 bg-gradient-to-b from-blue-500/20 to-blue-500/5 rounded-lg p-2"
                                animate={{ y: [0, -3, 0] }}
                                transition={{ duration: 3.5, repeat: Infinity, repeatType: "reverse", delay: 0.5 }}
                              >
                                <div className="text-xs text-white/90 mb-1 font-medium">Contact</div>
                                <div className="w-full h-3 bg-white/10 rounded-sm mb-1"></div>
                                <div className="w-3/4 h-3 bg-white/10 rounded-sm"></div>
                              </motion.div>

                              <motion.div
                                className="h-20 bg-gradient-to-b from-purple-500/20 to-purple-500/5 rounded-lg p-2"
                                animate={{ y: [0, -3, 0] }}
                                transition={{ duration: 2.8, repeat: Infinity, repeatType: "reverse", delay: 1 }}
                              >
                                <div className="text-xs text-white/90 mb-1 font-medium">Qualify</div>
                                <div className="w-full h-3 bg-white/10 rounded-sm mb-1"></div>
                                <div className="w-3/4 h-3 bg-white/10 rounded-sm"></div>
                              </motion.div>

                              <motion.div
                                className="h-20 bg-gradient-to-b from-green-500/20 to-green-500/5 rounded-lg p-2"
                                animate={{ y: [0, -3, 0] }}
                                transition={{ duration: 3.2, repeat: Infinity, repeatType: "reverse", delay: 1.5 }}
                              >
                                <div className="text-xs text-white/90 mb-1 font-medium">Won</div>
                                <div className="w-full h-3 bg-white/10 rounded-sm mb-1"></div>
                                <div className="w-3/4 h-3 bg-white/10 rounded-sm"></div>
                              </motion.div>
                            </div>

                            {/* Contact list */}
                            <div className="space-y-2">
                              <div className="flex justify-between items-center p-2 bg-white/5 rounded-lg">
                                <div className="flex items-center gap-2">
                                  <div className="w-6 h-6 rounded-full bg-aha-red/30"></div>
                                  <div className="w-24 h-3 bg-white/20 rounded-full"></div>
                                </div>
                                <div className="w-16 h-3 bg-white/10 rounded-full"></div>
                              </div>
                              <div className="flex justify-between items-center p-2 bg-white/5 rounded-lg">
                                <div className="flex items-center gap-2">
                                  <div className="w-6 h-6 rounded-full bg-blue-500/30"></div>
                                  <div className="w-24 h-3 bg-white/20 rounded-full"></div>
                                </div>
                                <div className="w-16 h-3 bg-white/10 rounded-full"></div>
                              </div>
                            </div>
                          </div>
                        )}

                        {activeTab === "automation" && (
                          <div className="relative h-full">
                            {/* Automation workflow visualization */}
                            <div className="flex flex-col items-center">
                              <motion.div
                                className="w-32 h-16 bg-gradient-to-r from-blue-500/20 to-blue-500/5 rounded-lg flex items-center justify-center mb-4 relative"
                                whileHover={{ scale: 1.05 }}
                              >
                                <div className="text-sm text-white/90 font-medium">Trigger</div>
                                <motion.div
                                  className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-0.5 h-4 bg-white/20"
                                  animate={{ height: [3, 4, 3] }}
                                  transition={{ duration: 1.5, repeat: Infinity }}
                                />
                              </motion.div>

                              <div className="grid grid-cols-2 gap-4 mb-4 w-full max-w-xs">
                                <motion.div
                                  className="h-16 bg-gradient-to-r from-purple-500/20 to-purple-500/5 rounded-lg flex items-center justify-center relative"
                                  whileHover={{ scale: 1.05 }}
                                >
                                  <div className="text-sm text-white/90 font-medium">Email</div>
                                  <motion.div
                                    className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-0.5 h-4 bg-white/20"
                                    animate={{ height: [3, 4, 3] }}
                                    transition={{ duration: 1.5, repeat: Infinity, delay: 0.5 }}
                                  />
                                </motion.div>

                                <motion.div
                                  className="h-16 bg-gradient-to-r from-aha-red/20 to-aha-red/5 rounded-lg flex items-center justify-center relative"
                                  whileHover={{ scale: 1.05 }}
                                >
                                  <div className="text-sm text-white/90 font-medium">SMS</div>
                                  <motion.div
                                    className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-0.5 h-4 bg-white/20"
                                    animate={{ height: [3, 4, 3] }}
                                    transition={{ duration: 1.5, repeat: Infinity, delay: 0.7 }}
                                  />
                                </motion.div>
                              </div>

                              <motion.div
                                className="w-32 h-16 bg-gradient-to-r from-green-500/20 to-green-500/5 rounded-lg flex items-center justify-center"
                                whileHover={{ scale: 1.05 }}
                              >
                                <div className="text-sm text-white/90 font-medium">Action</div>
                              </motion.div>
                            </div>

                            {/* Animated flow */}
                            <motion.div
                              className="absolute top-[25%] left-[45%] w-2 h-2 rounded-full bg-blue-500"
                              animate={{
                                y: [0, 50, 100],
                                opacity: [1, 0.8, 0]
                              }}
                              transition={{ duration: 3, repeat: Infinity }}
                            />
                          </div>
                        )}

                        {activeTab === "email" && (
                          <div className="relative h-full">
                            {/* Email builder visualization */}
                            <div className="flex h-full">
                              {/* Template sidebar */}
                              <div className="w-1/4 border-r border-white/10 pr-2">
                                <div className="text-xs text-white/90 mb-2 font-medium">Templates</div>
                                <div className="space-y-2">
                                  <div className="h-12 bg-purple-500/20 rounded-md"></div>
                                  <div className="h-12 bg-aha-red/20 rounded-md"></div>
                                  <div className="h-12 bg-blue-500/20 rounded-md"></div>
                                </div>
                              </div>

                              {/* Email canvas */}
                              <div className="w-3/4 pl-3">
                                <div className="text-xs text-white/90 mb-2 font-medium">Email Editor</div>
                                <div className="border border-white/10 rounded-md p-3 bg-white/5">
                                  <div className="w-full h-4 bg-white/20 rounded-full mb-2"></div>
                                  <div className="w-3/4 h-3 bg-white/10 rounded-full mb-4"></div>
                                  <div className="w-full h-20 bg-gradient-to-r from-aha-red/10 to-purple-500/10 rounded-md mb-4"></div>
                                  <div className="w-full h-3 bg-white/10 rounded-full mb-2"></div>
                                  <div className="w-5/6 h-3 bg-white/10 rounded-full mb-2"></div>
                                  <div className="w-full h-3 bg-white/10 rounded-full mb-4"></div>
                                  <div className="w-24 h-8 mx-auto bg-aha-red/30 rounded-md"></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}

                        {activeTab === "calendar" && (
                          <div className="relative h-full">
                            {/* Calendar visualization */}
                            <div className="grid grid-cols-7 gap-1 mb-4">
                              {["M", "T", "W", "T", "F", "S", "S"].map((day, i) => (
                                <div key={i} className="text-center text-xs text-white/90 font-medium">{day}</div>
                              ))}
                              {Array.from({ length: 35 }).map((_, i) => {
                                const hasEvent = [4, 12, 17, 23, 28].includes(i);
                                return (
                                  <motion.div
                                    key={i}
                                    className={`aspect-square rounded-md flex items-center justify-center text-xs ${
                                      hasEvent ? 'bg-aha-red/20' : 'bg-white/5'
                                    }`}
                                    whileHover={{ scale: 1.1, backgroundColor: hasEvent ? "rgba(234, 56, 76, 0.3)" : "rgba(255, 255, 255, 0.1)" }}
                                  >
                                    {i + 1}
                                  </motion.div>
                                );
                              })}
                            </div>

                            {/* Appointments */}
                            <div className="space-y-2">
                              <div className="text-xs text-white/90 mb-1 font-medium">Upcoming Appointments</div>
                              <div className="p-2 bg-aha-red/10 rounded-lg">
                                <div className="flex justify-between items-center">
                                  <div className="w-32 h-3 bg-white/20 rounded-full"></div>
                                  <div className="text-xs text-white/90 font-medium">9:00 AM</div>
                                </div>
                              </div>
                              <div className="p-2 bg-blue-500/10 rounded-lg">
                                <div className="flex justify-between items-center">
                                  <div className="w-32 h-3 bg-white/20 rounded-full"></div>
                                  <div className="text-xs text-white/90 font-medium">2:30 PM</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}

                        {activeTab === "kanban" && (
                          <div className="relative h-full">
                            {/* Kanban board visualization */}
                            <div className="flex gap-2 overflow-x-auto pb-2">
                              {["To Do", "In Progress", "Review", "Done"].map((column, i) => (
                                <div key={i} className="min-w-[120px] flex-shrink-0">
                                  <div className="text-xs text-white/90 mb-2 font-medium">{column}</div>
                                  <div className="space-y-2">
                                    {Array.from({ length: i === 1 ? 3 : i === 2 ? 1 : 2 }).map((_, j) => (
                                      <motion.div
                                        key={j}
                                        className="p-2 bg-white/5 rounded-md"
                                        whileHover={{ scale: 1.05, backgroundColor: "rgba(255, 255, 255, 0.1)" }}
                                        drag
                                        dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
                                      >
                                        <div className="w-full h-2 bg-white/20 rounded-full mb-1"></div>
                                        <div className="w-3/4 h-2 bg-white/10 rounded-full"></div>
                                      </motion.div>
                                    ))}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {activeTab === "website" && (
                          <div className="relative h-full">
                            {/* Website builder visualization */}
                            <div className="flex h-full">
                              {/* Elements sidebar */}
                              <div className="w-1/4 border-r border-white/10 pr-2">
                                <div className="text-xs text-white/90 mb-2 font-medium">Elements</div>
                                <div className="space-y-2">
                                  <div className="h-8 bg-blue-500/20 rounded-md"></div>
                                  <div className="h-8 bg-aha-red/20 rounded-md"></div>
                                  <div className="h-8 bg-purple-500/20 rounded-md"></div>
                                  <div className="h-8 bg-green-500/20 rounded-md"></div>
                                </div>
                              </div>

                              {/* Canvas */}
                              <div className="w-3/4 pl-3">
                                <div className="text-xs text-white/90 mb-2 font-medium">Canvas</div>
                                <div className="border border-white/10 rounded-md bg-white/5 h-[180px] relative">
                                  {/* Header */}
                                  <div className="h-8 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-transparent px-3 flex items-center">
                                    <div className="w-6 h-6 rounded-full bg-blue-500/20 mr-2"></div>
                                    <div className="w-16 h-2 bg-white/20 rounded-full"></div>
                                  </div>

                                  {/* Content */}
                                  <div className="p-3">
                                    <div className="w-3/4 h-3 bg-white/20 rounded-full mb-2"></div>
                                    <div className="w-1/2 h-2 bg-white/10 rounded-full mb-3"></div>
                                    <div className="w-24 h-6 bg-aha-red/30 rounded-md mb-3"></div>
                                  </div>

                                  {/* Draggable element */}
                                  <motion.div
                                    className="absolute w-16 h-12 bg-blue-500/20 border border-blue-500/40 rounded-md flex items-center justify-center"
                                    style={{ top: '40%', left: '60%' }}
                                    drag
                                    dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
                                    dragElastic={0.2}
                                  >
                                    <div className="w-8 h-2 bg-white/30 rounded-full"></div>
                                  </motion.div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeatureShowcase;
