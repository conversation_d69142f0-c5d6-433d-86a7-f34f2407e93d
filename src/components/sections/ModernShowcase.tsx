import React, { useRef, forwardRef } from 'react';
import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { ArrowRight, Check, Monitor, Smartphone, Tablet } from 'lucide-react';
import { AnimatedBeam } from '../ui/animated-beam';
import { cn } from '@/lib/utils';
import CompetitorLogos from '../CompetitorLogos';

// Feature item component
const FeatureItem: React.FC<{ text: string }> = ({ text }) => (
  <li className="flex items-center gap-2">
    <div className="w-5 h-5 rounded-full bg-aha-red/20 flex items-center justify-center">
      <Check size={12} className="text-aha-red" />
    </div>
    <span className="text-white/90 font-medium">{text}</span>
  </li>
);

// Circle component for animated beam
const Circle = forwardRef<
  HTMLDivElement,
  { className?: string; children?: React.ReactNode }
>(({ className, children }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "z-10 flex size-12 items-center justify-center rounded-full border-2 bg-white p-3 shadow-[0_0_20px_-12px_rgba(0,0,0,0.8)]",
        className,
      )}
    >
      {children}
    </div>
  );
});

Circle.displayName = "Circle";

// AHA Logo component
const AHALogo = () => (
  <img
    src="/Logomark Red.svg"
    alt="AHA-Innovations"
    className="w-8 h-8"
  />
);


// Animated Beam Showcase Component
const AnimatedBeamShowcase: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const div1Ref = useRef<HTMLDivElement>(null);
  const div2Ref = useRef<HTMLDivElement>(null);
  const div3Ref = useRef<HTMLDivElement>(null);
  const div4Ref = useRef<HTMLDivElement>(null);
  const div5Ref = useRef<HTMLDivElement>(null);
  const div6Ref = useRef<HTMLDivElement>(null);
  const div7Ref = useRef<HTMLDivElement>(null);

  return (
    <div
      className="relative flex h-[500px] w-full items-center justify-center overflow-hidden rounded-lg border border-white/10 bg-black/30 backdrop-blur-sm p-10"
      ref={containerRef}
    >
      <div className="flex size-full flex-col max-w-lg max-h-[200px] items-stretch justify-between gap-10">
        <div className="flex flex-row items-center justify-between">
          <Circle ref={div1Ref} className="bg-white/10 border-white/20">
            <CompetitorLogos name="HubSpot" />
          </Circle>
          <Circle ref={div5Ref} className="bg-white/10 border-white/20">
            <CompetitorLogos name="Mailchimp" />
          </Circle>
        </div>
        <div className="flex flex-row items-center justify-between">
          <Circle ref={div2Ref} className="bg-white/10 border-white/20">
            <CompetitorLogos name="Zapier" />
          </Circle>
          <Circle ref={div4Ref} className="size-16 bg-aha-red/20 border-aha-red/30 relative z-[100] shadow-2xl shadow-aha-red/20">
            <AHALogo />
          </Circle>
          <Circle ref={div6Ref} className="bg-white/10 border-white/20">
            <CompetitorLogos name="Calendly" />
          </Circle>
        </div>
        <div className="flex flex-row items-center justify-between">
          <Circle ref={div3Ref} className="bg-white/10 border-white/20">
            <CompetitorLogos name="Messenger" />
          </Circle>
          <Circle ref={div7Ref} className="bg-white/10 border-white/20">
            <CompetitorLogos name="Twilio" />
          </Circle>
        </div>
      </div>

      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div1Ref}
        toRef={div4Ref}
        curvature={-75}
        endXOffset={-22}
        endYOffset={-22}
        gradientStartColor="#FF7A59"
        gradientStopColor="#FF4A00"
        className="z-0"
        pathOpacity={0.8}
        pathWidth={2}
      />
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div2Ref}
        toRef={div4Ref}
        endXOffset={-30}
        endYOffset={0}
        gradientStartColor="#FF4A00"
        gradientStopColor="#ef4444"
        className="z-0"
        pathOpacity={0.8}
        pathWidth={2}
      />
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div3Ref}
        toRef={div4Ref}
        curvature={75}
        endXOffset={-22}
        endYOffset={22}
        gradientStartColor="#0084FF"
        gradientStopColor="#ef4444"
        className="z-0"
        pathOpacity={0.8}
        pathWidth={2}
      />
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div5Ref}
        toRef={div4Ref}
        curvature={-75}
        endXOffset={22}
        endYOffset={-22}
        reverse
        gradientStartColor="#FFE01B"
        gradientStopColor="#FF4A00"
        className="z-0"
        pathOpacity={0.8}
        pathWidth={2}
      />
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div6Ref}
        toRef={div4Ref}
        endXOffset={30}
        endYOffset={0}
        reverse
        gradientStartColor="#006BFF"
        gradientStopColor="#FF4A00"
        className="z-0"
        pathOpacity={0.8}
        pathWidth={2}
      />
      <AnimatedBeam
        containerRef={containerRef}
        fromRef={div7Ref}
        toRef={div4Ref}
        curvature={75}
        endXOffset={22}
        endYOffset={22}
        reverse
        gradientStartColor="#F22F46"
        gradientStopColor="#FF4A00"
        className="z-0"
        pathOpacity={0.8}
        pathWidth={2}
      />
    </div>
  );
};

const ModernShowcase: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  // Parallax effects for images
  const imageY = useTransform(scrollYProgress, [0, 1], [0, -50]);
  const imageOpacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.8, 1, 1, 0.8]);
  const imageScale = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.95, 1, 1, 0.95]);
  const bgPatternY = useTransform(scrollYProgress, [0, 1], [0, -100]);

  return (
    <section
      ref={containerRef}
      className="py-12 md:py-24 relative overflow-hidden"
    >
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute top-0 left-0 w-full h-full bg-[url('/lovable-uploads/grid-pattern.svg')] bg-repeat opacity-5"
          style={{ y: bgPatternY }}
        />
        <motion.div
          className="absolute top-1/3 right-0 w-1/2 h-1/2 bg-gradient-to-l from-purple-500/5 via-blue-500/5 to-transparent rounded-full blur-[150px]"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-10 md:mb-20"
        >
          <Badge className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 border-none mb-3 md:mb-6 py-1 md:py-1.5 px-3 md:px-4 mx-auto block w-fit font-medium text-xs md:text-sm">
            Platform Overview
          </Badge>
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-6xl font-extrabold mb-3 md:mb-6 tracking-tight">
            One platform, <span className="text-blue-400">unlimited</span> potential
          </h2>
          <p className="text-center text-gray-400 max-w-3xl mx-auto text-sm md:text-base lg:text-lg">
            AHA-Innovations brings together all the tools you need to grow your business in one seamless platform.
            No more juggling between different software solutions.
          </p>
        </motion.div>

        {/* Device showcase */}
        <motion.div
          className="mb-16 md:mb-24 relative"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-aha-red/5 rounded-xl md:rounded-3xl blur-xl"></div>
          <div className="bg-black/30 backdrop-blur-sm border border-white/10 rounded-xl md:rounded-3xl p-4 sm:p-6 md:p-8 lg:p-12 relative z-10">
            <div className="flex flex-col md:flex-row justify-between items-center mb-8 md:mb-12 gap-4 md:gap-8">
              <div className="text-center md:text-left">
                <h3 className="text-xl sm:text-2xl md:text-3xl font-bold mb-2 md:mb-4">All Your Business Tools, Connected</h3>
                <p className="text-gray-400 max-w-lg text-sm md:text-base">
                  AHA-Innovations seamlessly integrates with your existing tools and workflows,
                  creating a unified ecosystem that powers your business growth.
                </p>
              </div>

              <div className="flex gap-3 md:gap-6 mt-4 md:mt-0">
                {[
                  { label: "CRM", color: "bg-blue-500/20" },
                  { label: "Email", color: "bg-green-500/20" },
                  { label: "Analytics", color: "bg-cyan-500/20" },
                  { label: "Automation", color: "bg-purple-500/20" }
                ].map((integration, index) => (
                  <motion.div
                    key={index}
                    className="flex flex-col items-center gap-1 md:gap-2"
                    initial={{ opacity: 0, y: 10 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <div className={`w-10 h-10 md:w-12 md:h-12 rounded-full ${integration.color} flex items-center justify-center border border-white/10`}>
                      <div className="w-2 h-2 rounded-full bg-white/60"></div>
                    </div>
                    <span className="text-xs md:text-sm text-gray-400">{integration.label}</span>
                  </motion.div>
                ))}
              </div>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <AnimatedBeamShowcase />
            </motion.div>
          </div>
        </motion.div>

        {/* Features grid */}
        <div className="grid md:grid-cols-2 gap-8 md:gap-16 items-center">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="px-1 sm:px-0"
          >
            <Badge className="bg-aha-red/20 hover:bg-aha-red/30 text-aha-red border-none mb-4 md:mb-6 py-1 px-3 font-medium text-xs md:text-sm">
              CRM & Pipeline Management
            </Badge>
            <h3 className="text-xl sm:text-2xl md:text-3xl font-bold mb-3 md:mb-6 break-words hyphens-auto">
              Organize Your Customer Relationships Like Never Before
            </h3>
            <p className="text-gray-400 mb-4 md:mb-8 text-sm md:text-base">
              Our powerful CRM system helps you track every client interaction, organize contacts, and visualize
              your sales pipeline in real-time. With customizable stages and automated follow-ups, you'll never
              miss an opportunity again.
            </p>

            <div className="space-y-5">
              {[
                "Visual pipeline management with drag-and-drop interface",
                "Contact organization with custom fields and tags",
                "Automated follow-up sequences",
                "Deal tracking and forecasting"
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  className="flex gap-2 md:gap-4 p-3 md:p-4 rounded-xl hover:bg-white/5 transition-colors duration-300"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <div className="w-5 h-5 md:w-6 md:h-6 rounded-full bg-aha-red/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Check size={12} className="text-aha-red md:hidden" />
                    <Check size={14} className="text-aha-red hidden md:block" />
                  </div>
                  <div>
                    <p className="text-gray-300 text-sm md:text-base">{feature}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              viewport={{ once: true }}
              className="mt-10"
            >
              <Button
                className="bg-aha-red hover:bg-aha-darkred text-white rounded-full px-4 md:px-6 py-3 md:py-5 text-sm md:text-base group relative overflow-hidden"
                onClick={() => window.location.href = "/features"}
              >
                <span className="relative z-10 flex items-center gap-1 md:gap-2 font-medium">
                  Learn More
                  <ArrowRight className="w-3 h-3 md:w-4 md:h-4 transition-transform duration-300 group-hover:translate-x-1" />
                </span>
                <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
              </Button>
            </motion.div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="relative px-1 sm:px-0"
          >
            <div className="absolute -inset-4 bg-gradient-to-r from-aha-red/10 via-purple-500/10 to-blue-500/10 rounded-2xl blur-xl opacity-70"></div>
            <div className="bg-gradient-to-br from-black/60 to-black/40 backdrop-blur-sm border border-white/10 rounded-2xl p-4 sm:p-6 relative z-10">
              {/* Abstract CRM Dashboard Visualization */}
              <div className="w-full rounded-xl border border-white/10 shadow-lg mb-4 sm:mb-6 bg-gradient-to-br from-gray-900 to-black p-3 md:p-6 overflow-hidden">
                {/* CRM Dashboard Header */}
                <div className="flex justify-between items-center mb-4 md:mb-6">
                  <div className="flex items-center gap-2 md:gap-3">
                    <div className="w-6 h-6 md:w-8 md:h-8 rounded-full bg-aha-red/30"></div>
                    <div className="space-y-1">
                      <div className="w-16 md:w-24 h-2 md:h-3 bg-white/20 rounded-full"></div>
                      <div className="w-12 md:w-16 h-1.5 md:h-2 bg-white/10 rounded-full"></div>
                    </div>
                  </div>
                  <div className="flex gap-1 md:gap-2">
                    <div className="w-6 h-6 md:w-8 md:h-8 rounded-full bg-white/10 flex items-center justify-center">
                      <div className="w-3 h-3 md:w-4 md:h-4 bg-white/20 rounded-sm"></div>
                    </div>
                    <div className="w-6 h-6 md:w-8 md:h-8 rounded-full bg-white/10 flex items-center justify-center">
                      <div className="w-3 h-3 md:w-4 md:h-4 bg-white/20 rounded-sm"></div>
                    </div>
                  </div>
                </div>

                {/* Pipeline visualization */}
                <div className="flex gap-2 md:gap-3 mb-4 md:mb-6 overflow-x-auto pb-2 -mx-1 px-1 scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-transparent">
                  <motion.div
                    className="min-w-[70px] sm:min-w-[80px] md:min-w-[120px] bg-gradient-to-b from-aha-red/20 to-aha-red/5 rounded-lg p-2 md:p-3 flex-shrink-0"
                    animate={{ y: [0, -3, 0] }}
                    transition={{ duration: 3, repeat: Infinity, repeatType: "reverse" }}
                  >
                    <div className="text-[10px] md:text-xs text-white/70 mb-1 md:mb-2 text-center">New Leads</div>
                    <motion.div
                      className="w-full h-8 sm:h-10 md:h-16 bg-white/5 rounded-md mb-1 md:mb-2 flex items-center justify-center"
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(255, 255, 255, 0.1)" }}
                    >
                      <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-8 md:h-8 rounded-full bg-aha-red/20"></div>
                    </motion.div>
                    <motion.div
                      className="w-full h-8 sm:h-10 md:h-16 bg-white/5 rounded-md flex items-center justify-center"
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(255, 255, 255, 0.1)" }}
                    >
                      <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-8 md:h-8 rounded-full bg-aha-red/20"></div>
                    </motion.div>
                  </motion.div>

                  <motion.div
                    className="min-w-[70px] sm:min-w-[80px] md:min-w-[120px] bg-gradient-to-b from-blue-500/20 to-blue-500/5 rounded-lg p-2 md:p-3 flex-shrink-0"
                    animate={{ y: [0, -3, 0] }}
                    transition={{ duration: 3.5, repeat: Infinity, repeatType: "reverse", delay: 0.5 }}
                  >
                    <div className="text-[10px] md:text-xs text-white/70 mb-1 md:mb-2 text-center">Contacted</div>
                    <motion.div
                      className="w-full h-8 sm:h-10 md:h-16 bg-white/5 rounded-md mb-1 md:mb-2 flex items-center justify-center"
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(255, 255, 255, 0.1)" }}
                    >
                      <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-8 md:h-8 rounded-full bg-blue-500/20"></div>
                    </motion.div>
                    <motion.div
                      className="w-full h-8 sm:h-10 md:h-16 bg-white/5 rounded-md mb-1 md:mb-2 flex items-center justify-center"
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(255, 255, 255, 0.1)" }}
                    >
                      <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-8 md:h-8 rounded-full bg-blue-500/20"></div>
                    </motion.div>
                  </motion.div>

                  <motion.div
                    className="min-w-[70px] sm:min-w-[80px] md:min-w-[120px] bg-gradient-to-b from-purple-500/20 to-purple-500/5 rounded-lg p-2 md:p-3 flex-shrink-0"
                    animate={{ y: [0, -3, 0] }}
                    transition={{ duration: 2.8, repeat: Infinity, repeatType: "reverse", delay: 1 }}
                  >
                    <div className="text-[10px] md:text-xs text-white/70 mb-1 md:mb-2 text-center">Qualified</div>
                    <motion.div
                      className="w-full h-8 sm:h-10 md:h-16 bg-white/5 rounded-md mb-1 md:mb-2 flex items-center justify-center"
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(255, 255, 255, 0.1)" }}
                    >
                      <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-8 md:h-8 rounded-full bg-purple-500/20"></div>
                    </motion.div>
                  </motion.div>

                  <motion.div
                    className="min-w-[70px] sm:min-w-[80px] md:min-w-[120px] bg-gradient-to-b from-green-500/20 to-green-500/5 rounded-lg p-2 md:p-3 flex-shrink-0"
                    animate={{ y: [0, -3, 0] }}
                    transition={{ duration: 3.2, repeat: Infinity, repeatType: "reverse", delay: 1.5 }}
                  >
                    <div className="text-[10px] md:text-xs text-white/70 mb-1 md:mb-2 text-center">Closed Won</div>
                    <motion.div
                      className="w-full h-8 sm:h-10 md:h-16 bg-white/5 rounded-md mb-1 md:mb-2 flex items-center justify-center"
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(255, 255, 255, 0.1)" }}
                    >
                      <div className="w-4 h-4 sm:w-5 sm:h-5 md:w-8 md:h-8 rounded-full bg-green-500/20"></div>
                    </motion.div>
                  </motion.div>
                </div>

                {/* Animated data flow */}
                <motion.div
                  className="absolute top-1/4 left-1/4 w-1.5 md:w-2 h-1.5 md:h-2 rounded-full bg-aha-red"
                  animate={{
                    x: [0, 50, 100, 150],
                    y: [0, 25, 50, 25],
                    opacity: [1, 0.8, 0.6, 0]
                  }}
                  transition={{ duration: 4, repeat: Infinity, repeatType: "loop" }}
                />

                <motion.div
                  className="absolute top-1/3 left-1/3 w-1.5 md:w-2 h-1.5 md:h-2 rounded-full bg-blue-500"
                  animate={{
                    x: [0, 50, 100],
                    y: [0, -25, 0],
                    opacity: [1, 0.8, 0]
                  }}
                  transition={{ duration: 3, repeat: Infinity, repeatType: "loop", delay: 1 }}
                />
              </div>

              <div className="grid grid-cols-2 gap-3 sm:gap-4 mt-4 sm:mt-6">
                {[
                  {
                    title: "Contacts",
                    value: "10,000+",
                    subtitle: "Managed efficiently",
                    color: "aha-red"
                  },
                  {
                    title: "Deals",
                    value: "32%",
                    subtitle: "Conversion increase",
                    color: "blue"
                  }
                ].map((stat, index) => {
                  const bgColor = stat.color === "blue" ? "bg-blue-500/10" : "bg-aha-red/10";
                  const textColor = stat.color === "blue" ? "text-blue-400" : "text-aha-red";

                  return (
                    <motion.div
                      key={index}
                      className={`${bgColor} rounded-xl p-2 sm:p-3 md:p-5 flex flex-col`}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      whileHover={{ y: -5 }}
                    >
                      <span className="text-[10px] sm:text-xs md:text-sm text-gray-400 mb-0.5 md:mb-1">{stat.title}</span>
                      <span className={`text-lg sm:text-xl md:text-2xl font-bold ${textColor} mb-0.5 md:mb-1`}>{stat.value}</span>
                      <span className="text-[10px] sm:text-xs text-gray-500">{stat.subtitle}</span>
                    </motion.div>
                  );
                })}
              </div>
            </div>
          </motion.div>
        </div>

        {/* Second showcase item - reversed */}
        <div className="grid md:grid-cols-2 gap-8 md:gap-16 items-center mt-16 md:mt-32">
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="relative order-2 md:order-1"
          >
            <div className="absolute -inset-4 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-aha-red/10 rounded-2xl blur-xl opacity-70"></div>
            <div className="bg-gradient-to-br from-black/60 to-black/40 backdrop-blur-sm border border-white/10 rounded-2xl p-6 relative z-10">
              {/* Abstract Sales Funnel Builder Visualization */}
              <div className="w-full rounded-xl border border-white/10 shadow-lg mb-6 bg-gradient-to-br from-gray-900 to-black p-3 md:p-6 overflow-hidden">
                {/* Funnel Builder Header */}
                <div className="flex justify-between items-center mb-4 md:mb-6">
                  <div className="flex items-center gap-2 md:gap-3">
                    <div className="w-6 h-6 md:w-8 md:h-8 rounded-full bg-blue-500/30"></div>
                    <div className="space-y-1">
                      <div className="w-20 md:w-32 h-2 md:h-3 bg-white/20 rounded-full"></div>
                      <div className="w-14 md:w-20 h-1.5 md:h-2 bg-white/10 rounded-full"></div>
                    </div>
                  </div>
                  <div className="flex gap-1 md:gap-2">
                    <motion.div
                      className="px-2 md:px-3 py-0.5 md:py-1 bg-blue-500/30 rounded-md text-[10px] md:text-xs text-white"
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(59, 130, 246, 0.5)" }}
                    >
                      Preview
                    </motion.div>
                    <motion.div
                      className="px-2 md:px-3 py-0.5 md:py-1 bg-aha-red/30 rounded-md text-[10px] md:text-xs text-white"
                      whileHover={{ scale: 1.05, backgroundColor: "rgba(234, 56, 76, 0.5)" }}
                    >
                      Publish
                    </motion.div>
                  </div>
                </div>

                {/* Funnel visualization */}
                <div className="flex flex-col items-center gap-1 md:gap-2 mb-4 md:mb-6">
                  {/* Funnel steps */}
                  <motion.div
                    className="w-full max-w-[200px] md:max-w-[300px] h-10 md:h-16 bg-gradient-to-r from-blue-500/20 to-blue-500/5 rounded-lg flex items-center justify-center relative"
                    whileHover={{ scale: 1.02, backgroundColor: "rgba(59, 130, 246, 0.2)" }}
                  >
                    <div className="text-xs md:text-sm text-white/70">Landing Page</div>
                    <motion.div
                      className="absolute -bottom-4 md:-bottom-6 left-1/2 transform -translate-x-1/2 w-0.5 h-4 md:h-6 bg-white/20"
                      animate={{ height: [3, 4, 3] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  </motion.div>

                  <motion.div
                    className="w-[90%] max-w-[180px] md:max-w-[270px] h-10 md:h-16 bg-gradient-to-r from-purple-500/20 to-purple-500/5 rounded-lg flex items-center justify-center relative"
                    whileHover={{ scale: 1.02, backgroundColor: "rgba(139, 92, 246, 0.2)" }}
                  >
                    <div className="text-xs md:text-sm text-white/70">Opt-in Form</div>
                    <motion.div
                      className="absolute -bottom-4 md:-bottom-6 left-1/2 transform -translate-x-1/2 w-0.5 h-4 md:h-6 bg-white/20"
                      animate={{ height: [3, 4, 3] }}
                      transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
                    />
                  </motion.div>

                  <motion.div
                    className="w-[80%] max-w-[160px] md:max-w-[240px] h-10 md:h-16 bg-gradient-to-r from-aha-red/20 to-aha-red/5 rounded-lg flex items-center justify-center relative"
                    whileHover={{ scale: 1.02, backgroundColor: "rgba(234, 56, 76, 0.2)" }}
                  >
                    <div className="text-xs md:text-sm text-white/70">Sales Page</div>
                    <motion.div
                      className="absolute -bottom-4 md:-bottom-6 left-1/2 transform -translate-x-1/2 w-0.5 h-4 md:h-6 bg-white/20"
                      animate={{ height: [3, 4, 3] }}
                      transition={{ duration: 2, repeat: Infinity, delay: 1 }}
                    />
                  </motion.div>

                  <motion.div
                    className="w-[70%] max-w-[140px] md:max-w-[210px] h-10 md:h-16 bg-gradient-to-r from-green-500/20 to-green-500/5 rounded-lg flex items-center justify-center"
                    whileHover={{ scale: 1.02, backgroundColor: "rgba(34, 197, 94, 0.2)" }}
                  >
                    <div className="text-xs md:text-sm text-white/70">Thank You Page</div>
                  </motion.div>
                </div>

                {/* Animated user flow */}
                <motion.div
                  className="absolute top-[35%] left-[45%] w-2 md:w-3 h-2 md:h-3 rounded-full bg-blue-500/70"
                  animate={{
                    y: [0, 25, 50, 75],
                    opacity: [1, 0.9, 0.8, 0.7, 0]
                  }}
                  transition={{ duration: 5, repeat: Infinity, repeatType: "loop" }}
                />

                <motion.div
                  className="absolute top-[35%] left-[55%] w-2 md:w-3 h-2 md:h-3 rounded-full bg-purple-500/70"
                  animate={{
                    y: [0, 25, 50, 75],
                    opacity: [1, 0.9, 0.8, 0.7, 0]
                  }}
                  transition={{ duration: 5, repeat: Infinity, repeatType: "loop", delay: 1.5 }}
                />
              </div>

              <div className="grid grid-cols-2 gap-4 mt-6">
                {[
                  {
                    title: "Conversion",
                    value: "+45%",
                    subtitle: "Average increase",
                    color: "blue"
                  },
                  {
                    title: "Setup Time",
                    value: "5 min",
                    subtitle: "With templates",
                    color: "aha-red"
                  }
                ].map((stat, index) => {
                  const bgColor = stat.color === "blue" ? "bg-blue-500/10" : "bg-aha-red/10";
                  const textColor = stat.color === "blue" ? "text-blue-400" : "text-aha-red";

                  return (
                    <motion.div
                      key={index}
                      className={`${bgColor} rounded-xl p-3 md:p-5 flex flex-col`}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      whileHover={{ y: -5 }}
                    >
                      <span className="text-xs md:text-sm text-gray-400 mb-0.5 md:mb-1">{stat.title}</span>
                      <span className={`text-xl md:text-2xl font-bold ${textColor} mb-0.5 md:mb-1`}>{stat.value}</span>
                      <span className="text-xs text-gray-500">{stat.subtitle}</span>
                    </motion.div>
                  );
                })}
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="order-1 md:order-2"
          >
            <Badge className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 border-none mb-4 md:mb-6 py-1 px-3 font-medium text-xs md:text-sm">
              Sales Funnels
            </Badge>
            <h3 className="text-xl sm:text-2xl md:text-3xl font-bold mb-3 md:mb-6">
              Convert More Prospects with Unlimited Sales Funnels
            </h3>
            <p className="text-gray-400 mb-4 md:mb-8 text-sm md:text-base">
              Design high-converting sales funnels with our intuitive drag-and-drop builder.
              Create landing pages, order forms, upsells, and thank you pages that guide customers
              through their buying journey.
            </p>

            <div className="space-y-5">
              {[
                "Unlimited sales funnels with all plans",
                "Drag-and-drop page builder",
                "Pre-made templates for quick setup",
                "A/B testing to optimize conversion rates"
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  className="flex gap-2 md:gap-4 p-3 md:p-4 rounded-xl hover:bg-white/5 transition-colors duration-300"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <div className="w-5 h-5 md:w-6 md:h-6 rounded-full bg-blue-500/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                    <Check size={12} className="text-blue-400 md:hidden" />
                    <Check size={14} className="text-blue-400 hidden md:block" />
                  </div>
                  <div>
                    <p className="text-gray-300 text-sm md:text-base">{feature}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              viewport={{ once: true }}
              className="mt-10"
            >
              <Button
                className="bg-blue-500 hover:bg-blue-600 text-white rounded-full px-4 md:px-6 py-3 md:py-5 text-sm md:text-base group relative overflow-hidden"
                onClick={() => window.location.href = "/features"}
              >
                <span className="relative z-10 flex items-center gap-1 md:gap-2 font-medium">
                  Learn More
                  <ArrowRight className="w-3 h-3 md:w-4 md:h-4 transition-transform duration-300 group-hover:translate-x-1" />
                </span>
                <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ModernShowcase;
