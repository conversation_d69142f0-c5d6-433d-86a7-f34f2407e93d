import React from 'react';
import { motion } from 'framer-motion';
import { Badge } from '../ui/badge';
import { TestimonialsColumn } from '../ui/testimonials-columns-1';
import { AvatarCircles } from '../ui/avatar-circles';



// Testimonial interface for TestimonialsColumn
interface ColumnTestimonial {
  text: string;
  image: string;
  name: string;
  role: string;
}



const EnhancedTestimonials: React.FC = () => {

  // Customer avatars from testimonials for social proof
  const testimonialAvatars = [
    "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
    "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
    "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
  ];

  // Testimonials data for TestimonialsColumn component
  const columnTestimonials: ColumnTestimonial[] = [
    {
      text: "Honestly, I was skeptical at first, but AHA-Innovations completely transformed how we handle our client pipeline. The automation saves us about 15 hours a week, and our follow-up rate went from maybe 30% to over 85%. Worth every penny.",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      name: "Sarah Johnson",
      role: "Marketing Director"
    },
    {
      text: "We were drowning in spreadsheets and missed opportunities. Three months with AHA-Innovations and we've closed more deals than the entire previous quarter. The team actually gets excited about using the system now.",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      name: "Marcus Chen",
      role: "Sales Manager"
    },
    {
      text: "I'll be real - the first week was rough learning everything. But their support team was incredible, and now I can't imagine running our business without it. Client retention is up 40% since we started.",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      name: "David Rodriguez",
      role: "Business Owner"
    },
    {
      text: "My assistant quit last month, and I thought I was screwed. Turns out AHA-Innovations handles most of what she used to do, plus stuff we never even thought of. I'm actually sleeping better at night knowing nothing's falling through the cracks.",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      name: "Emily Rodriguez",
      role: "Agency Owner"
    },
    {
      text: "Look, I'm not tech-savvy at all. But their onboarding was so smooth, even I figured it out. Now my team actually knows what everyone else is working on. Game changer for our chaos.",
      image: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      name: "Lisa Wang",
      role: "Restaurant Owner"
    },
    {
      text: "We were losing so many leads it wasn't even funny. Six months later, we're booking 3x more consultations and actually following up with everyone. My biggest regret is not finding them sooner.",
      image: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      name: "Jennifer Martinez",
      role: "Fitness Coach"
    },
    {
      text: "I used to spend weekends catching up on admin work. Now I actually have time for my family again. The automated reports show me exactly what's working and what isn't - no more guessing games.",
      image: "https://images.unsplash.com/photo-1560250097-0b93528c311a?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      name: "Robert Kim",
      role: "Consultant"
    },
    {
      text: "Tried 4 different systems before this one. Finally found something that doesn't make me want to throw my laptop out the window. My team actually uses it without me having to nag them.",
      image: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      name: "Amanda Foster",
      role: "Real Estate Broker"
    },
    {
      text: "Best investment I've made in years. My online store went from barely breaking even to consistently hitting 5-figure months. The automated follow-ups alone are worth the price.",
      image: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
      name: "James Wilson",
      role: "E-commerce Owner"
    }
  ];

  // Split testimonials into columns
  const firstColumn = columnTestimonials.slice(0, 3);
  const secondColumn = columnTestimonials.slice(3, 6);
  const thirdColumn = columnTestimonials.slice(6, 9);

  return (
    <section className="py-24 relative overflow-hidden">
      {/* Subtle background elements that blend with hero */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Subtle glass orbs for depth */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-aha-red/4 via-aha-red/2 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-tr from-aha-red/3 via-purple-500/2 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute top-1/3 right-1/3 w-64 h-64 bg-gradient-to-bl from-blue-500/2 via-aha-red/2 to-transparent rounded-full blur-2xl"></div>
        <div className="absolute bottom-1/3 left-1/3 w-80 h-80 bg-gradient-to-tr from-aha-red/3 via-transparent to-purple-500/2 rounded-full blur-3xl"></div>

        {/* Subtle grid pattern overlay for texture */}
        <div className="absolute inset-0 opacity-20" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      <div className="relative z-10">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl md:text-6xl font-bold text-center mb-8 md:mb-12 font-gotham">
            <span className="text-white">See what </span>
            <span className="text-aha-red">our clients</span>
            <span className="text-white"> are saying</span>
          </h2>



          {/* TestimonialsColumn Component */}
          <div className="flex justify-center gap-6 mt-10 mb-16 [mask-image:linear-gradient(to_bottom,transparent,black_25%,black_75%,transparent)] max-h-[740px] overflow-hidden">
            <TestimonialsColumn testimonials={firstColumn} duration={15} />
            <TestimonialsColumn testimonials={secondColumn} className="hidden md:block" duration={19} />
            <TestimonialsColumn testimonials={thirdColumn} className="hidden lg:block" duration={17} />
          </div>


        </div>
      </div>


    </section>
  );
};

export default EnhancedTestimonials;
