import React from 'react';
import { motion } from 'framer-motion';
import { Badge } from '../ui/badge';
import { TextEffect } from '../ui/text-effect';
import {
  Layers,
  TrendingDown,
  Clock,
  Search,
  Code,
  Lightbulb
} from 'lucide-react';

interface PainPointProps {
  icon: React.ReactNode;
  title: string;
  delay?: number;
}

const PainPoint: React.FC<PainPointProps> = ({ icon, title, delay = 0 }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 + (delay * 0.1) }}
      viewport={{ once: true }}
      className="flex items-center gap-4 p-4 rounded-xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 hover:border-aha-red/20 transition-all duration-300 group"
    >
      <div className="w-12 h-12 rounded-full bg-aha-red/20 flex items-center justify-center text-white group-hover:bg-aha-red/30 transition-all duration-300">
        {icon}
      </div>
      <div>
        <h3 className="font-semibold text-white font-gotham">{title}</h3>
      </div>
    </motion.div>
  );
};

const PainPoints: React.FC = () => {
  const painPoints = [
    {
      icon: <Layers size={24} />,
      title: "Juggling 10+ different tools",
    },
    {
      icon: <Clock size={24} />,
      title: "Wasting hours on manual tasks",
    },
    {
      icon: <TrendingDown size={24} />,
      title: "Missing leads and opportunities",
    },
    {
      icon: <Code size={24} />,
      title: "Complex tech setup headaches",
    },
  ];

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Subtle background elements that blend with hero */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.06),transparent_50%)]"></div>
        <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.04),transparent_50%)]"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="bg-aha-red/20 hover:bg-aha-red/30 text-aha-red border-none mb-6 py-1.5 px-4 mx-auto block w-fit font-medium">
            Pain Points
          </Badge>
          <TextEffect
            per="word"
            as="h2"
            className="text-4xl md:text-5xl font-bold mb-6 tracking-tight font-gotham"
            variants={{
              container: {
                hidden: { opacity: 0 },
                visible: {
                  opacity: 1,
                  transition: { staggerChildren: 0.08, delayChildren: 0.2 },
                },
                exit: {
                  transition: { staggerChildren: 0.05, staggerDirection: -1 },
                },
              },
              item: {
                hidden: {
                  opacity: 0,
                  y: 30,
                  rotateX: -90,
                },
                visible: {
                  opacity: 1,
                  y: 0,
                  rotateX: 0,
                  transition: {
                    type: "spring",
                    damping: 12,
                    stiffness: 200,
                  },
                },
                exit: {
                  opacity: 0,
                  y: -20,
                  rotateX: 90,
                  transition: {
                    duration: 0.3,
                  },
                },
              },
            }}
          >
            Is this you right now?
          </TextEffect>
          <p className="text-center text-white/90 max-w-4xl mx-auto text-lg md:text-xl lg:text-xl font-medium leading-relaxed tracking-wide font-gotham">
            You're not alone. These are the top challenges small business owners face every day.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {painPoints.map((point, index) => (
            <PainPoint
              key={index}
              icon={point.icon}
              title={point.title}
              delay={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default PainPoints;
