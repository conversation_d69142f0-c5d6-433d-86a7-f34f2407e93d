import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { OnboardingPricingCard, OnboardingPricingTier } from '@/components/ui/onboarding-pricing-card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Check } from 'lucide-react';

// Define the pricing tiers for onboarding
const ONBOARDING_TIERS: OnboardingPricingTier[] = [
  {
    id: "free",
    name: "Free Plan",
    price: {
      monthly: "$0",
      yearly: "$0",
    },
    description: "Perfect for getting started",
    features: [
      "3 Pipelines",
      "100 Contacts", 
      "100 Email Marketing/month",
      "1 Sales Funnel",
      "Email Support",
      "Basic Analytics",
    ],
    cta: "Continue Free",
  },
  {
    id: "basic",
    name: "Basic Plan",
    price: {
      monthly: 5,
      yearly: 4.58,
    },
    description: "Perfect for freelancers",
    features: [
      "15 Pipelines",
      "1,000 Contacts",
      "1,000 Email Marketing/month",
      "Unlimited Sales Funnels",
      "Basic Automation",
      "Priority Email Support",
    ],
    cta: "Start Basic Plan",
    stripeLinks: {
      monthly: "https://buy.stripe.com/bJe5kE7Za5Ea9XZ37U4801F",
      yearly: "https://buy.stripe.com/3cI14o7Za0jQgmndMy4801G",
    },
  },
  {
    id: "agency",
    name: "Agency Plan",
    price: {
      monthly: 25,
      yearly: 20.83,
    },
    description: "Ideal for small agencies",
    popular: true,
    features: [
      "50 Pipelines",
      "10,000 Contacts",
      "7,500 Email Marketing/month",
      "Unlimited Sales Funnels",
      "Workflow Triggers & Actions",
      "Advanced Analytics",
      "Priority Support + Chat",
    ],
    cta: "Start Agency Plan",
    stripeLinks: {
      monthly: "https://buy.stripe.com/example-agency-monthly",
      yearly: "https://buy.stripe.com/example-agency-yearly",
    },
  },
  {
    id: "enterprise",
    name: "Enterprise Plan",
    price: {
      monthly: 79,
      yearly: 65.83,
    },
    originalPrice: {
      monthly: 99,
      yearly: 79.17,
    },
    limitedOffer: "Limited offer, get Enterprise for the price of Basic!",
    description: "Recommended for business",
    features: [
      "Unlimited Pipelines",
      "Unlimited Contacts",
      "15,000 Email Marketing/month",
      "Unlimited Sales Funnels",
      "Advanced Workflow Automation",
      "Reputation Management",
      "Custom Integrations",
      "Dedicated Support",
    ],
    cta: "Start Enterprise Plan",
    stripeLinks: {
      monthly: "https://buy.stripe.com/example-enterprise-monthly",
      yearly: "https://buy.stripe.com/example-enterprise-yearly",
    },
  },
];

interface PlanSelectionStepProps {
  onSelectPlan: (planId: string, planName: string, isUpgrade: boolean) => void;
  onBack?: () => void;
  selectedPlan?: string;
}

export function PlanSelectionStep({ onSelectPlan, onBack, selectedPlan }: PlanSelectionStepProps) {
  const [paymentFrequency, setPaymentFrequency] = useState<'monthly' | 'yearly'>('yearly');
  const [showCalendar, setShowCalendar] = useState(false);

  const handlePlanSelection = (planId: string, planName: string, isUpgrade: boolean) => {
    console.log('Plan selected:', { planId, planName, isUpgrade });
    onSelectPlan(planId, planName, isUpgrade);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.h2 
          className="text-3xl font-bold text-white font-gotham"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          Choose Your Plan
        </motion.h2>
        <motion.p 
          className="text-gray-400 max-w-2xl mx-auto font-gotham"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          Start with our free plan and upgrade anytime. All paid plans include a 14-day free trial.
        </motion.p>
      </div>

      {/* Billing Toggle */}
      <motion.div 
        className="flex justify-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className="flex rounded-full bg-black/20 backdrop-blur-sm border border-white/10 p-1">
          <button
            onClick={() => setPaymentFrequency('monthly')}
            className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
              paymentFrequency === 'monthly'
                ? 'bg-aha-red text-white shadow-lg'
                : 'text-white/70 hover:text-white'
            }`}
          >
            Monthly
          </button>
          <button
            onClick={() => setPaymentFrequency('yearly')}
            className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-2 ${
              paymentFrequency === 'yearly'
                ? 'bg-aha-red text-white shadow-lg'
                : 'text-white/70 hover:text-white'
            }`}
          >
            Yearly
            <span className="text-xs bg-yellow-500/20 text-yellow-300 px-2 py-0.5 rounded-full font-bold">
              Save 20%
            </span>
          </button>
        </div>
      </motion.div>

      {/* Pricing Cards */}
      <motion.div 
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        {ONBOARDING_TIERS.map((tier, index) => (
          <motion.div
            key={tier.id}
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}
          >
            <OnboardingPricingCard
              tier={tier}
              paymentFrequency={paymentFrequency}
              onSelectPlan={handlePlanSelection}
              onContactUs={() => setShowCalendar(true)}
            />
          </motion.div>
        ))}
      </motion.div>

      {/* Selected Plan Indicator */}
      {selectedPlan && (
        <motion.div 
          className="text-center"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <div className="inline-flex items-center gap-2 bg-green-500/20 border border-green-500/30 rounded-full px-4 py-2">
            <Check className="h-4 w-4 text-green-400" />
            <span className="text-green-400 font-medium">
              {ONBOARDING_TIERS.find(t => t.id === selectedPlan)?.name} Selected
            </span>
          </div>
        </motion.div>
      )}

      {/* Back Button */}
      {onBack && (
        <div className="flex justify-center pt-4">
          <Button
            onClick={onBack}
            variant="outline"
            className="border-white/20 text-white/70 hover:bg-white/10 hover:text-white"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Profile Setup
          </Button>
        </div>
      )}

      {/* Additional Info */}
      <motion.div 
        className="max-w-4xl mx-auto"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.8 }}
      >
        <div className="glass-card rounded-xl p-6 border border-white/10 bg-gradient-to-r from-blue-500/5 to-purple-500/5">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 w-5 h-5 rounded-full bg-blue-500/20 flex items-center justify-center mt-0.5">
              <span className="text-blue-400 text-xs">ℹ</span>
            </div>
            <div className="flex-1">
              <p className="text-white/90 text-sm font-gotham leading-relaxed">
                <span className="font-medium">Free Plan Benefits:</span> Start immediately with no credit card required. 
                You can upgrade anytime as your business grows. All paid plans include a 14-day free trial period.
              </p>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
