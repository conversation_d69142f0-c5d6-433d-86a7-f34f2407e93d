
import React from 'react';
import { useNavigate } from 'react-router-dom';
import Logo from './Logo';
import { ArrowLeft } from 'lucide-react';
import { motion } from 'framer-motion';

interface AuthLayoutProps {
  children: React.ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-aha-dark text-white overflow-x-hidden relative">
      {/* Background elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
        <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-5"></div>
      </div>

      {/* Back Button */}
      <motion.button
        className="fixed top-4 left-4 sm:top-6 sm:left-6 z-50 flex items-center gap-2 text-white bg-black/20 hover:bg-black/30 px-3 py-2 sm:px-4 sm:py-2 rounded-full backdrop-blur-sm transition-all duration-300 border border-white/10"
        onClick={() => navigate('/')}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
      >
        <ArrowLeft size={16} />
        <span className="hidden sm:inline">Back to Home</span>
        <span className="sm:hidden">Back</span>
      </motion.button>

      <div className="flex min-h-screen">
        {/* Left side - Branding */}
        <motion.div
          className="hidden lg:flex lg:w-1/2 flex-col justify-center items-center p-8 relative"
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="max-w-md">
            <div className="flex justify-center mb-6">
              <Logo className="h-12 w-auto" />
            </div>
            <div className="text-center">
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
                You're just one click away from <span className="text-aha-red">Growth</span>
              </h1>
              <p className="text-gray-300 max-w-sm mx-auto text-lg">
                Start building with AHA-Innovations! Just the tools you need to succeed
              </p>
            </div>
          </div>
        </motion.div>

        {/* Right side - Form */}
        <motion.div
          className="w-full lg:w-1/2 flex flex-col justify-center items-center p-4 sm:p-6 lg:p-8 min-h-screen lg:min-h-0"
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {/* Mobile logo (only visible on small screens) */}
          <div className="lg:hidden w-full max-w-md mb-6 sm:mb-8 mt-12 sm:mt-16">
            <div className="flex justify-center mb-4 sm:mb-6">
              <Logo className="h-8 sm:h-10 w-auto" />
            </div>
          </div>

          <div className="w-full max-w-md">
            {children}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default AuthLayout;
