import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SiMailchimp,
  Si<PERSON>apier,
  SiWix,
  Si<PERSON><PERSON>lio,
  <PERSON><PERSON>lack,
  <PERSON><PERSON><PERSON>gle,
  SiYelp,
  Si<PERSON><PERSON>ndly,
  SiSalesforce,
  SiMessenger
} from 'react-icons/si';

interface CompetitorLogoProps {
  name: string;
}

// Real brand logos using react-icons
const CompetitorLogos: React.FC<CompetitorLogoProps> = ({ name }) => {
  // Map of competitor names to their brand colors and icons
  const brandData: Record<string, { color: string; icon: React.ComponentType<any> }> = {
    'HubSpot': { color: '#FF7A59', icon: SiHubspot },
    'Mailchimp': { color: '#FFE01B', icon: SiMailchimp },
    'Zapier': { color: '#FF4A00', icon: SiZapier },
    'Wix': { color: '#0C6EFC', icon: SiWix },
    'Twilio': { color: '#F22F46', icon: <PERSON><PERSON><PERSON><PERSON> },
    'Slack': { color: '#4A154B', icon: <PERSON><PERSON><PERSON><PERSON> },
    'Google Analytics': { color: '#E37400', icon: SiGoogle },
    'Yelp': { color: '#D32323', icon: SiYel<PERSON> },
    'Calendly': { color: '#006BFF', icon: SiCalendly },
    'Salesforce': { color: '#00A1E0', icon: SiSalesforce },
    'Messenger': { color: '#0084FF', icon: SiMessenger },
    // Competitors without react-icons will fall back to initials
    'ClickFunnels': { color: '#0066FF', icon: null },
    'JotForm': { color: '#FF6100', icon: null },
    'Kajabi': { color: '#2B5CE6', icon: null },
  };

  // Get brand data or use defaults
  const brand = brandData[name];
  const bgColor = brand?.color || '#6B7280';
  const IconComponent = brand?.icon;

  // Determine text color based on background brightness
  const isLightBackground = ['#FFE01B'].includes(bgColor);
  const textColor = isLightBackground ? 'text-gray-900' : 'text-white';

  // Return the appropriate logo based on the competitor name
  if (IconComponent) {
    return (
      <IconComponent
        className="w-6 h-6"
        style={{ color: bgColor }}
      />
    );
  }

  // For competitors without brand icons, use the first two letters
  return (
    <span
      className="font-bold text-sm"
      style={{ color: bgColor }}
    >
      {name.substring(0, 2).toUpperCase()}
    </span>
  );
};

export default CompetitorLogos;
