'use client';
import React, { useState } from 'react';
import { Github, Star } from 'lucide-react';
import { Liquid } from '@/components/ui/button-1';

// AHA Brand Colors for Liquid Button
const AHA_COLORS = {
  color1: '#FFFFFF',        // White base
  color2: '#ea384c',        // AHA Red
  color3: '#c4162a',        // AHA Dark Red
  color4: '#FCFCFE',        // Light white
  color5: '#F9F9FD',        // Off white
  color6: '#ea384c',        // AHA Red
  color7: '#c4162a',        // AHA Dark Red
  color8: '#1A1F2C',        // AHA Dark
  color9: '#ea384c',        // AHA Red
  color10: '#c4162a',       // AHA Dark Red
  color11: '#1a1525',       // AHA Dark Purple
  color12: '#ea384c',       // AHA Red
  color13: '#c4162a',       // AHA Dark Red
  color14: '#ea384c',       // AHA Red
  color15: '#c4162a',       // AHA Dark Red
  color16: '#1A1F2C',       // AHA Dark
  color17: '#1a1525',       // AHA Dark Purple
};

const LiquidButtonDemo = () => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div className="flex justify-center">
      <a
        href="https://github.com/ui-layouts/uilayouts"
        target="_blank"
        className="relative inline-block sm:w-36 w-14 h-[2.7em] mx-auto group dark:bg-black bg-white dark:border-white border-black border-2 rounded-full">
        <div className="absolute w-[112.81%] h-[128.57%] top-[8.57%] left-1/2 -translate-x-1/2 filter blur-[19px] opacity-70">
          <span className="absolute inset-0 rounded-full bg-[#d9d9d9] filter blur-[6.5px]"></span>
          <div className="relative w-full h-full overflow-hidden rounded-full">
            <Liquid isHovered={isHovered} colors={AHA_COLORS} />
          </div>
        </div>
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-[40%] w-[92.23%] h-[112.85%] rounded-full bg-[#010128] filter blur-[7.3px]"></div>
        <div className="relative w-full h-full overflow-hidden rounded-full">
          <span className="absolute inset-0 rounded-full bg-[#d9d9d9]"></span>
          <span className="absolute inset-0 rounded-full bg-black"></span>
          <Liquid isHovered={isHovered} colors={AHA_COLORS} />
          {[1, 2, 3, 4, 5].map((i) => (
            <span
              key={i}
              className={`absolute inset-0 rounded-full border-solid border-[3px] border-gradient-to-b from-transparent to-white mix-blend-overlay filter ${i <= 2 ? 'blur-[3px]' : i === 3 ? 'blur-[5px]' : 'blur-[4px]'}`}></span>
          ))}
          <span className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-[40%] w-[70.8%] h-[42.85%] rounded-full filter blur-[15px] bg-[#006]"></span>
        </div>
        <button
          className="absolute inset-0 rounded-full bg-transparent cursor-pointer"
          aria-label="Get Started"
          type="button"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}>
          <span className="flex items-center justify-between px-4 gap-2 rounded-full group-hover:text-yellow-400 text-white text-xl font-semibold tracking-wide whitespace-nowrap">
            <Star className="group-hover:fill-yellow-400 fill-white w-6 h-6 flex-shrink-0 sm:inline-block hidden" />
            <Github className="sm:hidden inline-block group-hover:fill-yellow-400 fill-white w-6 h-6 flex-shrink-0" />
            <span className="sm:inline-block hidden">Github</span>
          </span>
        </button>
      </a>
    </div>
  );
};

export default LiquidButtonDemo;
