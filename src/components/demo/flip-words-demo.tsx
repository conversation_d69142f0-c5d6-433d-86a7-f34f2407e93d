import React from "react";
import { FlipWords } from "@/components/ui/flip-words";

export function FlipWordsDemo() {
  const words = ["Websites", "Funnels", "Workflows", "Meetings"];

  return (
    <div className="h-[40rem] flex justify-center items-center px-4">
      <div className="text-4xl mx-auto font-normal text-neutral-600 dark:text-neutral-400">
        Build
        <FlipWords words={words} /> <br />
        with AHA Innovations
      </div>
    </div>
  );
}

export function AHAFlipWordsHero() {
  const capabilities = ["Websites", "Funnels", "Workflows", "Automations"];

  return (
    <div className="text-center">
      <div className="text-4xl md:text-6xl lg:text-7xl font-extrabold mb-6 tracking-tight leading-tight">
        <span className="text-white">Build </span>
        <FlipWords 
          words={capabilities} 
          duration={2500}
          className="text-aha-red font-extrabold"
        />
        <br />
        <span className="text-white">with AHA Innovations</span>
      </div>
      <p className="text-lg md:text-xl text-white/80 max-w-2xl mx-auto leading-relaxed">
        Replace 10+ tools with one simple platform. Grow faster, work smarter.
      </p>
    </div>
  );
}
