# Hero2 Component

## Overview
The `Hero2` component is a modern, animated hero section with a gradient background, mobile-responsive navigation, and a prominent call-to-action area. It's designed specifically for AHA-Innovations with customized content and branding.

## Features
- **Animated gradient background** with multiple colored layers
- **Responsive navigation** with mobile hamburger menu
- **Smooth animations** using Framer Motion
- **Mobile-first design** with responsive breakpoints
- **AHA-Innovations branding** with logo and color scheme
- **Dashboard showcase** with the actual AHA-Innovations demo GIF

## Usage

### Basic Usage
```tsx
import { Hero2 } from '@/components/ui/hero-2-1';

function MyPage() {
  return (
    <div>
      <Hero2 />
    </div>
  );
}
```

### Demo Page
The component has been integrated into a demo page accessible at `/hero-demo`.

## Component Structure

### Main Component
- `Hero2`: The main hero component with navigation and content

### Sub-components
- `NavItem`: Desktop navigation item with optional dropdown indicator
- `MobileNavItem`: Mobile navigation item with arrow indicator

## Customization

### Content Customization
The component includes AHA-Innovations specific content:
- **Title**: "We Make Building Your Business Easy"
- **Subtitle**: "Want to build your custom solution? Click here"
- **Description**: Comprehensive platform description
- **CTA Buttons**: "Get Started Free" and "Watch Demo"
- **Dashboard Image**: `/AHA-innovations-showcase-.gif`

### Navigation Items
Current navigation includes:
- Features
- Pricing  
- Resources (with dropdown)
- Contact
- Sign In button

### Styling
The component uses:
- **Background**: Animated gradient with noise texture
- **Colors**: AHA-Innovations brand colors (aha-red, aha-darkred)
- **Typography**: Responsive text sizing (5xl to 7xl for headlines)
- **Animations**: Framer Motion for smooth transitions

## Dependencies
- React
- Framer Motion (`motion/react`)
- Lucide React (for icons)
- AHA-Innovations Logo component

## CSS Classes Added
The following CSS classes were added to support the component:
```css
.bg-noise {
  background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0);
  background-size: 20px 20px;
}

.bg-grainy {
  background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0);
  background-size: 15px 15px;
}
```

## File Locations
- Component: `src/components/ui/hero-2-1.tsx`
- Demo: `src/pages/HeroDemo.tsx`
- Route: `/hero-demo`

## Responsive Behavior
- **Mobile**: Hamburger menu, stacked buttons, smaller text
- **Tablet**: Expanded navigation, side-by-side buttons
- **Desktop**: Full navigation, large typography, optimal spacing

## Integration Notes
- Uses existing AHA-Innovations Logo component
- Matches brand color scheme
- Includes actual dashboard demo GIF
- Mobile menu animations with smooth transitions
- Backdrop blur effects for modern glass-morphism look
