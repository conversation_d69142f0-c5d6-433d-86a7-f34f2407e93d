import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Button } from './button';
import { ButtonProps } from '@radix-ui/react-dropdown-menu';

interface GlassButtonProps extends ButtonProps {
  children: React.ReactNode;
  variant?: 'default' | 'red' | 'blue' | 'purple';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  glow?: boolean;
  withArrow?: boolean;
  onClick?: () => void;
  className?: string;
}

const GlassButton: React.FC<GlassButtonProps> = ({
  children,
  variant = 'default',
  size = 'default',
  glow = false,
  withArrow = false,
  onClick,
  className,
  ...props
}) => {
  // Determine the glass class based on the variant
  const glassClass = {
    default: 'glass glass-hover',
    red: 'glass-red glass-red-hover',
    blue: 'glass-blue glass-blue-hover',
    purple: 'glass-purple glass-purple-hover',
  }[variant];

  // Determine the glow class
  const glowClass = glow ? {
    default: 'glass-border-glow',
    red: 'glass-border-glow-red',
    blue: 'glass-border-glow-blue',
    purple: 'glass-border-glow-purple',
  }[variant] : '';

  // Combine all the classes
  const combinedClasses = cn(
    glassClass,
    glowClass,
    'border-none text-white font-medium',
    className
  );

  return (
    <motion.div
      whileHover={{ scale: 1.03 }}
      whileTap={{ scale: 0.97 }}
      className="inline-block"
    >
      <Button
        onClick={onClick}
        size={size}
        className={combinedClasses}
        {...props}
      >
        <span className="relative z-10 flex items-center gap-2">
          {children}
          {withArrow && (
            <motion.svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="transition-transform duration-300 group-hover:translate-x-1"
            >
              <path d="M5 12h14"></path>
              <path d="M12 5l7 7-7 7"></path>
            </motion.svg>
          )}
        </span>
      </Button>
    </motion.div>
  );
};

export { GlassButton };
