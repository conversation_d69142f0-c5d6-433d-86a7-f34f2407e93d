# FeaturesSectionWithCardGradient Component

## Overview
The `FeaturesSectionWithCardGradient` component is a modern, responsive feature section that displays features in a grid layout with gradient backgrounds and animated grid patterns.

## Features
- Responsive grid layout (1-4 columns based on screen size)
- Gradient backgrounds with dark/light theme support
- Animated grid pattern overlay
- Customizable feature data
- Modern card design with hover effects

## Usage

### Basic Usage
```tsx
import { FeaturesSectionWithCardGradient } from '@/components/ui/feature-section-with-card-gradient';

function MyPage() {
  return (
    <div>
      <FeaturesSectionWithCardGradient />
    </div>
  );
}
```

### Integration Example
The component has been integrated into the main Index page as the "How AHA-Innovations Help You Grow" section.

## Component Structure

### Main Component
- `FeaturesSectionWithCardGradient`: The main component that renders the feature grid

### Sub-components
- `Grid`: Creates the animated grid pattern overlay
- `GridPattern`: SVG pattern generator for the grid effect

## Customization

### Modifying Feature Data
To customize the features displayed, edit the `grid` array in the component:

```tsx
const grid = [
  {
    title: "Your Feature Title",
    description: "Your feature description goes here..."
  },
  // Add more features...
];
```

### Styling
The component uses Tailwind CSS classes and supports dark/light themes:
- Light theme: `from-neutral-100 to-white`
- Dark theme: `dark:from-neutral-900 dark:to-neutral-950`

## Dependencies
- React
- Tailwind CSS
- No external dependencies required

## File Location
- Component: `src/components/ui/feature-section-with-card-gradient.tsx`
- Demo: `src/pages/FeatureDemo.tsx`
- Integration: `src/pages/Index.tsx` (or `ModernIndex.tsx`)

## Demo
Visit `/feature-demo` to see the component in isolation, or view it integrated on the main homepage.

## Responsive Behavior
- Mobile (sm): 1-2 columns
- Tablet (md): 3 columns  
- Desktop (lg): 4 columns
- Adjustable gap spacing based on screen size
