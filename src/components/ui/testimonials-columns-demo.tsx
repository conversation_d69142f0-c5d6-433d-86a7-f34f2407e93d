import { TestimonialsColumn } from "@/components/ui/testimonials-columns-1";
import { motion } from "motion/react";

const testimonials = [
  {
    text: "Honestly, I was skeptical at first, but AHA-Innovations completely transformed how we handle our client pipeline. The automation saves us about 15 hours a week, and our follow-up rate went from maybe 30% to over 85%. Worth every penny.",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    name: "<PERSON>",
    role: "Marketing Director",
  },
  {
    text: "We were drowning in spreadsheets and missed opportunities. Three months with AHA-Innovations and we've closed more deals than the entire previous quarter. The team actually gets excited about using the system now.",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    name: "Marcus Chen",
    role: "Sales Manager",
  },
  {
    text: "My assistant quit last month, and I thought I was screwed. Turns out AHA-Innovations handles most of what she used to do, plus stuff we never even thought of. I'm actually sleeping better at night knowing nothing's falling through the cracks.",
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    name: "Emily Rodriguez",
    role: "Agency Owner",
  },
  {
    text: "I'll be real - the first week was rough learning everything. But their support team was incredible, and now I can't imagine running our business without it. Client retention is up 40% since we started.",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    name: "David Rodriguez",
    role: "Business Owner",
  },
  {
    text: "Look, I'm not tech-savvy at all. But their onboarding was so smooth, even I figured it out. Now my team actually knows what everyone else is working on. Game changer for our chaos.",
    image: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    name: "Lisa Wang",
    role: "Restaurant Owner",
  },
  {
    text: "We were losing so many leads it wasn't even funny. Six months later, we're booking 3x more consultations and actually following up with everyone. My biggest regret is not finding them sooner.",
    image: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    name: "Jennifer Martinez",
    role: "Fitness Coach",
  },
  {
    text: "I used to spend weekends catching up on admin work. Now I actually have time for my family again. The automated reports show me exactly what's working and what isn't - no more guessing games.",
    image: "https://images.unsplash.com/photo-1560250097-0b93528c311a?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    name: "Robert Kim",
    role: "Consultant",
  },
  {
    text: "Tried 4 different systems before this one. Finally found something that doesn't make me want to throw my laptop out the window. My team actually uses it without me having to nag them.",
    image: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    name: "Amanda Foster",
    role: "Real Estate Broker",
  },
  {
    text: "Best investment I've made in years. My online store went from barely breaking even to consistently hitting 5-figure months. The automated follow-ups alone are worth the price.",
    image: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?q=80&w=150&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    name: "James Wilson",
    role: "E-commerce Owner",
  },
];

const firstColumn = testimonials.slice(0, 3);
const secondColumn = testimonials.slice(3, 6);
const thirdColumn = testimonials.slice(6, 9);

const TestimonialsDemo = () => {
  return (
    <section className="bg-background my-20 relative">
      <div className="container z-10 mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.1, ease: [0.16, 1, 0.3, 1] }}
          viewport={{ once: true }}
          className="flex flex-col items-center justify-center max-w-[540px] mx-auto"
        >
          <div className="flex justify-center">
            <div className="border py-1 px-4 rounded-lg">Testimonials</div>
          </div>

          <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold tracking-tighter mt-5">
            What our users say
          </h2>
          <p className="text-center mt-5 opacity-75">
            See what our customers have to say about us.
          </p>
        </motion.div>

        <div className="flex justify-center gap-6 mt-10 [mask-image:linear-gradient(to_bottom,transparent,black_25%,black_75%,transparent)] max-h-[740px] overflow-hidden">
          <TestimonialsColumn testimonials={firstColumn} duration={15} />
          <TestimonialsColumn testimonials={secondColumn} className="hidden md:block" duration={19} />
          <TestimonialsColumn testimonials={thirdColumn} className="hidden lg:block" duration={17} />
        </div>
      </div>
    </section>
  );
};

export default TestimonialsDemo;
