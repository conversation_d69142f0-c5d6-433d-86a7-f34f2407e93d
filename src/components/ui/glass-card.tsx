import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface GlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  variant?: 'default' | 'medium' | 'heavy' | 'dark' | 'red' | 'blue' | 'purple';
  hoverEffect?: boolean;
  reflection?: boolean;
  glow?: boolean;
  borderGlow?: boolean;
  borderGlowColor?: 'default' | 'red' | 'blue' | 'purple';
  frost?: boolean;
  animate?: boolean;
  delay?: number;
}

const GlassCard: React.FC<GlassCardProps> = ({
  children,
  className,
  variant = 'default',
  hoverEffect = true,
  reflection = false,
  glow = false,
  borderGlow = false,
  borderGlowColor = 'default',
  frost = false,
  animate = true,
  delay = 0,
  ...props
}) => {
  // Determine the glass class based on the variant
  const glassClass = {
    default: 'glass',
    medium: 'glass-medium',
    heavy: 'glass-heavy',
    dark: 'glass-dark',
    red: 'glass-red',
    blue: 'glass-blue',
    purple: 'glass-purple',
  }[variant];

  // Determine the hover class
  const hoverClass = hoverEffect ? {
    default: 'glass-hover',
    red: 'glass-red-hover',
    blue: 'glass-blue-hover',
    purple: 'glass-purple-hover',
  }[variant === 'red' ? 'red' : variant === 'blue' ? 'blue' : variant === 'purple' ? 'purple' : 'default'] : '';

  // Determine the border glow class
  const borderGlowClass = borderGlow ? {
    default: 'glass-border-glow',
    red: 'glass-border-glow-red',
    blue: 'glass-border-glow-blue',
    purple: 'glass-border-glow-purple',
  }[borderGlowColor] : '';

  // Combine all the classes
  const combinedClasses = cn(
    'rounded-xl p-6',
    glassClass,
    hoverClass,
    reflection ? 'glass-reflection' : '',
    glow ? 'glass-card-glow' : '',
    borderGlowClass,
    frost ? 'glass-frost' : '',
    className
  );

  // If animation is enabled, wrap with motion.div
  if (animate) {
    return (
      <motion.div
        className={combinedClasses}
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: delay * 0.1 }}
        viewport={{ once: true, margin: "-50px" }}
        {...props}
      >
        {children}
      </motion.div>
    );
  }

  // Otherwise, return a regular div
  return (
    <div className={combinedClasses} {...props}>
      {children}
    </div>
  );
};

export { GlassCard };
