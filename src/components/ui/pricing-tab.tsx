"use client"

import * as React from "react"
import { motion } from "framer-motion"

import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"

interface TabProps {
  text: string
  selected: boolean
  setSelected: (text: string) => void
  discount?: boolean
}

export function Tab({
  text,
  selected,
  setSelected,
  discount = false,
}: TabProps) {
  return (
    <button
      onClick={() => setSelected(text)}
      className={cn(
        "relative w-fit px-4 py-2 text-sm font-semibold capitalize",
        "text-white/70 hover:text-white transition-colors",
        selected && "text-white",
        discount && "flex items-center justify-center gap-2.5"
      )}
    >
      <span className="relative z-10">{text}</span>
      {selected && (
        <motion.span
          layoutId="tab"
          transition={{ type: "spring", duration: 0.4 }}
          className="absolute inset-0 z-0 rounded-full bg-aha-red/90 shadow-sm"
        />
      )}
      {discount && (
        <Badge
          className={cn(
            "relative z-10 whitespace-nowrap shadow-none bg-yellow-500/90 text-black font-bold",
            selected && "bg-yellow-400/90"
          )}
        >
          Save 35%
        </Badge>
      )}
    </button>
  )
}
