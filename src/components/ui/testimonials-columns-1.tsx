"use client";
import React from "react";
import { motion } from "motion/react";

interface Testimonial {
  text: string;
  image: string;
  name: string;
  role: string;
}

export const TestimonialsColumn = (props: {
  className?: string;
  testimonials: Testimonial[];
  duration?: number;
}) => {
  return (
    <div className={props.className}>
      <motion.div
        animate={{
          translateY: "-50%",
        }}
        transition={{
          duration: props.duration || 10,
          repeat: Infinity,
          ease: "linear",
          repeatType: "loop",
        }}
        className="flex flex-col gap-6 pb-6"
      >
        {[
          ...new Array(2).fill(0).map((_, index) => (
            <React.Fragment key={index}>
              {props.testimonials.map(({ text, image, name, role }, i) => (
                <div className="p-8 rounded-3xl bg-white/[0.02] backdrop-blur-xl border border-white/[0.08] shadow-2xl shadow-black/20 hover:shadow-aha-red/20 max-w-xs w-full transition-all duration-500 hover:border-aha-red/30 hover:bg-white/[0.05] hover:backdrop-blur-2xl group relative overflow-hidden hover:scale-[1.02]" key={i}>
                  {/* Glass-like inner glow */}
                  <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-white/[0.05] via-transparent to-transparent opacity-40 pointer-events-none"></div>
                  <div className="absolute inset-px rounded-3xl bg-gradient-to-t from-transparent via-white/[0.01] to-white/[0.03] pointer-events-none"></div>

                  {/* Content */}
                  <div className="relative z-10">
                    {/* Quote symbol */}
                    <div className="text-white/30 text-4xl font-gotham mb-4 group-hover:text-white/50 transition-colors duration-500 drop-shadow-lg">"</div>

                    <div className="text-gray-200 text-sm leading-relaxed mb-6 drop-shadow-sm">{text}</div>

                    <div className="flex items-center gap-3 mt-auto">
                      {/* Quote symbol instead of avatar */}
                      <div className="h-10 w-10 rounded-full bg-gradient-to-br from-white/[0.08] to-white/[0.03] border border-white/[0.15] flex items-center justify-center backdrop-blur-sm shadow-lg">
                        <span className="text-white/70 text-lg font-gotham drop-shadow-sm">"</span>
                      </div>
                      <div className="flex flex-col">
                        <div className="font-semibold tracking-tight leading-5 text-white text-sm drop-shadow-sm">{name}</div>
                        <div className="leading-5 text-gray-300 tracking-tight text-xs">{role}</div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </React.Fragment>
          )),
        ]}
      </motion.div>
    </div>
  );
};
