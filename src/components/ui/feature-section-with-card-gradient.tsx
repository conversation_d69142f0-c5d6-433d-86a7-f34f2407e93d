import React from "react";
import { useId } from "react";
import { DotPattern } from "@/components/ui/dot-pattern";

export function FeaturesSectionWithCardGradient() {
  return (
    <div className="py-20 lg:py-40">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-10 md:gap-2 max-w-7xl mx-auto">
        {grid.map((feature) => (
          <div
            key={feature.title}
            className="relative bg-white/5 backdrop-blur-sm border border-white/10 p-6 rounded-3xl overflow-hidden hover:border-aha-red/20 transition-all duration-300"
          >
            {/* Dot pattern for feature cards */}
            <DotPattern
              width={16}
              height={16}
              cx={0.6}
              cy={0.6}
              cr={0.25}
              className="fill-white/[0.03] dark:fill-white/[0.02] [mask-image:radial-gradient(150px_circle_at_center,white,transparent)]"
            />
            <Grid size={20} />
            <p className="text-base font-bold text-neutral-800 dark:text-white relative z-20">
              {feature.title}
            </p>
            <p className="text-neutral-600 dark:text-neutral-400 mt-4 text-base font-normal relative z-20">
              {feature.description}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}

const grid = [
  {
    title: "GoHighLevel CRM Integration",
    description:
      "Seamlessly integrate with GoHighLevel's powerful CRM system to manage leads, automate follow-ups, and track customer interactions.",
  },
  {
    title: "Automated Lead Nurturing",
    description:
      "Set up sophisticated email sequences and SMS campaigns that nurture leads automatically, converting prospects into paying customers.",
  },
  {
    title: "Sales Funnel Optimization",
    description:
      "Build high-converting sales funnels with landing pages, forms, and checkout processes that maximize your revenue potential.",
  },
  {
    title: "Multi-Channel Marketing",
    description:
      "Reach your audience across email, SMS, social media, and voice calls from one unified platform to increase engagement.",
  },
  {
    title: "Advanced Analytics & Reporting",
    description:
      "Get detailed insights into your marketing performance with comprehensive analytics that help you make data-driven decisions.",
  },
  {
    title: "Appointment Scheduling",
    description:
      "Streamline your booking process with automated appointment scheduling that syncs with your calendar and sends reminders.",
  },
  {
    title: "White-Label Solutions",
    description:
      "Offer marketing automation services under your own brand with our white-label platform and grow your agency revenue.",
  },
  {
    title: "24/7 Support & Training",
    description:
      "Get expert support and comprehensive training to ensure you maximize the potential of your marketing automation setup.",
  },
];

export const Grid = ({
  pattern,
  size,
}: {
  pattern?: number[][];
  size?: number;
}) => {
  const p = pattern ?? [
    [Math.floor(Math.random() * 4) + 7, Math.floor(Math.random() * 6) + 1],
    [Math.floor(Math.random() * 4) + 7, Math.floor(Math.random() * 6) + 1],
    [Math.floor(Math.random() * 4) + 7, Math.floor(Math.random() * 6) + 1],
    [Math.floor(Math.random() * 4) + 7, Math.floor(Math.random() * 6) + 1],
    [Math.floor(Math.random() * 4) + 7, Math.floor(Math.random() * 6) + 1],
  ];
  return (
    <div className="pointer-events-none absolute left-1/2 top-0  -ml-20 -mt-2 h-full w-full [mask-image:linear-gradient(white,transparent)]">
      <div className="absolute inset-0 bg-gradient-to-r  [mask-image:radial-gradient(farthest-side_at_top,white,transparent)] dark:from-zinc-900/30 from-zinc-100/30 to-zinc-300/30 dark:to-zinc-900/30 opacity-100">
        <GridPattern
          width={size ?? 20}
          height={size ?? 20}
          x="-12"
          y="4"
          squares={p}
          className="absolute inset-0 h-full w-full  mix-blend-overlay dark:fill-white/10 dark:stroke-white/10 stroke-black/10 fill-black/10"
        />
      </div>
    </div>
  );
};

export function GridPattern({ width, height, x, y, squares, ...props }: any) {
  const patternId = useId();

  return (
    <svg aria-hidden="true" {...props}>
      <defs>
        <pattern
          id={patternId}
          width={width}
          height={height}
          patternUnits="userSpaceOnUse"
          x={x}
          y={y}
        >
          <path d={`M.5 ${height}V.5H${width}`} fill="none" />
        </pattern>
      </defs>
      <rect
        width="100%"
        height="100%"
        strokeWidth={0}
        fill={`url(#${patternId})`}
      />
      {squares && (
        <svg x={x} y={y} className="overflow-visible">
          {squares.map(([x, y]: any) => (
            <rect
              strokeWidth="0"
              key={`${x}-${y}`}
              width={width + 1}
              height={height + 1}
              x={x * width}
              y={y * height}
            />
          ))}
        </svg>
      )}
    </svg>
  );
}
