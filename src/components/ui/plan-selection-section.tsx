"use client"

import * as React from "react"
import { useNavigate } from "react-router-dom"
import { type PricingTier } from "@/components/ui/pricing-card"
import { Tab } from "@/components/ui/pricing-tab"
import { CalendarPopup } from "@/components/ui/calendar-popup"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DotPattern } from "@/components/ui/dot-pattern"
import { BadgeCheck, ArrowRight } from "lucide-react"
import NumberFlow from "@number-flow/react"
import { cn } from "@/lib/utils"

interface PlanSelectionSectionProps {
  title: string
  subtitle: string
  tiers: PricingTier[]
  frequencies: string[]
  userInfo?: any
  onPlanSelect?: (tier: PricingTier) => void
}

export function PlanSelectionSection({
  title,
  subtitle,
  tiers,
  frequencies,
  userInfo = {},
  onPlanSelect
}: PlanSelectionSectionProps) {
  const [selectedFrequency, setSelectedFrequency] = React.useState(frequencies[0])
  const [showCalendar, setShowCalendar] = React.useState(false)
  const navigate = useNavigate()

  const handlePlanSelection = async (tier: PricingTier) => {
    if (onPlanSelect) {
      onPlanSelect(tier);
    } else {
      // Default behavior if no handler provided
      if (tier.id === 'free') {
        // Redirect to onboarding for free plan
        navigate('/onboarding', {
          state: {
            userInfo,
            selectedPlan: tier
          }
        });
      } else {
        // Redirect to Stripe checkout for paid plans (stays in same tab)
        const stripeLink = tier.stripeLinks?.[selectedFrequency];
        if (stripeLink) {
          window.location.href = stripeLink;
        } else {
          console.error('No Stripe link found for plan:', tier.id, selectedFrequency);
        }
      }
    }
  };

  // Background components for highlighted and popular cards
  const HighlightedBackground = () => (
    <div className="absolute inset-0 bg-gradient-to-br from-aha-red/20 to-aha-darkred/20 rounded-lg" />
  );

  const PopularBackground = () => (
    <div className="absolute inset-0 bg-gradient-to-br from-aha-red/10 to-aha-darkred/10 rounded-lg" />
  );

  const PlanCard = ({ tier }: { tier: PricingTier }) => {
    const price = tier.price[selectedFrequency];
    const originalPrice = tier.originalPrice?.[selectedFrequency];
    const isHighlighted = tier.highlighted;
    const isPopular = tier.popular;
    const isFree = tier.id === 'free';
    const isContactUs = tier.cta === "Contact Us";

    return (
      <div className="relative">
        {/* Custom pricing card with integrated buttons - enlarged for plan selection */}
        <Card
          className={cn(
            "relative flex flex-col gap-6 overflow-hidden p-8 glass-card border-white/10 min-h-[520px]",
            isHighlighted
              ? "bg-gradient-to-br from-aha-red/90 to-aha-darkred/90 text-white"
              : "bg-black/20 backdrop-blur-sm text-white",
            isPopular && "ring-2 ring-aha-red"
          )}
        >
          {/* Dot pattern */}
          <DotPattern
            width={20}
            height={20}
            cx={0.8}
            cy={0.8}
            cr={0.4}
            className={cn(
              isHighlighted
                ? "fill-white/[0.08] [mask-image:radial-gradient(300px_circle_at_center,white,transparent)]"
                : isPopular
                ? "fill-aha-red/[0.04] [mask-image:radial-gradient(300px_circle_at_center,white,transparent)]"
                : "fill-white/[0.02] [mask-image:radial-gradient(300px_circle_at_center,white,transparent)]"
            )}
          />
          {isHighlighted && <HighlightedBackground />}
          {isPopular && <PopularBackground />}

          <h2 className="flex items-center gap-3 text-xl font-medium capitalize text-white font-gotham">
            {tier.name}
            {isPopular && (
              <Badge className="mt-1 z-10 bg-yellow-500/90 text-black font-bold text-sm">
                Popular
              </Badge>
            )}
          </h2>



          {/* Price section */}
          <div className="space-y-3">
            <div className="min-h-[90px] flex flex-col justify-center">
              {typeof price === "number" ? (
                <>
                  {/* Show crossed-out original price with discount */}
                  {originalPrice && (
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <NumberFlow
                        format={{
                          style: "currency",
                          currency: "USD",
                        }}
                        value={originalPrice as number}
                        className="text-sm font-medium font-gotham text-white/50 line-through"
                      />
                      {tier.limitedOffer ? (
                        <div className="relative bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border border-yellow-500/40 text-yellow-200 px-3 py-1.5 rounded-lg font-bold text-[10px] leading-tight max-w-[140px] text-center shadow-lg shadow-yellow-500/10">
                          <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-lg animate-pulse"></div>
                          <span className="relative z-10">{tier.limitedOffer}</span>
                        </div>
                      ) : (
                        <span className="text-xs bg-red-500/20 text-red-300 px-2 py-0.5 rounded-full font-bold">
                          SAVE ${((originalPrice as number) - price).toFixed(0)}
                        </span>
                      )}
                    </div>
                  )}
                  <div className="text-center">
                    <NumberFlow
                      format={{
                        style: "currency",
                        currency: "USD",
                      }}
                      value={price}
                      className="text-4xl font-medium font-gotham"
                    />
                    <p className="text-sm text-white/70 mt-2">
                      Per month/user
                    </p>
                  </div>
                </>
              ) : (
                <div className="text-center">
                  <h1 className="text-5xl font-medium font-gotham">{price}</h1>
                </div>
              )}
            </div>
          </div>

          {/* Features */}
          <div className="flex-1 space-y-3">
            <h3 className="text-sm font-medium text-white/90 font-gotham">{tier.description}</h3>
            <ul className="space-y-2">
              {tier.features.map((feature, index) => (
                <li
                  key={index}
                  className="flex items-center gap-3 text-sm font-medium text-white/80"
                >
                  <BadgeCheck className="h-4 w-4 text-aha-red" />
                  {feature}
                </li>
              ))}
            </ul>
          </div>

          {/* Integrated Button Section */}
          <div className="mt-auto pt-6">
            {isFree ? (
              <>
                <Button
                  onClick={() => handlePlanSelection(tier)}
                  className="w-full relative group overflow-hidden bg-aha-red hover:bg-aha-darkred text-white rounded-full py-3"
                >
                  <span className="relative z-10 flex items-center justify-center gap-2">
                    Continue with Free Plan
                    <ArrowRight className="h-4 w-4" />
                  </span>
                  <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300 rounded-full"></span>
                </Button>
                <p className="text-xs text-center text-white/90 mt-3 font-medium">No credit card required</p>
              </>
            ) : isContactUs ? (
              <Button
                onClick={() => handlePlanSelection(tier)}
                className={cn(
                  "w-full relative group overflow-hidden rounded-full py-3",
                  isHighlighted
                    ? "bg-white text-aha-red hover:bg-white/90"
                    : "bg-aha-red hover:bg-aha-darkred text-white"
                )}
              >
                <span className="relative z-10 flex items-center justify-center gap-2">
                  {tier.cta}
                  <ArrowRight className="h-4 w-4" />
                </span>
                {!isHighlighted && (
                  <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300 rounded-full"></span>
                )}
              </Button>
            ) : (
              <>
                <Button
                  onClick={() => handlePlanSelection(tier)}
                  className={cn(
                    "w-full relative group overflow-hidden rounded-full py-3",
                    isHighlighted
                      ? "bg-white text-aha-red hover:bg-white/90"
                      : "bg-aha-red hover:bg-aha-darkred text-white"
                  )}
                >
                  <span className="relative z-10 flex items-center justify-center gap-2">
                    Choose {tier.name}
                    <ArrowRight className="h-4 w-4" />
                  </span>
                  {!isHighlighted && (
                    <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300 rounded-full"></span>
                  )}
                </Button>
                <p className="text-xs text-center text-white/70 mt-3">
                  14-day free trial • Credit card required
                </p>
              </>
            )}
          </div>
        </Card>
      </div>
    );
  };

  return (
    <section className="flex flex-col items-center gap-10 py-10">
      <div className="space-y-7 text-center">
        <div className="space-y-4">
          <h1 className="text-4xl font-medium md:text-5xl text-white font-gotham">{title}</h1>
          <p className="text-white/90 font-medium leading-relaxed tracking-wide font-gotham">{subtitle}</p>
        </div>
        <div className="mx-auto flex w-fit rounded-full bg-black/20 backdrop-blur-sm border border-white/10 p-1">
          {frequencies.map((freq) => (
            <Tab
              key={freq}
              text={freq}
              selected={selectedFrequency === freq}
              setSelected={setSelectedFrequency}
              discount={freq === "yearly"}
            />
          ))}
        </div>
      </div>

      <div className="grid w-full max-w-6xl gap-6 sm:grid-cols-2 xl:grid-cols-4">
        {tiers.map((tier) => (
          <PlanCard key={tier.name} tier={tier} />
        ))}
      </div>



      {/* Calendar Popup */}
      <CalendarPopup
        isOpen={showCalendar}
        onClose={() => setShowCalendar(false)}
      />
    </section>
  )
}
