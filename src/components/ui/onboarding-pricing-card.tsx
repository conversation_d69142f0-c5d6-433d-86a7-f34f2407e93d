"use client"

import * as React from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowR<PERSON> } from "lucide-react"
import NumberF<PERSON> from "@number-flow/react"

import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { DotPattern } from "@/components/ui/dot-pattern"

export interface OnboardingPricingTier {
  id: string
  name: string
  price: Record<string, number | string>
  originalPrice?: Record<string, number | string>
  description: string
  limitedOffer?: string
  features: string[]
  cta: string
  highlighted?: boolean
  popular?: boolean
  stripeLinks?: {
    monthly?: string
    yearly?: string
  }
}

interface OnboardingPricingCardProps {
  tier: OnboardingPricingTier
  paymentFrequency: string
  onSelectPlan: (planId: string, planName: string, isUpgrade: boolean) => void
  onContactUs?: () => void
}

export function OnboardingPricingCard({ 
  tier, 
  paymentFrequency, 
  onSelectPlan,
  onContactUs 
}: OnboardingPricingCardProps) {
  const price = tier.price[paymentFrequency]
  const isHighlighted = tier.highlighted
  const isPopular = tier.popular
  const isFree = tier.id === 'free' || tier.name.toLowerCase().includes('free')
  const isContactUs = tier.cta === "Contact Us"

  const handlePlanSelection = () => {
    if (isContactUs) {
      onContactUs?.()
    } else if (isFree) {
      // Continue with free plan
      onSelectPlan(tier.id, tier.name, false)
    } else {
      // This is an upgrade - will show payment options
      onSelectPlan(tier.id, tier.name, true)
    }
  }

  const handleDirectPayment = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    const stripeLink = tier.stripeLinks?.[paymentFrequency]
    if (stripeLink) {
      window.open(stripeLink, '_blank', 'noopener,noreferrer')
    }
  }

  return (
    <Card
      className={cn(
        "relative flex flex-col gap-8 overflow-hidden p-6 glass-card border-white/10",
        isHighlighted
          ? "bg-gradient-to-br from-aha-red/90 to-aha-darkred/90 text-white"
          : "bg-black/20 backdrop-blur-sm text-white",
        isPopular && "ring-2 ring-aha-red"
      )}
    >
      {/* Dot pattern for pricing cards */}
      <DotPattern
        width={20}
        height={20}
        cx={0.8}
        cy={0.8}
        cr={0.4}
        className={cn(
          isHighlighted
            ? "fill-white/[0.08] [mask-image:radial-gradient(300px_circle_at_center,white,transparent)]"
            : isPopular
            ? "fill-aha-red/[0.04] [mask-image:radial-gradient(300px_circle_at_center,white,transparent)]"
            : "fill-white/[0.02] [mask-image:radial-gradient(300px_circle_at_center,white,transparent)]"
        )}
      />
      {isHighlighted && <HighlightedBackground />}
      {isPopular && <PopularBackground />}

      <h2 className="flex items-center gap-3 text-xl font-medium capitalize text-white font-gotham">
        {tier.name}
        {isPopular && (
          <Badge className="mt-1 z-10 bg-yellow-500/90 text-black font-bold">
            Popular
          </Badge>
        )}
      </h2>

      <div className="relative">
        {/* Limited Offer Banner */}
        {tier.limitedOffer && (
          <div className="mb-3 px-2 py-1 bg-yellow-500/20 border border-yellow-500/30 rounded-md">
            <p className="text-xs font-bold text-yellow-300 text-center">
              {tier.limitedOffer}
            </p>
          </div>
        )}

        <div className="min-h-[80px] flex flex-col justify-center">
          {typeof price === "number" ? (
            <>
              {/* Show crossed-out original price with discount */}
              {tier.originalPrice && tier.originalPrice[paymentFrequency] && (
                <div className="flex items-center justify-center gap-2 mb-2">
                  <NumberFlow
                    format={{
                      style: "currency",
                      currency: "USD",
                    }}
                    value={tier.originalPrice[paymentFrequency] as number}
                    className="text-sm font-medium font-gotham text-white/50 line-through"
                  />
                  <span className="text-xs bg-red-500/20 text-red-300 px-2 py-0.5 rounded-full font-bold">
                    SAVE ${((tier.originalPrice[paymentFrequency] as number) - price).toFixed(0)}
                  </span>
                </div>
              )}
              <div className="text-center">
                <NumberFlow
                  format={{
                    style: "currency",
                    currency: "USD",
                  }}
                  value={price}
                  className="text-4xl font-medium font-gotham"
                />
                <p className="text-xs text-white/70 mt-1">
                  Per month/user
                </p>
              </div>
            </>
          ) : (
            <div className="text-center">
              <h1 className="text-4xl font-medium font-gotham">{price}</h1>
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 space-y-2">
        <h3 className="text-sm font-medium text-white/90 font-gotham">{tier.description}</h3>
        <ul className="space-y-2">
          {tier.features.map((feature, index) => (
            <li
              key={index}
              className="flex items-center gap-2 text-sm font-medium text-white/80"
            >
              <BadgeCheck className="h-4 w-4 text-aha-red" />
              {feature}
            </li>
          ))}
        </ul>
      </div>

      {/* Button Section - Different for Onboarding */}
      <div className="space-y-3">
        {isFree ? (
          <>
            <Button
              onClick={handlePlanSelection}
              className={cn(
                "w-full relative group overflow-hidden",
                isHighlighted
                  ? "bg-white text-aha-red hover:bg-white/90"
                  : "bg-aha-red hover:bg-aha-darkred text-white"
              )}
            >
              <span className="relative z-10 flex items-center justify-center gap-2">
                Continue with Free Plan
                <ArrowRight className="h-4 w-4" />
              </span>
              {!isHighlighted && (
                <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
              )}
            </Button>
            <p className="text-xs text-center text-white/90 mt-3 font-medium">No credit card required</p>
          </>
        ) : isContactUs ? (
          <Button
            onClick={handlePlanSelection}
            className={cn(
              "w-full relative group overflow-hidden",
              isHighlighted
                ? "bg-white text-aha-red hover:bg-white/90"
                : "bg-aha-red hover:bg-aha-darkred text-white"
            )}
          >
            <span className="relative z-10 flex items-center justify-center gap-2">
              {tier.cta}
              <ArrowRight className="h-4 w-4" />
            </span>
            {!isHighlighted && (
              <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
            )}
          </Button>
        ) : (
          <>
            {/* Primary button - Start Free Trial */}
            <Button
              onClick={handlePlanSelection}
              className={cn(
                "w-full relative group overflow-hidden",
                isHighlighted
                  ? "bg-white text-aha-red hover:bg-white/90"
                  : "bg-aha-red hover:bg-aha-darkred text-white"
              )}
            >
              <span className="relative z-10 flex items-center justify-center gap-2">
                Start Free Trial
                <ArrowRight className="h-4 w-4" />
              </span>
              {!isHighlighted && (
                <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
              )}
            </Button>
            
            {/* Secondary button - Buy Now (if Stripe link available) */}
            {tier.stripeLinks?.[paymentFrequency] && (
              <Button
                onClick={handleDirectPayment}
                variant="outline"
                className="w-full border-white/20 text-white/90 hover:bg-white/10 hover:text-white"
              >
                <span className="flex items-center justify-center gap-2">
                  Buy Now - {tier.cta}
                  <ArrowRight className="h-4 w-4" />
                </span>
              </Button>
            )}
            
            <p className="text-xs text-center text-white/70 mt-2">
              14-day free trial • No credit card required for trial
            </p>
          </>
        )}
      </div>
    </Card>
  )
}

const HighlightedBackground = () => (
  <div className="absolute inset-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:45px_45px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_110%)]" />
)

const PopularBackground = () => (
  <div className="absolute inset-0 bg-[radial-gradient(ellipse_80%_80%_at_50%_-20%,rgba(120,119,198,0.1),rgba(255,255,255,0))]" />
)
