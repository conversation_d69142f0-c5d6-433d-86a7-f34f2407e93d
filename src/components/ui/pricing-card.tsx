"use client"

import * as React from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowR<PERSON> } from "lucide-react"
import NumberF<PERSON> from "@number-flow/react"
import { useNavigate } from "react-router-dom"

import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { DotPattern } from "@/components/ui/dot-pattern"

export interface PricingTier {
  name: string
  price: Record<string, number | string>
  originalPrice?: Record<string, number | string>
  description: string
  limitedOffer?: string
  features: string[]
  cta: string
  highlighted?: boolean
  popular?: boolean
  stripeLinks?: {
    monthly?: string
    yearly?: string
  }
}

interface PricingCardProps {
  tier: PricingTier
  paymentFrequency: string
  onContactUs?: () => void
  onPlanSelect?: (tier: PricingTier) => void
  showButtons?: boolean
  hasAbsoluteButtons?: boolean // For plan selection page with absolute positioned buttons
}

export function PricingCard({ tier, paymentFrequency, onContactUs, onPlanSelect, showButtons = false, hasAbsoluteButtons = false }: PricingCardProps) {
  const navigate = useNavigate()
  const price = tier.price[paymentFrequency]
  const isHighlighted = tier.highlighted
  const isPopular = tier.popular

  const handleGetStartedClick = () => {
    if (tier.cta === "Contact Us") {
      onContactUs?.()
    } else if (onPlanSelect) {
      onPlanSelect(tier)
    } else {
      // All cards now redirect to signup for lead capture
      navigate('/signup')
    }
  }

  const handleBuyNowClick = (e) => {
    e.preventDefault()
    e.stopPropagation()
    const stripeLink = tier.stripeLinks?.[paymentFrequency]
    if (stripeLink) {
      window.open(stripeLink, '_blank', 'noopener,noreferrer')
    }
  }

  const isFree = tier.name.toLowerCase().includes('free')
  const isEnterprise = tier.name.toLowerCase().includes('enterprise')
  const isContactUs = tier.cta === "Contact Us"

  return (
    <Card
      className={cn(
        "relative flex flex-col gap-8 overflow-hidden p-6 glass-card border-white/10",
        isHighlighted
          ? "bg-gradient-to-br from-aha-red/90 to-aha-darkred/90 text-white"
          : "bg-black/20 backdrop-blur-sm text-white",
        isPopular && "ring-2 ring-aha-red",
        hasAbsoluteButtons && "pb-20" // Extra bottom padding for absolute positioned buttons
      )}
    >
      {/* Dot pattern for pricing cards */}
      <DotPattern
        width={20}
        height={20}
        cx={0.8}
        cy={0.8}
        cr={0.4}
        className={cn(
          isHighlighted
            ? "fill-white/[0.08] [mask-image:radial-gradient(300px_circle_at_center,white,transparent)]"
            : isPopular
            ? "fill-aha-red/[0.04] [mask-image:radial-gradient(300px_circle_at_center,white,transparent)]"
            : "fill-white/[0.02] [mask-image:radial-gradient(300px_circle_at_center,white,transparent)]"
        )}
      />
      {isHighlighted && <HighlightedBackground />}
      {isPopular && <PopularBackground />}

      <h2 className="flex items-center gap-3 text-xl font-medium capitalize text-white font-gotham">
        {tier.name}
        {isPopular && (
          <Badge className="mt-1 z-10 bg-yellow-500/90 text-black font-bold">
            Popular
          </Badge>
        )}
      </h2>

      <div className="relative">
        {/* Limited Offer Banner for Enterprise */}
        {tier.limitedOffer && (
          <div className="mb-3 px-2 py-1 bg-yellow-500/20 border border-yellow-500/30 rounded-md">
            <p className="text-xs font-bold text-yellow-300 text-center">
              {tier.limitedOffer}
            </p>
          </div>
        )}

        <div className="min-h-[80px] flex flex-col justify-center">
          {typeof price === "number" ? (
            <>
              {/* Show crossed-out original price inline with discount badge - similar to Trae */}
              {tier.originalPrice && tier.originalPrice[paymentFrequency] && (
                <div className="flex items-center justify-center gap-2 mb-2">
                  <NumberFlow
                    format={{
                      style: "currency",
                      currency: "USD",
                    }}
                    value={tier.originalPrice[paymentFrequency] as number}
                    className="text-sm font-medium font-gotham text-white/50 line-through"
                  />
                  <span className="text-xs bg-red-500/20 text-red-300 px-2 py-0.5 rounded-full font-bold">
                    SAVE ${((tier.originalPrice[paymentFrequency] as number) - price).toFixed(0)}
                  </span>
                </div>
              )}
              <div className="text-center">
                <NumberFlow
                  format={{
                    style: "currency",
                    currency: "USD",
                  }}
                  value={price}
                  className="text-4xl font-medium font-gotham"
                />
                <p className="text-xs text-white/70 mt-1">
                  Per month/user
                </p>
              </div>
            </>
          ) : (
            <div className="text-center">
              <h1 className="text-4xl font-medium font-gotham">{price}</h1>
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 space-y-2">
        <h3 className="text-sm font-medium text-white/90 font-gotham">{tier.description}</h3>
        <ul className="space-y-2">
          {tier.features.map((feature, index) => (
            <li
              key={index}
              className="flex items-center gap-2 text-sm font-medium text-white/80"
            >
              <BadgeCheck className="h-4 w-4 text-aha-red" />
              {feature}
            </li>
          ))}
        </ul>
      </div>

      {/* Button section - only show if showButtons is true */}
      {showButtons && (
        <div className="mt-6 space-y-3">
          {isFree ? (
            <Button
              onClick={handleGetStartedClick}
              className="w-full rounded-full bg-white/10 hover:bg-white/20 text-white border border-white/20 hover:border-white/40 transition-all duration-300 font-gotham"
            >
              {tier.cta}
            </Button>
          ) : isContactUs ? (
            <Button
              onClick={handleGetStartedClick}
              className="w-full rounded-full bg-white/10 hover:bg-white/20 text-white border border-white/20 hover:border-white/40 transition-all duration-300 font-gotham"
            >
              {tier.cta}
            </Button>
          ) : (
            <>
              <Button
                onClick={handleGetStartedClick}
                className="w-full rounded-full bg-white/10 hover:bg-white/20 text-white border border-white/20 hover:border-white/40 transition-all duration-300 font-gotham"
              >
                Get Started
              </Button>
              <Button
                onClick={handleBuyNowClick}
                className="w-full rounded-full bg-aha-red hover:bg-aha-darkred text-white transition-all duration-300 font-gotham"
              >
                {tier.cta} <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </>
          )}

          {!isFree && !isContactUs && (
            <p className="text-xs text-white/60 text-center">
              14-day free trial • Credit card required
            </p>
          )}

          {isFree && (
            <p className="text-xs text-white/60 text-center">
              No credit card required
            </p>
          )}
        </div>
      )}

    </Card>
  )
}

const HighlightedBackground = () => (
  <div className="absolute inset-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] bg-[size:45px_45px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_110%)]" />
)

const PopularBackground = () => (
  <div className="absolute inset-0 bg-[radial-gradient(ellipse_80%_80%_at_50%_-20%,rgba(120,119,198,0.1),rgba(255,255,255,0))]" />
)
