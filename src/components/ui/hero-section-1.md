# HeroSection Component

## Overview
The `HeroSection` component is a sophisticated, animated hero section with advanced motion effects, responsive navigation, and a modern design. It's built using Framer Motion for smooth animations and includes a full header navigation system.

## Features
- **Advanced Animations**: Uses Framer Motion with staggered animations and spring physics
- **Responsive Navigation**: Adaptive header that changes on scroll with mobile menu
- **Background Effects**: Subtle radial gradients and animated background elements
- **Customer Showcase**: Interactive customer logos section with hover effects
- **AHA-Innovations Branding**: Customized content, colors, and dashboard showcase
- **Modern Design**: Clean typography, proper spacing, and professional layout

## Usage

### Basic Usage
```tsx
import { HeroSection } from '@/components/ui/hero-section-1';

function MyPage() {
  return (
    <div className="min-h-screen bg-aha-dark text-white">
      <HeroSection />
    </div>
  );
}
```

### Demo Page
The component has been integrated into a demo page accessible at `/hero-demo`.

## Component Structure

### Main Components
- `HeroSection`: The main hero component with all sections
- `HeroHeader`: Responsive navigation header with scroll effects
- `AnimatedGroup`: Reusable animation wrapper component

### Key Sections
1. **Header Navigation**: Fixed header with scroll-based styling changes
2. **Hero Content**: Main title, description, and CTA buttons
3. **Dashboard Preview**: Animated showcase of the AHA-Innovations dashboard
4. **Customer Logos**: Interactive grid of client/partner logos

## Customization

### Content Customization
The component includes AHA-Innovations specific content:
- **Title**: "We Make Building Your Business Easy"
- **Subtitle Badge**: "Want to build your custom solution? Click here"
- **Description**: Comprehensive platform description
- **CTA Buttons**: "Get Started Free" and "Watch Demo"
- **Dashboard Image**: `/AHA-innovations-showcase-.gif`

### Navigation Items
Current navigation includes:
- Features (`/features`)
- Pricing (`/pricing`)
- Contact (`/contact`)
- About (`/about`)

### Customer Logos
Uses actual AHA-Innovations client logos:
- Company logos (1-4).png
- StephenLovino.png
- MilllennialBusinessAcademy.png
- AHA-logomodern.png
- Primary Red.png

### Animation Variants
The component uses several animation presets:
- **Blur animations**: Elements fade in with blur effects
- **Staggered animations**: Children animate in sequence
- **Spring physics**: Natural, bouncy motion effects
- **Scroll-triggered**: Animations based on scroll position

## Dependencies
- React
- React Router DOM
- Framer Motion
- Lucide React (for icons)
- AHA-Innovations Logo component
- AnimatedGroup component

## CSS Classes Added
The following CSS classes were added to support the component:
```css
.aspect-15/8 {
  aspect-ratio: 15 / 8;
}

.h-10.5 {
  height: 2.625rem;
}

.inset-shadow-2xs {
  box-shadow: inset 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.blur-xs {
  filter: blur(2px);
}

.contain-strict {
  contain: strict;
}
```

## File Locations
- Main Component: `src/components/ui/hero-section-1.tsx`
- Animation Helper: `src/components/ui/animated-group.tsx`
- Demo: `src/pages/HeroDemo.tsx`
- Route: `/hero-demo`

## Responsive Behavior
- **Mobile**: Hamburger menu, stacked buttons, simplified layout
- **Tablet**: Expanded navigation, responsive grid
- **Desktop**: Full navigation, optimal spacing, advanced animations

## Animation Details
- **Initial Load**: Staggered fade-in with blur effects
- **Scroll Effects**: Header transforms and background parallax
- **Hover States**: Interactive customer logos and buttons
- **Mobile Menu**: Smooth slide animations with backdrop

## Integration Notes
- Uses existing AHA-Innovations Logo component
- Matches brand color scheme (aha-red, aha-darkred)
- Includes actual dashboard demo GIF
- Navigation uses React Router for SPA routing
- Fully responsive with mobile-first approach
- Optimized animations for performance

## Performance Considerations
- Uses `contain: strict` for animation optimization
- Lazy loading for background images
- Efficient re-renders with React.memo patterns
- Smooth 60fps animations with hardware acceleration
