"use client"

import * as React from "react"
import { Link } from "react-router-dom"
import { PricingCard, type PricingTier } from "@/components/ui/pricing-card"
import { Tab } from "@/components/ui/pricing-tab"
import { CalendarPopup } from "@/components/ui/calendar-popup"

interface PricingSectionProps {
  title: string
  subtitle: string
  tiers: PricingTier[]
  frequencies: string[]
}

export function PricingSection({
  title,
  subtitle,
  tiers,
  frequencies,
}: PricingSectionProps) {
  const [selectedFrequency, setSelectedFrequency] = React.useState(frequencies[0])
  const [showCalendar, setShowCalendar] = React.useState(false)

  return (
    <section className="flex flex-col items-center gap-10 py-10">
      <div className="space-y-7 text-center">
        <div className="space-y-4">
          <h1 className="text-4xl font-medium md:text-5xl text-white font-gotham">{title}</h1>
          <p className="text-white/90 font-medium leading-relaxed tracking-wide font-gotham">{subtitle}</p>
        </div>
        <div className="mx-auto flex w-fit rounded-full bg-black/20 backdrop-blur-sm border border-white/10 p-1">
          {frequencies.map((freq) => (
            <Tab
              key={freq}
              text={freq}
              selected={selectedFrequency === freq}
              setSelected={setSelectedFrequency}
              discount={freq === "yearly"}
            />
          ))}
        </div>
      </div>

      <div className="grid w-full max-w-6xl gap-6 sm:grid-cols-2 xl:grid-cols-4">
        {tiers.map((tier) => (
          <PricingCard
            key={tier.name}
            tier={tier}
            paymentFrequency={selectedFrequency}
            onContactUs={() => setShowCalendar(true)}
            showButtons={false}
          />
        ))}
      </div>

      {/* Single CTA Button for All Plans */}
      <div className="flex flex-col items-center gap-4 mt-8">
        <Link to="/signup">
          <button className="relative group bg-aha-red hover:bg-aha-darkred text-white font-bold py-4 px-10 rounded-full text-lg transition-all duration-300 overflow-hidden">
            <span className="relative z-10 flex items-center gap-2">
              Get Started, it's FREE!
              <svg className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </span>
            <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300 rounded-full"></span>
            {/* Glowing effect */}
            <div className="absolute inset-0 rounded-full bg-gradient-to-r from-aha-red to-aha-darkred opacity-75 blur-lg scale-110 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
          </button>
        </Link>
        <p className="text-white/90 text-sm font-medium">No credit card required</p>
      </div>

      {/* Pay-As-You-Go Features Note */}
      <div className="max-w-4xl mx-auto">
        <div className="glass-card rounded-xl p-6 border border-white/10 bg-gradient-to-r from-yellow-500/5 to-orange-500/5">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 w-5 h-5 rounded-full bg-yellow-500/20 flex items-center justify-center mt-0.5">
              <span className="text-yellow-400 text-xs">!</span>
            </div>
            <div className="flex-1">
              <p className="text-white/90 text-sm font-gotham leading-relaxed">
                <span className="font-medium">*Additional Usage Fees:</span> SMS, calls, AI credits, premium workflow triggers, and custom email services are billed separately based on usage. These features use third-party providers and operate on a pay-as-you-go credit system.{' '}
                <Link
                  to="/terms#usage-fees"
                  className="text-aha-red hover:text-aha-red/80 underline transition-colors font-medium"
                >
                  Learn more about usage fees
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Calendar Popup */}
      <CalendarPopup
        isOpen={showCalendar}
        onClose={() => setShowCalendar(false)}
      />
    </section>
  )
}
