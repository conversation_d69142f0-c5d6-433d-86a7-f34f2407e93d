import React, { useState, ReactNode, createContext, useContext, useEffect } from 'react';
import { motion, AnimatePresence, useAnimation } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { OnboardingButton } from '@/components/ui/onboarding-button';
import { cn } from '@/lib/utils';
import { ChevronRight, ChevronLeft, Check } from 'lucide-react';

// Create a context for the wizard
interface WizardContextType {
  formData: any;
  updateFormData: (newData: any) => void;
  nextStep: () => void;
  isLastStep: boolean;
  wizardContext?: any; // Additional context data
  isTyping: boolean;
  setIsTyping: (isTyping: boolean) => void;
}

const WizardContext = createContext<WizardContextType | undefined>(undefined);

// Custom hook to use the wizard context
export const useWizard = () => {
  const context = useContext(WizardContext);
  if (!context) {
    throw new Error('useWizard must be used within a StepWizard');
  }
  return context;
};

export interface StepProps {
  title: string;
  description?: string;
  children: ReactNode;
  isOptional?: boolean;
}

export const Step: React.FC<StepProps> = ({ children }) => {
  return <>{children}</>;
};

interface StepWizardProps {
  children: ReactNode;
  onComplete: (data: any) => void;
  onCancel?: () => void;
  initialData?: any;
  className?: string;
  context?: any; // Additional context data
  showWelcomeAnimation?: boolean;
}

// Animation variants for step transitions
const variants = {
  enter: (direction: number) => ({
    x: direction > 0 ? 100 : -100,
    opacity: 0,
  }),
  center: {
    x: 0,
    opacity: 1,
  },
  exit: (direction: number) => ({
    x: direction < 0 ? 100 : -100,
    opacity: 0,
  }),
};

// Welcome animation component
const WelcomeAnimation = ({ onComplete }: { onComplete: () => void }) => {
  const controls = useAnimation();

  useEffect(() => {
    const sequence = async () => {
      await controls.start({ opacity: 1, y: 0, transition: { duration: 0.8 } });
      await new Promise(resolve => setTimeout(resolve, 1500));
      await controls.start({ opacity: 0, transition: { duration: 0.5 } });
      onComplete();
    };

    sequence();
  }, [controls, onComplete]);

  return (
    <motion.div
      className="absolute inset-0 flex flex-col items-center justify-center z-50 bg-gradient-to-br from-gray-900 to-black"
      initial={{ opacity: 0, y: 20 }}
      animate={controls}
    >
      <motion.div
        className="text-center space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.5 }}
      >
        <motion.img
          src="/Aha-logomodern.png"
          alt="AHA Innovations"
          className="h-16 mx-auto mb-6"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5, type: "spring" }}
        />
        <motion.h2
          className="text-3xl font-bold text-white"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.5 }}
        >
          Get ready to automate
        </motion.h2>
        <motion.p
          className="text-xl text-gray-300"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          and focus on what matters
        </motion.p>
      </motion.div>
    </motion.div>
  );
};

const StepWizard: React.FC<StepWizardProps> = ({
  children,
  onComplete,
  onCancel,
  initialData = {},
  className,
  context = {},
  showWelcomeAnimation = true,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState(initialData);
  const [direction, setDirection] = useState(0); // -1 for backward, 1 for forward
  const [showingWelcome, setShowingWelcome] = useState(showWelcomeAnimation);
  const [isTyping, setIsTyping] = useState(false);
  const [bgColor, setBgColor] = useState("from-gray-900 to-gray-800");

  // Effect to change background color when typing
  useEffect(() => {
    if (isTyping) {
      // Notify parent component about typing state
      document.body.classList.add('is-typing');
      setBgColor("from-gray-900 via-gray-800 to-gray-900");
    } else {
      document.body.classList.remove('is-typing');
      setBgColor("from-gray-900 to-gray-800");
    }

    // Cleanup function to remove class when component unmounts
    return () => {
      document.body.classList.remove('is-typing');
    };
  }, [isTyping]);

  // Get all steps from children
  const steps = React.Children.toArray(children).filter(
    (child) => React.isValidElement(child) && child.type === Step
  ) as React.ReactElement<StepProps>[];

  const currentStepElement = steps[currentStep];
  const isLastStep = currentStep === steps.length - 1;
  const isFirstStep = currentStep === 0;

  // Update form data
  const updateFormData = (newData: any) => {
    setFormData((prevData) => ({ ...prevData, ...newData }));
  };

  // Go to next step
  const handleNext = () => {
    if (isLastStep) {
      onComplete(formData);
    } else {
      setDirection(1);
      setCurrentStep((prev) => prev + 1);
    }
  };

  // Go to previous step
  const handlePrevious = () => {
    if (isFirstStep) {
      onCancel?.();
    } else {
      setDirection(-1);
      setCurrentStep((prev) => prev - 1);
    }
  };

  // Create context value
  const contextValue: WizardContextType = {
    formData,
    updateFormData,
    nextStep: handleNext,
    isLastStep,
    wizardContext: context,
    isTyping,
    setIsTyping,
  };

  if (showingWelcome) {
    return <WelcomeAnimation onComplete={() => setShowingWelcome(false)} />;
  }

  return (
    <WizardContext.Provider value={contextValue}>
      <motion.div
        className={cn(
          "w-full max-w-md mx-auto relative overflow-hidden",
          className
        )}
        animate={{
          background: isTyping
            ? "linear-gradient(135deg, #1a1a1a, #2a2a2a, #1a1a1a)"
            : "linear-gradient(135deg, #1a1a1a, #2a2a2a)"
        }}
        transition={{ duration: 0.5 }}
      >
        {/* Progress indicator */}
        <div className="flex justify-center mb-6">
          <div className="relative w-full max-w-xs">
            {/* Progress bar background with gradient */}
            <div className="h-2 bg-gray-800 w-full absolute top-0 left-0 right-0 rounded-full overflow-hidden"></div>

            {/* Animated progress bar */}
            <motion.div
              className="h-2 bg-gradient-to-r from-aha-red via-blue-500 to-aha-red absolute top-0 left-0 rounded-full"
              style={{
                backgroundSize: "200% 100%",
              }}
              initial={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
              animate={{
                width: `${(currentStep / (steps.length - 1)) * 100}%`,
                backgroundPosition: ["0% 0%", "100% 0%"],
              }}
              transition={{
                width: { duration: 0.4, ease: "easeInOut" },
                backgroundPosition: { duration: 3, repeat: Infinity, repeatType: "reverse" }
              }}
            ></motion.div>

            {/* Step indicator */}
            <div className="text-center mt-4">
              <motion.span
                className="text-sm text-gray-400"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                key={currentStep}
              >
                {currentStep + 1} <span className="mx-1">•</span> {steps.length}
              </motion.span>
            </div>
          </div>
        </div>

        {/* Step content with animations */}
        <div className="relative overflow-visible min-h-[350px] w-full">
          <AnimatePresence custom={direction} initial={false}>
            <motion.div
              key={currentStep}
              custom={direction}
              variants={variants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 },
              }}
              className="w-full absolute left-0 right-0"
              style={{ minHeight: '350px' }}
            >
              {/* Clone the current step and pass necessary props */}
              {React.cloneElement(currentStepElement, {
                formData,
                updateFormData,
                onNext: handleNext,
                isLastStep,
              })}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Navigation buttons */}
        <motion.div
          className="mt-6"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          {isFirstStep ? (
            <div className="flex justify-center">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <OnboardingButton
                  type="button"
                  onClick={handleNext}
                  className="bg-gradient-to-r from-aha-red to-aha-darkred hover:from-aha-darkred hover:to-aha-red text-white shadow-lg"
                >
                  Continue
                </OnboardingButton>
              </motion.div>
            </div>
          ) : isLastStep ? (
            <div className="flex justify-center">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <OnboardingButton
                  type="button"
                  onClick={handleNext}
                  className="bg-gradient-to-r from-aha-red to-aha-darkred hover:from-aha-darkred hover:to-aha-red text-white shadow-lg"
                >
                  Submit
                </OnboardingButton>
              </motion.div>
            </div>
          ) : (
            <div className="flex justify-center">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <OnboardingButton
                  type="button"
                  onClick={handleNext}
                  className="bg-gradient-to-r from-aha-red to-aha-darkred hover:from-aha-darkred hover:to-aha-red text-white shadow-lg"
                >
                  Continue
                </OnboardingButton>
              </motion.div>
            </div>
          )}
        </motion.div>
      </motion.div>
    </WizardContext.Provider>
  );
};

export { StepWizard };
