// This template requires the Embla Auto Scroll plugin to be installed:
// npm install embla-carousel-auto-scroll

"use client";

import AutoScroll from "embla-carousel-auto-scroll";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";

interface Logo {
  id: string;
  description: string;
  image: string;
  className?: string;
}

interface Logos3Props {
  heading?: string;
  logos?: Logo[];
  className?: string;
}

const Logos3 = ({
  heading = "Trusted by hundreds of companies worldwide",
  logos = [
    {
      id: "logo-1",
      description: "Company Logo 1",
      image: "/Company logos (1).png",
      className: "h-16 w-auto md:h-20",
    },
    {
      id: "logo-2",
      description: "Company Logo 2",
      image: "/Company logos (2).png",
      className: "h-16 w-auto md:h-20",
    },
    {
      id: "logo-3",
      description: "Company Logo 3",
      image: "/Company logos (3).png",
      className: "h-16 w-auto md:h-20",
    },
    {
      id: "logo-4",
      description: "Company Logo 4",
      image: "/Company logos (4).png",
      className: "h-16 w-auto md:h-20",
    },
  ],
}: Logos3Props) => {
  // Create multiple copies of logos for smooth infinite scrolling
  const extendedLogos = [
    ...logos,
    ...logos.map(logo => ({ ...logo, id: `${logo.id}-copy1` })),
    ...logos.map(logo => ({ ...logo, id: `${logo.id}-copy2` })),
    ...logos.map(logo => ({ ...logo, id: `${logo.id}-copy3` })),
    ...logos.map(logo => ({ ...logo, id: `${logo.id}-copy4` })),
    ...logos.map(logo => ({ ...logo, id: `${logo.id}-copy5` })),
  ];
  return (
    <section className="py-16 bg-gradient-to-b from-black via-gray-900 to-black">
      <div className="container flex flex-col items-center text-center">
        <h2 className="my-6 text-pretty text-xl font-bold lg:text-2xl text-white/90">
          {heading}
        </h2>
      </div>
      <div className="pt-10 md:pt-16 lg:pt-20">
        <div className="relative mx-auto flex items-center justify-center lg:max-w-5xl">
          <Carousel
            opts={{ loop: true, align: "start" }}
            plugins={[AutoScroll({ playOnInit: true, speed: 0.5, stopOnInteraction: false })]}
          >
            <CarouselContent className="ml-0">
              {extendedLogos.map((logo) => (
                <CarouselItem
                  key={logo.id}
                  className="flex basis-1/3 justify-center pl-0 sm:basis-1/4 md:basis-1/5 lg:basis-1/6"
                >
                  <div className="mx-8 flex shrink-0 items-center justify-center">
                    <img
                      src={logo.image}
                      alt={logo.description}
                      className={`${logo.className} grayscale opacity-60 hover:grayscale-0 hover:opacity-100 transition-all duration-300 filter brightness-0 invert`}
                    />
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
          <div className="absolute inset-y-0 left-0 w-12 bg-gradient-to-r from-black via-black/80 to-transparent"></div>
          <div className="absolute inset-y-0 right-0 w-12 bg-gradient-to-l from-black via-black/80 to-transparent"></div>
        </div>
      </div>
    </section>
  );
};

export { Logos3 };
