"use client"

import * as React from "react"
import { X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface CalendarPopupProps {
  isOpen: boolean
  onClose: () => void
  calendarUrl?: string
}

export function CalendarPopup({
  isOpen,
  onClose,
  calendarUrl = "https://calendar.aha-innovations.com/widget/bookings/aha-discovery-callbs3mf4"
}: CalendarPopupProps) {
  const [isLoading, setIsLoading] = React.useState(true)

  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  // Inject custom styles into the iframe when it loads
  const handleIframeLoad = () => {
    setIsLoading(false)

    try {
      const iframe = document.querySelector('iframe[title="Schedule Consultation"]') as HTMLIFrameElement
      if (iframe && iframe.contentDocument) {
        const iframeDoc = iframe.contentDocument
        const link = iframeDoc.createElement('link')
        link.rel = 'stylesheet'
        link.href = '/ghl-calendar-styles.css'
        iframeDoc.head.appendChild(link)
      }
    } catch (error) {
      // Cross-origin restrictions may prevent this, which is expected
      console.log('Could not inject styles due to cross-origin restrictions')
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/80 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative z-10 w-full max-w-4xl max-h-[90vh] bg-white rounded-lg shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-gray-50">
          <h2 className="text-xl font-semibold text-gray-900 font-gotham">
            Schedule a Consultation
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Calendar Content */}
        <div className="relative h-[600px]">
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
              <div className="flex flex-col items-center gap-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-aha-red"></div>
                <p className="text-gray-600 font-gotham">Loading calendar...</p>
              </div>
            </div>
          )}
          
          <iframe
            src={calendarUrl}
            width="100%"
            height="100%"
            className={cn(
              "border-none",
              isLoading ? "opacity-0" : "opacity-100"
            )}
            onLoad={handleIframeLoad}
            title="Schedule Consultation"
          />
        </div>
      </div>
    </div>
  )
}
