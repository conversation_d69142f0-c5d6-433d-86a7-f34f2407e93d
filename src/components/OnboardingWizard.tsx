import React, { useState, useEffect } from 'react';
import { StepWizard, Step, useWizard } from '@/components/ui/step-wizard';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { createGHLSubaccountAndUser } from '@/utils/ghlClient';
import { useToast } from '@/hooks/use-toast';
import { createClient } from '@supabase/supabase-js';
import { Building2, User, MapPin, Check } from 'lucide-react';
import { fetchUserProfileForOnboarding, OnboardingFormData } from '@/utils/autofillOnboarding';

// Import Supabase client from the centralized client
import { supabase } from '@/utils/supabaseClient';

interface OnboardingWizardProps {
  user: any;
  onSuccess: (locationId: string) => void;
  onCancel: () => void;
}

// Step 1: Company Information
const CompanyStep = () => {
  const { formData, updateFormData } = useWizard();

  return (
    <div className="space-y-4">
      <div className="flex justify-center mb-4">
        <div className="w-16 h-16 rounded-full bg-aha-red/20 flex items-center justify-center">
          <Building2 size={32} className="text-aha-red" />
        </div>
      </div>

      <h3 className="text-xl font-bold text-center mb-2">Tell us about your business</h3>
      <p className="text-gray-400 text-center mb-6">
        We'll use this information to set up your account
      </p>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="companyName">Company/Agency Name</Label>
          <Input
            id="companyName"
            value={formData.companyName || ''}
            onChange={(e) => updateFormData({ companyName: e.target.value })}
            className="bg-aha-dark border-gray-700 text-white"
            placeholder="Your company name"
            required
          />
        </div>
      </div>
    </div>
  );
};

// Step 2: Personal Information
const PersonalStep = () => {
  const { formData, updateFormData } = useWizard();

  return (
    <div className="space-y-4">
      <div className="flex justify-center mb-4">
        <div className="w-16 h-16 rounded-full bg-aha-red/20 flex items-center justify-center">
          <User size={32} className="text-aha-red" />
        </div>
      </div>

      <h3 className="text-xl font-bold text-center mb-2">Your personal details</h3>
      <p className="text-gray-400 text-center mb-6">
        Tell us a bit about yourself
      </p>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName">First Name</Label>
          <Input
            id="firstName"
            value={formData.firstName || ''}
            onChange={(e) => updateFormData({ firstName: e.target.value })}
            className="bg-aha-dark border-gray-700 text-white"
            placeholder="First name"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="lastName">Last Name</Label>
          <Input
            id="lastName"
            value={formData.lastName || ''}
            onChange={(e) => updateFormData({ lastName: e.target.value })}
            className="bg-aha-dark border-gray-700 text-white"
            placeholder="Last name"
            required
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="phone">Phone Number (Optional)</Label>
        <Input
          id="phone"
          type="tel"
          value={formData.phone || ''}
          onChange={(e) => updateFormData({ phone: e.target.value })}
          className="bg-aha-dark border-gray-700 text-white"
          placeholder="Your phone number"
        />
      </div>
    </div>
  );
};

// Step 3: Location Information (Optional)
const LocationStep = () => {
  const { formData, updateFormData } = useWizard();

  return (
    <div className="space-y-4">
      <div className="flex justify-center mb-4">
        <div className="w-16 h-16 rounded-full bg-aha-red/20 flex items-center justify-center">
          <MapPin size={32} className="text-aha-red" />
        </div>
      </div>

      <h3 className="text-xl font-bold text-center mb-2">Where are you located?</h3>
      <p className="text-gray-400 text-center mb-6">
        This information helps us provide better service
      </p>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="city">City (Optional)</Label>
          <Input
            id="city"
            value={formData.city || ''}
            onChange={(e) => updateFormData({ city: e.target.value })}
            className="bg-aha-dark border-gray-700 text-white"
            placeholder="Your city"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="state">State/Province (Optional)</Label>
          <Input
            id="state"
            value={formData.state || ''}
            onChange={(e) => updateFormData({ state: e.target.value })}
            className="bg-aha-dark border-gray-700 text-white"
            placeholder="Your state"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="country">Country (Optional)</Label>
        <Input
          id="country"
          value={formData.country || ''}
          onChange={(e) => updateFormData({ country: e.target.value })}
          className="bg-aha-dark border-gray-700 text-white"
          placeholder="Your country"
        />
      </div>
    </div>
  );
};

// Step 4: Review & Confirm
const ReviewStep = () => {
  const { formData } = useWizard();

  return (
    <div className="space-y-4">
      <div className="flex justify-center mb-4">
        <div className="w-16 h-16 rounded-full bg-green-600/20 flex items-center justify-center">
          <Check size={32} className="text-green-500" />
        </div>
      </div>

      <h3 className="text-xl font-bold text-center mb-2">Almost there!</h3>
      <p className="text-gray-400 text-center mb-6">
        Review your information and complete setup
      </p>

      <div className="space-y-4 bg-aha-dark/50 p-4 rounded-lg border border-gray-800">
        <div className="grid grid-cols-2 gap-2">
          <div className="text-gray-400">Company:</div>
          <div className="text-white font-medium">{formData.companyName || 'Not provided'}</div>

          <div className="text-gray-400">Name:</div>
          <div className="text-white font-medium">
            {formData.firstName} {formData.lastName}
          </div>

          <div className="text-gray-400">Phone:</div>
          <div className="text-white font-medium">{formData.phone || 'Not provided'}</div>

          <div className="text-gray-400">Location:</div>
          <div className="text-white font-medium">
            {[formData.city, formData.state, formData.country]
              .filter(Boolean)
              .join(', ') || 'Not provided'}
          </div>
        </div>
      </div>

      <p className="text-sm text-gray-400 text-center mt-4">
        By completing setup, you agree to our Terms of Service and Privacy Policy
      </p>
    </div>
  );
};

const OnboardingWizard: React.FC<OnboardingWizardProps> = ({ user, onSuccess, onCancel }) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [initialData, setInitialData] = useState<OnboardingFormData | null>(null);
  const [isLoadingData, setIsLoadingData] = useState(true);

  // Fetch user profile data for autofilling the form
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoadingData(true);

        // Fetch user profile data from the edge function
        const profileData = await fetchUserProfileForOnboarding(user?.id, user?.email);

        console.log('OnboardingWizard: Fetched user profile data:', profileData);

        // Set the initial form data
        setInitialData(profileData);
      } catch (error) {
        console.error('Error fetching user profile data:', error);

        // Fallback to basic data if fetch fails
        const fallbackData: OnboardingFormData = {
          companyName: user?.user_metadata?.full_name ? `${user.user_metadata.full_name}'s Agency` : '',
          firstName: user?.user_metadata?.first_name || user?.user_metadata?.name?.split(' ')[0] || '',
          lastName: user?.user_metadata?.last_name || user?.user_metadata?.name?.split(' ').slice(1).join(' ') || '',
          country: 'United States',
        };

        setInitialData(fallbackData);
      } finally {
        setIsLoadingData(false);
      }
    };

    fetchUserData();
  }, [user?.id, user?.email]);

  const handleComplete = async (formData: any) => {
    if (!formData.companyName || !formData.firstName || !formData.lastName) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);

      toast({
        title: "Setting Up Account",
        description: "Setting up your account. This may take a moment...",
      });

      // Create the GHL account with all the collected information
      const result = await createGHLSubaccountAndUser({
        email: user.email,
        firstName: formData.firstName,
        lastName: formData.lastName,
        companyName: formData.companyName,
        // Additional information
        city: formData.city || '',
        state: formData.state || '',
        country: formData.country || '',
        phone: formData.phone || '',
      });

      if (result?.locationId) {
        // Update user profile with the new GHL location ID
        await supabase
          .from('profiles')
          .upsert({
            id: user.id,
            ghl_account_id: result.locationId,
            ghl_location_id: result.locationId,
            updated_at: new Date().toISOString(),
            // Store additional profile information
            company_name: formData.companyName,
            first_name: formData.firstName,
            last_name: formData.lastName,
            city: formData.city || '',
            state: formData.state || '',
            country: formData.country || '',
            phone: formData.phone || '',
          });

        toast({
          title: "Success!",
          description: "Your account has been set up successfully!",
        });

        // Call the success callback with the location ID
        onSuccess(result.locationId);
      } else {
        throw new Error("Failed to set up your account. Please try again.");
      }
    } catch (error: any) {
      console.error('Error creating account:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to set up your account. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto bg-aha-darkpurple/80 border-gray-800 text-white">
      <CardHeader>
        <CardTitle>Complete Your Account Setup</CardTitle>
        <CardDescription className="text-gray-400">
          Let's get your account set up in a few simple steps
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoadingData ? (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-aha-red"></div>
            <p className="mt-4 text-gray-400">Loading your information...</p>
          </div>
        ) : (
          <StepWizard
            onComplete={handleComplete}
            onCancel={onCancel}
            initialData={initialData || {}}
          >
            <Step title="Business">
              <CompanyStep />
            </Step>
            <Step title="Personal">
              <PersonalStep />
            </Step>
            <Step title="Location" isOptional={true}>
              <LocationStep />
            </Step>
            <Step title="Review">
              <ReviewStep />
            </Step>
          </StepWizard>
        )}
      </CardContent>
    </Card>
  );
};

export default OnboardingWizard;
