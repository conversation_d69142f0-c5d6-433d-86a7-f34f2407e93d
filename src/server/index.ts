import { handleTestGHLIntegration } from './api';

// Import our new API handlers
import testGHLFormHandler from '../pages/api/test-ghl-form';
import getGHLLocationsHandler from '../pages/api/get-ghl-locations';

// Helper function to convert Node.js style handler to Request/Response
async function convertNodeHandler(handler: any, request: Request) {
  const url = new URL(request.url);

  // Create mock req/res objects with proper environment access
  const req = {
    method: request.method,
    url: request.url,
    headers: Object.fromEntries(request.headers.entries()),
    body: request.method !== 'GET' ? await request.json().catch(() => ({})) : {},
    query: Object.fromEntries(url.searchParams.entries()),
    // Ensure server-side environment variables are accessible
    env: process.env,
  };

  let responseData: any = null;
  let statusCode = 200;
  let headers: Record<string, string> = {};

  const res = {
    status: (code: number) => {
      statusCode = code;
      return res;
    },
    json: (data: any) => {
      responseData = data;
      headers['Content-Type'] = 'application/json';
      return res;
    },
    setHeader: (name: string, value: string) => {
      headers[name] = value;
    },
    end: () => {},
  };

  await handler(req, res);

  return new Response(
    responseData ? JSON.stringify(responseData) : '',
    {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
        ...headers,
      },
    }
  );
}

// Simple server-side request handler
export async function handleRequest(request: Request) {
  const url = new URL(request.url);
  const path = url.pathname;

  // Handle CORS preflight requests
  if (request.method === 'OPTIONS') {
    return new Response('', {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  }

  // Handle API routes
  if (path === '/api/test-ghl-integration' && request.method === 'POST') {
    return handleTestGHLIntegration(request);
  }

  if (path === '/api/test-ghl-form' && (request.method === 'POST' || request.method === 'GET')) {
    return convertNodeHandler(testGHLFormHandler, request);
  }

  if (path === '/api/get-ghl-locations' && request.method === 'GET') {
    return convertNodeHandler(getGHLLocationsHandler, request);
  }

  // Return 404 for unknown routes
  return new Response('Not Found', { status: 404 });
}
