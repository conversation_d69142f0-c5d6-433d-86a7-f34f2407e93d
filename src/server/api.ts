import { createGHLSubaccountAndUser } from '../utils/ghlPrivateIntegration';

// Simple API handler for testing GHL integration
export async function handleTestGHLIntegration(req: Request) {
  try {
    const body = await req.json();
    const {
      email,
      firstName,
      lastName,
      companyName,
      phone,
      city,
      state,
      country,
    } = body;

    // Validate required fields
    if (!email || !firstName || !lastName) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }

    // Create GHL account using Private Integration token
    const result = await createGHLSubaccountAndUser({
      email,
      firstName,
      lastName,
      companyName: companyName || `${firstName}'s Agency`,
      phone,
      city,
      state,
      country,
    });

    // Return the result
    return new Response(JSON.stringify({
      success: true,
      data: result,
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error: any) {
    console.error('Error testing GHL integration:', error);
    return new Response(JSON.stringify({
      error: 'Failed to create GHL account',
      message: error.message,
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}
