import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { ThemeProvider } from "next-themes";
// import Index from "./pages/Index";
import SimpleIndex from "./pages/SimpleIndex";
// Using relative import path
import ModernIndex from "./pages/ModernIndex";

import SignUp from "./pages/SignUp";
import EmbeddedSignUp from "./pages/EmbeddedSignUp";
import SimpleSignUp from "./pages/SimpleSignUp";
import TestPortfolioForm from "./pages/TestPortfolioForm";
import ForgotPassword from "./pages/ForgotPassword";
import Dashboard from "./pages/Dashboard";
import NotFound from "./pages/NotFound";
import GHLAuthCallback from "./components/GHLAuthCallback";
import AuthCallback from "./components/AuthCallback";
import GHLInitiate from "./pages/GHLInitiate";
import Onboarding from "./pages/Onboarding";
import TestSocialLogin from "./pages/TestSocialLogin";
import TestSaasSubscription from "./pages/TestSaasSubscription";
import SSOLogin from "./pages/SSOLogin";
import Pricing from "./pages/Pricing";
import PlanSelection from "./pages/PlanSelection";
import Contact from "./pages/Contact";
import Features from "./pages/Features";
import Showcase from "./pages/Showcase";
import SetupGHLToken from "./pages/setup-ghl-token";
import About from "./pages/About";
import Terms from "./pages/Terms";
import Privacy from "./pages/Privacy";
import Cookies from "./pages/Cookies";
import GDPR from "./pages/GDPR";
import Security from "./pages/Security";
import ThankYou from "./pages/ThankYou";
import FeatureDemo from "./pages/FeatureDemo";
import HeroDemo from "./pages/HeroDemo";
import PricingDemo from "./pages/PricingDemo";
import PricingTest from "./pages/PricingTest";
import DotPatternDemo from "./pages/DotPatternDemo";
import FlipWordsTest from "./pages/FlipWordsTest";
import LiquidButtonDemo from "./pages/LiquidButtonDemo";
import TextEffectDemo from "./pages/TextEffectDemo";
import FontDemo from "./pages/FontDemo";
import AvatarCirclesDemo from "./pages/AvatarCirclesDemo";
import AnimatedBeamDemo from "./pages/AnimatedBeamDemo";
import VideoPlayerDemo from "./pages/VideoPlayerDemo";
import ScrollToTop from "./ScrollToTop";

// Import CSS
import "./App.css";

const queryClient = new QueryClient();

// Main application URL
const MAIN_APP_URL = '/';

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider attribute="class" defaultTheme="dark">
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <ScrollToTop />
          <Routes>
            <Route path="/" element={<ModernIndex />} />
            <Route path="/classic" element={<SimpleIndex />} />
            <Route path="/pricing" element={<Pricing />} />
            <Route path="/plan-selection" element={<PlanSelection />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/features" element={<Features />} />
            <Route path="/showcase" element={<Showcase />} />
            <Route path="/feature-demo" element={<FeatureDemo />} />
            <Route path="/hero-demo" element={<HeroDemo />} />
            <Route path="/pricing-demo" element={<PricingDemo />} />
            <Route path="/pricing-test" element={<PricingTest />} />
            <Route path="/dot-pattern-demo" element={<DotPatternDemo />} />
            <Route path="/flip-words-test" element={<FlipWordsTest />} />
            <Route path="/liquid-button-demo" element={<LiquidButtonDemo />} />
            <Route path="/text-effect-demo" element={<TextEffectDemo />} />
            <Route path="/font-demo" element={<FontDemo />} />
            <Route path="/avatar-circles-demo" element={<AvatarCirclesDemo />} />
            <Route path="/animated-beam-demo" element={<AnimatedBeamDemo />} />
            <Route path="/video-player-demo" element={<VideoPlayerDemo />} />

            <Route path="/signup" element={<SignUp />} />
            <Route path="/embedded-signup" element={<EmbeddedSignUp />} />
            <Route path="/test-form" element={<TestPortfolioForm />} />
            <Route path="/test-social-login" element={<TestSocialLogin />} />
            <Route path="/test-saas-subscription" element={<TestSaasSubscription />} />
            <Route path="/sso-login" element={<SSOLogin />} />
            <Route path="/simple-signup" element={<SimpleSignUp />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/onboarding" element={<Onboarding />} />
            {/* The Dashboard page and OAuth callback handler */}
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/auth/callback" element={<AuthCallback />} />
            {/* GHL OAuth routes */}
            <Route path="/ghl-auth-callback" element={<GHLAuthCallback />} />
            <Route path="/initiate" element={<GHLInitiate />} />
            <Route path="/setup-ghl-token" element={<SetupGHLToken />} />
            {/* Legal and Company Pages */}
            <Route path="/about" element={<About />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/cookies" element={<Cookies />} />
            <Route path="/gdpr" element={<GDPR />} />
            <Route path="/security" element={<Security />} />
            {/* Thank You Page */}
            <Route path="/thank-you" element={<ThankYou />} />
            {/* Handle 404 errors */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
