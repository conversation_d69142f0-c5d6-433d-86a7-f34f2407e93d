import React, { useRef, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { DotPattern } from '@/components/ui/dot-pattern';
import { TextEffect } from '@/components/ui/text-effect';
import HeroHeader from '@/components/sections/HeroHeader';
import ModernFooter from '@/components/sections/ModernFooter';
import { ArrowRight, Check, Sparkles } from 'lucide-react';
import CompetitorLogos from '@/components/CompetitorLogos';
import { PricingSection } from '@/components/ui/pricing-section';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import '@/styles/pricing.css';

const Pricing = () => {
  const navigate = useNavigate();
  const heroRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  // Parallax effect for hero section - keep content visible
  const y = useTransform(scrollYProgress, [0, 1], [0, 100]);
  const opacity = useTransform(scrollYProgress, [0, 0.95], [1, 0.98]);

  // Add a CSS class to ensure content remains visible
  useEffect(() => {
    document.body.classList.add('pricing-page');
    return () => {
      document.body.classList.remove('pricing-page');
    };
  }, []);

  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden pricing-content" style={{ background: '#0F0F0F' }}>
      <HeroHeader />

      {/* Hero Section */}
      <section ref={heroRef} className="py-16 relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #0F0F0F 0%, #1a1a1a 100%)' }}>
        {/* Background elements */}
        <div className="absolute inset-0 z-0">
          {/* Dot pattern texture overlay */}
          <DotPattern
            width={30}
            height={30}
            cx={1}
            cy={1}
            cr={0.8}
            className="fill-white/[0.025] [mask-image:radial-gradient(1000px_circle_at_center,white,transparent)]"
          />
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          {/* Modern Pricing Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            className="mb-8"
          >
            {/* Force unified button approach - bypass any component caching */}
            <div className="relative flex justify-center items-center w-full">
              <PricingSection
                title="Affordable Pricing"
                subtitle="Choose the best plan for your needs"
                frequencies={["monthly", "yearly"]}
                tiers={[
                  {
                    id: "free",
                    name: "Free",
                    price: { monthly: "$0", yearly: "$0" },
                    description: "Perfect for getting started",
                    features: ["20 Contacts", "10 Users/Teammates", "Social Planner", "Email Support", "Basic Analytics"],
                    cta: "Get Started Free",
                  },
                  {
                    id: "basic",
                    name: "Basic Plan",
                    price: { monthly: 5, yearly: 4.58 },
                    description: "Perfect for freelancers",
                    features: ["15 Pipelines", "Unlimited Sales Funnel", "1,000 Email Marketing", "Unlimited Contacts", "Basic Automation"],
                    cta: "Start Free Trial",
                    stripeLinks: { monthly: "https://buy.stripe.com/bJe5kE7Za5Ea9XZ37U4801F", yearly: "https://buy.stripe.com/3cI14o7Za0jQgmndMy4801G" },
                  },
                  {
                    id: "agency",
                    name: "Agency Plan",
                    price: { monthly: 25, yearly: 23.75 },
                    description: "Ideal for small agencies",
                    features: ["50 Pipelines", "Unlimited Sales Funnel", "7,500 Email Marketing", "Workflow Triggers & Actions", "Advanced Analytics"],
                    cta: "Start Free Trial",
                    popular: true,
                    stripeLinks: { monthly: "https://buy.stripe.com/fZu3cw7Za0jQ6LN37U4801H", yearly: "https://buy.stripe.com/00wbJ26V6aYuc677oa4801L" },
                  },
                  {
                    id: "enterprise",
                    name: "Enterprise Plan",
                    price: { monthly: 5, yearly: 4.58 },
                    originalPrice: { monthly: 39, yearly: 37.50 },
                    description: "Recommended for business",
                    limitedOffer: "Limited offer, get Enterprise for the price of Basic!",
                    features: ["Unlimited Pipelines", "Unlimited Sales Funnel", "15,000 Email Marketing", "Reputation Management", "Priority Support"],
                    cta: "Get Started",
                    highlighted: true,
                    stripeLinks: { monthly: "https://buy.stripe.com/4gM3cwenyeaGfijbEq4801O", yearly: "https://buy.stripe.com/4gM5kEcfq5Ea4DF8se4801P" },
                  },
                ]}
              />
            </div>
          </motion.div>

          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center mb-8"
            >
              <TextEffect
                per="word"
                as="h2"
                className="text-2xl md:text-4xl font-bold text-aha-red font-gotham"
                variants={{
                  container: {
                    hidden: { opacity: 0 },
                    visible: {
                      opacity: 1,
                      transition: { staggerChildren: 0.05, delayChildren: 0.6 },
                    },
                    exit: {
                      transition: { staggerChildren: 0.03, staggerDirection: -1 },
                    },
                  },
                  item: {
                    hidden: {
                      opacity: 0,
                      x: -20,
                      filter: 'brightness(0%)',
                    },
                    visible: {
                      opacity: 1,
                      x: 0,
                      filter: 'brightness(100%)',
                      transition: {
                        duration: 0.5,
                        ease: "easeOut",
                      },
                    },
                    exit: {
                      opacity: 0,
                      x: 20,
                      filter: 'brightness(200%)',
                      transition: {
                        duration: 0.3,
                      },
                    },
                  },
                }}
              >
                Save Thousands: All-In-One Solution vs Individual Subscriptions
              </TextEffect>
            </motion.div>

            {/* Comparison Table */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="overflow-x-auto mb-16 glass-card rounded-xl p-1 pricing-table relative"
            >
              {/* Dot pattern for table background */}
              <DotPattern
                width={24}
                height={24}
                cx={0.8}
                cy={0.8}
                cr={0.4}
                className="fill-white/[0.015] [mask-image:radial-gradient(600px_circle_at_center,white,transparent)]"
              />
              <Table className="w-full">
                <TableHeader>
                  <TableRow className="bg-gradient-to-r from-gray-800/30 to-gray-800/40 backdrop-blur-sm">
                    <TableHead className="text-white font-bold text-lg py-4">Feature</TableHead>
                    <TableHead className="text-white text-center font-bold text-lg py-4">Competitor</TableHead>
                    <TableHead className="text-white text-center font-bold text-lg py-4">Price</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <ComparisonRow feature="CRM & Pipeline Management" competitor="HubSpot" price="$99/month" index={0} />
                  <ComparisonRow feature="Unlimited Sales Funnels" competitor="ClickFunnels" price="$297/month" index={1} />
                  <ComparisonRow feature="Surveys & Forms" competitor="JotForm" price="$29/month" index={2} />
                  <ComparisonRow feature="Email Marketing" competitor="Mailchimp" price="$49/month" index={3} />
                  <ComparisonRow feature="2-Way SMS Marketing" competitor="Twilio" price="$99/month" index={4} />
                  <ComparisonRow feature="Website Builder" competitor="Wix" price="$99/month" index={5} />
                  <ComparisonRow feature="Booking & Appointments" competitor="Calendly" price="$29/month" index={6} />
                  <ComparisonRow feature="Workflow Automations" competitor="Zapier" price="$169/month" index={7} />
                  <ComparisonRow feature="Courses/Products" competitor="Kajabi" price="$99/month" index={8} />
                </TableBody>
              </Table>
            </motion.div>

            {/* Pricing Comparison */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="glass-card rounded-xl p-8 mb-16 relative overflow-hidden glow-effect"
            >
              {/* Dot pattern for comparison card */}
              <DotPattern
                width={28}
                height={28}
                cx={1}
                cy={1}
                cr={0.6}
                className="fill-aha-red/[0.03] [mask-image:radial-gradient(500px_circle_at_center,white,transparent)]"
              />
              <div className="absolute inset-0 bg-gradient-to-r from-aha-red/10 to-aha-red/20 opacity-80 z-0"></div>
              <div className="relative z-10">
                <div className="flex flex-col md:flex-row items-center justify-between gap-8">
                  <motion.div
                    className="text-center md:text-left"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <div className="flex items-center mb-4">
                      <Sparkles className="text-yellow-300 mr-2" />
                      <h3 className="text-yellow-300 text-2xl font-bold">Aha-Innovations:</h3>
                    </div>
                    <div className="glass-card bg-black/20 p-6 rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300">
                      <motion.div
                        className="text-white text-4xl font-bold mb-2"
                        initial={{ scale: 0.9 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.5, delay: 0.8 }}
                      >
                        Only $23.75/month
                      </motion.div>
                      <div className="text-white/90">billed annually</div>
                      <div className="text-white/90 mt-2">or $25/month billed monthly</div>
                      <motion.div
                        className="mt-4 bg-gradient-to-r from-aha-red/80 to-aha-darkred/80 rounded-lg p-2 text-white text-sm"
                        whileHover={{ scale: 1.05 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                      >
                        Save over $6,950 per month!
                      </motion.div>
                    </div>
                  </motion.div>

                  <motion.div
                    className="text-center relative"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <div className="w-16 h-16 rounded-full bg-gradient-to-br from-white/10 to-white/5 flex items-center justify-center text-3xl font-bold shadow-lg">
                      VS
                    </div>
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 rounded-full bg-white/5 animate-pulse"></div>
                  </motion.div>

                  <motion.div
                    className="text-center md:text-right"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <h3 className="text-white text-2xl font-bold mb-4">Total Competitor Pricing:</h3>
                    <div className="glass-card bg-black/20 p-6 rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300">
                      <motion.div
                        className="text-white text-4xl font-bold"
                        initial={{ scale: 0.9 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.5, delay: 0.8 }}
                      >
                        $7,000+/month
                      </motion.div>
                      <div className="text-white/90">for all individual services</div>
                      <div className="flex flex-wrap justify-center md:justify-end gap-3 mt-4">
                        <motion.div whileHover={{ scale: 1.1 }} transition={{ type: "spring", stiffness: 400, damping: 10 }}>
                          <CompetitorLogos name="HubSpot" />
                        </motion.div>
                        <motion.div whileHover={{ scale: 1.1 }} transition={{ type: "spring", stiffness: 400, damping: 10 }}>
                          <CompetitorLogos name="ClickFunnels" />
                        </motion.div>
                        <motion.div whileHover={{ scale: 1.1 }} transition={{ type: "spring", stiffness: 400, damping: 10 }}>
                          <CompetitorLogos name="Mailchimp" />
                        </motion.div>
                        <motion.div whileHover={{ scale: 1.1 }} transition={{ type: "spring", stiffness: 400, damping: 10 }}>
                          <CompetitorLogos name="Zapier" />
                        </motion.div>
                      </div>
                    </div>
                  </motion.div>
                </div>
              </div>
            </motion.div>

            {/* 14-Day Trial CTA Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true, margin: "-50px" }}
              className="mt-20 mb-12 max-w-4xl mx-auto"
            >
              <div className="glass-card rounded-xl overflow-hidden border border-white/10 glow-effect relative">
                {/* Dot pattern for CTA section */}
                <DotPattern
                  width={26}
                  height={26}
                  cx={1}
                  cy={1}
                  cr={0.7}
                  className="fill-white/[0.02] [mask-image:radial-gradient(400px_circle_at_center,white,transparent)]"
                />
                <div className="bg-gradient-to-r from-gray-800/20 to-gray-900/20 p-10 text-center relative">
                  <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(234,56,76,0.08),transparent_70%)]"></div>
                  <div className="relative z-10">
                    <motion.div
                      initial={{ scale: 0.9 }}
                      whileInView={{ scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.1 }}
                      viewport={{ once: true }}
                    >
                      <TextEffect
                        per="word"
                        as="h2"
                        className="text-3xl md:text-4xl font-bold mb-4 font-gotham"
                        variants={{
                          container: {
                            hidden: { opacity: 0 },
                            visible: {
                              opacity: 1,
                              transition: { staggerChildren: 0.15, delayChildren: 0.8 },
                            },
                            exit: {
                              transition: { staggerChildren: 0.1, staggerDirection: 1 },
                            },
                          },
                          item: {
                            hidden: {
                              opacity: 0,
                              scale: 0.3,
                              y: 40,
                            },
                            visible: {
                              opacity: 1,
                              scale: 1,
                              y: 0,
                              transition: {
                                type: "spring",
                                damping: 8,
                                stiffness: 120,
                                duration: 1.2,
                              },
                            },
                            exit: {
                              opacity: 0,
                              scale: 1.5,
                              y: -40,
                              transition: {
                                duration: 0.7,
                                ease: "easeInOut",
                              },
                            },
                          },
                        }}
                      >
                        Try AHA-Innovations Free for 14 Days
                      </TextEffect>
                    </motion.div>
                    <motion.p
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      viewport={{ once: true }}
                      className="text-lg text-white/90 mb-8 max-w-2xl mx-auto"
                    >
                      Experience all features with no limitations. Start your 14-day free trial of the Agency Plan. Credit card required.
                      Upgrade to any plan when you're ready to scale your business.
                    </motion.p>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="inline-block"
                    >
                      <Button
                        onClick={() => window.open('https://buy.stripe.com/fZu3cw7Za0jQ6LN37U4801H', '_blank', 'noopener,noreferrer')}
                        size="lg"
                        className="bg-aha-red hover:bg-aha-darkred text-white text-lg px-8 py-6 group relative overflow-hidden"
                      >
                        <span className="relative z-10 flex items-center gap-2">
                          Start Your 14-Day Free Trial
                          <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                        </span>
                        <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
                      </Button>
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      <ModernFooter />
    </div>
  );
};

// Helper Components
const ComparisonRow = ({ feature, competitor, price, index }) => {
  return (
    <TableRow className="border-b border-white/10 hover:bg-white/5 transition-colors">
      <TableCell className="py-4 font-medium">
        <motion.div
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 * index }}
          className="flex items-center"
        >
          <Check className="text-aha-red mr-2 h-5 w-5" />
          {feature}
        </motion.div>
      </TableCell>
      <TableCell className="text-center py-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3, delay: 0.1 * index + 0.1 }}
          className="flex justify-center"
        >
          <CompetitorLogos name={competitor} />
        </motion.div>
      </TableCell>
      <TableCell className="text-center py-4 font-bold">
        <motion.div
          initial={{ opacity: 0, x: 10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 * index + 0.2 }}
        >
          {price}
        </motion.div>
      </TableCell>
    </TableRow>
  );
};

export default Pricing;
