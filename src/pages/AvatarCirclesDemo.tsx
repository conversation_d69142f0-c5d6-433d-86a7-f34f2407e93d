import React from 'react';
import { AvatarCircles } from "@/components/ui/avatar-circles";
import { Button } from "@/components/ui/button";
import { useNavigate } from 'react-router-dom';

// Using real Unsplash images for professional avatars
const avatarUrls = [
  "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
  "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
];

export default function AvatarCirclesDemo() {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <Button 
            onClick={() => navigate('/')} 
            variant="outline" 
            className="mb-4"
          >
            ← Back to Home
          </Button>
          <h1 className="text-4xl font-bold mb-4">Avatar Circles Demo</h1>
          <p className="text-gray-300">
            This component shows overlapping user avatars with a count indicator - perfect for social proof!
          </p>
        </div>

        <div className="space-y-12">
          {/* Basic Usage */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8">
            <h2 className="text-2xl font-bold mb-4">Basic Usage</h2>
            <p className="text-gray-300 mb-6">Default size with 4 avatars and 99+ users</p>
            <AvatarCircles numPeople={99} avatarUrls={avatarUrls} />
          </div>

          {/* Different Sizes */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8">
            <h2 className="text-2xl font-bold mb-4">Different Sizes</h2>
            <div className="space-y-6">
              <div>
                <p className="text-gray-300 mb-3">Small (32px)</p>
                <AvatarCircles 
                  numPeople={150} 
                  avatarUrls={avatarUrls} 
                  className="[&>img]:h-8 [&>img]:w-8 [&>a]:h-8 [&>a]:w-8 [&>a]:text-xs"
                />
              </div>
              <div>
                <p className="text-gray-300 mb-3">Default (40px)</p>
                <AvatarCircles numPeople={250} avatarUrls={avatarUrls} />
              </div>
              <div>
                <p className="text-gray-300 mb-3">Large (48px)</p>
                <AvatarCircles 
                  numPeople={500} 
                  avatarUrls={avatarUrls} 
                  className="[&>img]:h-12 [&>img]:w-12 [&>a]:h-12 [&>a]:w-12"
                />
              </div>
            </div>
          </div>

          {/* Social Proof Context */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8">
            <h2 className="text-2xl font-bold mb-4">Social Proof Example</h2>
            <div className="text-center">
              <h3 className="text-xl font-semibold mb-2">Join thousands of satisfied customers</h3>
              <div className="flex justify-center items-center gap-4 mb-4">
                <AvatarCircles numPeople={5000} avatarUrls={avatarUrls} />
                <div className="text-left">
                  <p className="text-sm text-gray-300">5,000+ businesses trust</p>
                  <p className="font-semibold text-aha-red">AHA-Innovations</p>
                </div>
              </div>
              <Button className="bg-aha-red hover:bg-aha-darkred">
                Get Started Free
              </Button>
            </div>
          </div>

          {/* Testimonial Integration */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8">
            <h2 className="text-2xl font-bold mb-4">Testimonial Integration</h2>
            <div className="bg-white/5 p-6 rounded-lg">
              <div className="flex items-start gap-4 mb-4">
                <AvatarCircles 
                  numPeople={12} 
                  avatarUrls={avatarUrls.slice(0, 3)} 
                  className="[&>img]:h-8 [&>img]:w-8 [&>a]:h-8 [&>a]:w-8 [&>a]:text-xs"
                />
                <div>
                  <p className="text-sm text-gray-300">12+ team members love this tool</p>
                </div>
              </div>
              <blockquote className="text-gray-300 italic">
                "AHA-Innovations has transformed how our team collaborates. The automation features save us hours every week!"
              </blockquote>
              <div className="mt-4 text-sm">
                <p className="font-semibold">Sarah Johnson</p>
                <p className="text-gray-400">Marketing Director, TechCorp</p>
              </div>
            </div>
          </div>

          {/* Usage Recommendations */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8">
            <h2 className="text-2xl font-bold mb-4">Perfect For:</h2>
            <ul className="space-y-2 text-gray-300">
              <li>• <strong>Hero sections</strong> - Show social proof with user count</li>
              <li>• <strong>Testimonials</strong> - Display team or customer avatars</li>
              <li>• <strong>Pricing pages</strong> - Show how many users are on each plan</li>
              <li>• <strong>Feature sections</strong> - Highlight user adoption</li>
              <li>• <strong>Footer</strong> - Community member count</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
