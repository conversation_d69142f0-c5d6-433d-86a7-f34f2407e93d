import React, { useState } from 'react';
import { supabase } from '../utils/supabaseClient';

const TestSaasSubscription = () => {
  const [locationId, setLocationId] = useState('');
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);

  // Testing with Agency Plan (has 30-day trial) instead of Free Plan (no trial)
  const planId = 'price_1RgQ6zL5UMPPQRhs3qb0X73a'; // Agency Plan monthly

  const handleTest = async () => {
    if (!locationId.trim()) {
      alert('Please enter a Location ID');
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      const { data, error } = await supabase.functions.invoke('test-ghl-saas-subscription', {
        body: {
          locationId: locationId.trim(),
          planId: planId, // Using the hardcoded plan ID
          email: email.trim() || undefined,
          firstName: firstName.trim() || undefined,
          lastName: lastName.trim() || undefined,
        }
      });

      if (error) {
        throw error;
      }

      setResult(data);
    } catch (error) {
      console.error('Test error:', error);
      setResult({
        error: true,
        message: error.message || 'Test failed'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-black/40 backdrop-blur-sm border border-white/10 text-white rounded-lg">
          <div className="p-6">
            <h1 className="text-2xl font-bold text-center mb-2">
              Test GHL SaaS Subscription Attachment
            </h1>
            <p className="text-gray-300 text-center mb-6">
              Test attaching a free SaaS subscription with 100% coupon to existing GHL accounts
            </p>
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Location ID (Required) *
                  </label>
                  <input
                    type="text"
                    value={locationId}
                    onChange={(e) => setLocationId(e.target.value)}
                    placeholder="Enter GHL Location ID"
                    className="w-full px-3 py-2 bg-black/20 border border-white/20 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                  />
                  <p className="text-xs text-gray-400 mt-1">
                    Testing with: Agency Plan (30-day trial) - price_1RgQ6zL5UMPPQRhs3qb0X73a
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Email (Optional)
                  </label>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 bg-black/20 border border-white/20 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">
                    First Name (Optional)
                  </label>
                  <input
                    type="text"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    placeholder="John"
                    className="w-full px-3 py-2 bg-black/20 border border-white/20 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Last Name (Optional)
                  </label>
                  <input
                    type="text"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    placeholder="Doe"
                    className="w-full px-3 py-2 bg-black/20 border border-white/20 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                  />
                </div>
              </div>

              <button
                onClick={handleTest}
                disabled={loading || !locationId.trim()}
                className="w-full px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-md font-medium transition-colors"
              >
                {loading ? 'Testing...' : 'Test SaaS Subscription Attachment'}
              </button>

              {result && (
                <div className="bg-black/20 border border-white/10 rounded-lg">
                  <div className="p-4">
                    <h3 className="text-lg font-semibold mb-4">
                      {result.error ? '❌ Test Failed' : '✅ Test Results'}
                    </h3>
                    <pre className="text-sm text-gray-300 whitespace-pre-wrap overflow-auto max-h-96 bg-black/30 p-4 rounded">
                      {JSON.stringify(result, null, 2)}
                    </pre>
                  </div>
                </div>
              )}

              <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
                <h3 className="font-semibold text-blue-300 mb-2">How to use:</h3>
                <ol className="text-sm text-blue-200 space-y-1">
                  <li>1. Enter a GHL Location ID from an existing subaccount</li>
                  <li>2. Optionally provide email and name for the Stripe customer</li>
                  <li>3. Click "Test SaaS Subscription Attachment" to run the test</li>
                  <li>4. Check the results to see if the subscription was attached successfully</li>
                </ol>
              </div>

              <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4">
                <h3 className="font-semibold text-yellow-300 mb-2">What this test does:</h3>
                <ul className="text-sm text-yellow-200 space-y-1">
                  <li>• Creates a 100% off Stripe coupon</li>
                  <li>• Creates a test Stripe customer</li>
                  <li>• Checks current subscription status for the location</li>
                  <li>• Attempts to attach Agency Plan with 30-day trial (should work without payment method)</li>
                  <li>• Uses the Update SaaS Subscription API endpoint</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestSaasSubscription;
