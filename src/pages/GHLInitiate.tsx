
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { generateGHLOAuthURL } from '@/utils/ghlClient';
import { useToast } from '@/hooks/use-toast';

const GHLInitiate = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    const initiateOAuth = async () => {
      try {
        setLoading(true);
        
        // Generate the OAuth URL and redirect to GHL
        const oauthURL = await generateGHLOAuthURL();
        window.location.href = oauthURL;
      } catch (err: any) {
        console.error('Error initiating GHL OAuth:', err);
        setError(err.message);
        
        toast({
          title: "Error",
          description: `Failed to initiate GHL connection: ${err.message}`,
          variant: "destructive",
        });
        
        // Redirect back to dashboard after short delay
        setTimeout(() => {
          navigate('/dashboard');
        }, 3000);
      } finally {
        setLoading(false);
      }
    };
    
    initiateOAuth();
  }, [navigate, toast]);
  
  if (loading) {
    return (
      <div className="min-h-screen bg-aha-dark flex items-center justify-center">
        <p className="text-white">Connecting to GoHighLevel... Please wait.</p>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="min-h-screen bg-aha-dark flex items-center justify-center flex-col">
        <p className="text-white mb-4">Error initiating GoHighLevel connection:</p>
        <p className="text-red-400">{error}</p>
        <p className="text-white mt-4">Redirecting to dashboard...</p>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-aha-dark flex items-center justify-center">
      <p className="text-white">Redirecting to GoHighLevel...</p>
    </div>
  );
};

export default GHLInitiate;
