import React from 'react';
import { DotPatternDemo } from '@/components/ui/dot-pattern-demo';
import { DotPattern } from '@/components/ui/dot-pattern';
import { cn } from '@/lib/utils';

const DotPatternDemoPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-aha-dark p-8">
      <div className="max-w-6xl mx-auto space-y-12">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">Dot Pattern Texture Demo</h1>
          <p className="text-gray-300 text-lg">
            Showcasing different dot pattern configurations for adding texture to sections
          </p>
        </div>

        {/* Basic Demo */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold text-white">Basic Dot Pattern</h2>
          <DotPatternDemo />
        </div>

        {/* Hero Section Style */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold text-white">Hero Section Style</h2>
          <div className="relative flex h-[400px] w-full flex-col items-center justify-center overflow-hidden rounded-lg bg-black border border-white/10">
            <div className="z-10 text-center space-y-4">
              <h3 className="text-3xl font-bold text-white">Your Business Success</h3>
              <p className="text-gray-300 max-w-md">
                Experience the power of subtle texture patterns that enhance your design without overwhelming the content.
              </p>
            </div>
            <DotPattern
              width={32}
              height={32}
              cx={1}
              cy={1}
              cr={1}
              className="fill-white/[0.02] [mask-image:radial-gradient(800px_circle_at_center,white,transparent)]"
            />
          </div>
        </div>

        {/* Different Variations */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold text-white">Pattern Variations</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Fine Pattern */}
            <div className="relative flex h-[300px] w-full flex-col items-center justify-center overflow-hidden rounded-lg bg-aha-dark border border-white/10">
              <div className="z-10 text-center">
                <h4 className="text-xl font-semibold text-white mb-2">Fine Pattern</h4>
                <p className="text-gray-400 text-sm">Smaller, denser dots</p>
              </div>
              <DotPattern
                width={16}
                height={16}
                cx={0.5}
                cy={0.5}
                cr={0.3}
                className="fill-white/[0.04] [mask-image:radial-gradient(300px_circle_at_center,white,transparent)]"
              />
            </div>

            {/* Bold Pattern */}
            <div className="relative flex h-[300px] w-full flex-col items-center justify-center overflow-hidden rounded-lg bg-aha-dark border border-white/10">
              <div className="z-10 text-center">
                <h4 className="text-xl font-semibold text-white mb-2">Bold Pattern</h4>
                <p className="text-gray-400 text-sm">Larger, more visible dots</p>
              </div>
              <DotPattern
                width={40}
                height={40}
                cx={2}
                cy={2}
                cr={1.5}
                className="fill-white/[0.06] [mask-image:radial-gradient(400px_circle_at_center,white,transparent)]"
              />
            </div>

            {/* Colored Pattern */}
            <div className="relative flex h-[300px] w-full flex-col items-center justify-center overflow-hidden rounded-lg bg-black border border-aha-red/20">
              <div className="z-10 text-center">
                <h4 className="text-xl font-semibold text-white mb-2">Colored Pattern</h4>
                <p className="text-gray-400 text-sm">Using brand colors</p>
              </div>
              <DotPattern
                width={28}
                height={28}
                cx={1}
                cy={1}
                cr={0.8}
                className="fill-aha-red/[0.08] [mask-image:radial-gradient(350px_circle_at_center,white,transparent)]"
              />
            </div>

            {/* Gradient Mask */}
            <div className="relative flex h-[300px] w-full flex-col items-center justify-center overflow-hidden rounded-lg bg-aha-dark border border-white/10">
              <div className="z-10 text-center">
                <h4 className="text-xl font-semibold text-white mb-2">Gradient Mask</h4>
                <p className="text-gray-400 text-sm">Fading from center</p>
              </div>
              <DotPattern
                width={24}
                height={24}
                cx={1}
                cy={1}
                cr={0.6}
                className="fill-white/[0.05] [mask-image:radial-gradient(ellipse_600px_400px_at_center,white,transparent)]"
              />
            </div>
          </div>
        </div>

        {/* Usage Guidelines */}
        <div className="space-y-4">
          <h2 className="text-2xl font-semibold text-white">Usage Guidelines</h2>
          <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-6">
            <div className="space-y-4 text-gray-300">
              <div>
                <h4 className="text-white font-semibold mb-2">Best Practices:</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>Keep opacity low (0.02-0.06) to maintain subtlety</li>
                  <li>Use mask-image for gradual fade effects</li>
                  <li>Match pattern density to content density</li>
                  <li>Consider brand colors for themed sections</li>
                </ul>
              </div>
              <div>
                <h4 className="text-white font-semibold mb-2">Parameters:</h4>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li><code className="bg-black/30 px-1 rounded">width/height</code>: Pattern tile size</li>
                  <li><code className="bg-black/30 px-1 rounded">cx/cy</code>: Circle position within tile</li>
                  <li><code className="bg-black/30 px-1 rounded">cr</code>: Circle radius</li>
                  <li><code className="bg-black/30 px-1 rounded">className</code>: Fill color and mask</li>
                </ul>
              </div>
              <div>
                <h4 className="text-white font-semibold mb-2">Optimized Settings by Section:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                  <div className="bg-black/20 p-3 rounded">
                    <h5 className="text-aha-red font-medium mb-1">Hero Sections</h5>
                    <p>width: 28-32px, opacity: 0.02-0.025</p>
                  </div>
                  <div className="bg-black/20 p-3 rounded">
                    <h5 className="text-aha-red font-medium mb-1">Pricing Cards</h5>
                    <p>width: 20px, opacity: 0.02-0.08</p>
                  </div>
                  <div className="bg-black/20 p-3 rounded">
                    <h5 className="text-aha-red font-medium mb-1">Feature Cards</h5>
                    <p>width: 16-18px, opacity: 0.015-0.03</p>
                  </div>
                  <div className="bg-black/20 p-3 rounded">
                    <h5 className="text-aha-red font-medium mb-1">Footer/Contact</h5>
                    <p>width: 16-26px, opacity: 0.01-0.02</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DotPatternDemoPage;
