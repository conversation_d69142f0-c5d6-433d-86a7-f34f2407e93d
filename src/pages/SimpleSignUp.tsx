import React from 'react';
import ModernHeader from '@/components/sections/ModernHeader';
import ModernFooter from '@/components/sections/ModernFooter';
import { motion } from 'framer-motion';
import CustomSignUpForm from '@/components/CustomSignUpForm';

const SimpleSignUp = () => {



  return (
    <div className="min-h-screen w-full bg-aha-dark text-white overflow-x-hidden">
      <ModernHeader />

      {/* Hero section */}
      <section className="pt-16 pb-8 md:pt-24 md:pb-12">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-400">
              Welcome to AHA Innovations
            </h1>
            <p className="text-gray-400 text-lg md:text-xl mb-8">
              The all-in-one platform for creators and agencies to automate their business.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Form section */}
      <section className="pb-16 md:pb-24 bg-aha-dark">
        <div className="container mx-auto px-4" style={{ backgroundColor: "transparent" }}>
          {/* Section title */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-10"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Create Your Account</h2>
            <p className="text-lg text-gray-300 max-w-2xl mx-auto">
              Join AHA Innovations and start automating your business today. Get access to all our powerful tools with a free account.
            </p>
          </motion.div>

          {/* Simple form container with fixed background */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="max-w-3xl mx-auto"
            style={{ backgroundColor: "transparent" }}
          >
            {/* Form container with decorative elements */}
            <div className="relative">
              {/* Decorative elements */}
              <div className="absolute -top-20 -right-20 w-40 h-40 bg-aha-red/20 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-aha-red/20 rounded-full blur-3xl"></div>

              {/* Use the CustomSignUpForm component */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                <CustomSignUpForm />
              </motion.div>

              {/* Already have an account section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.9 }}
                className="mt-8 text-center"
              >
                <p className="text-gray-400 mb-2">Already have an account?</p>
                <a
                  href="https://app.aha-innovations.com"
                  className="text-aha-red hover:text-aha-darkred font-medium transition-colors"
                >
                  Sign in to your account
                </a>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Sign in link */}
      <section className="pb-16 bg-aha-dark">
        <div className="container mx-auto px-4" style={{ backgroundColor: "transparent" }}>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="text-center"
          >
            <div className="pt-4 border-t border-gray-800">
              <p className="text-gray-400">Already have an account?</p>
              <a
                href="https://app.aha-innovations.com"
                className="mt-3 inline-block px-6 py-3 rounded-lg bg-gray-800 hover:bg-gray-700 transition-all duration-300 text-white font-medium"
              >
                Sign in to your account
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      <ModernFooter />
    </div>
  );
};

export default SimpleSignUp;
