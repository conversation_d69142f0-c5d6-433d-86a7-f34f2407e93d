import React from 'react';
import HeroHeader from '../components/sections/HeroHeader';
import { FlipWordsDemo, AHAFlipWordsHero } from '../components/demo/flip-words-demo';
import ModernFooter from '../components/sections/ModernFooter';

const FlipWordsTest: React.FC = () => {
  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden" style={{ background: '#0F0F0F' }}>
      <HeroHeader />
      
      {/* Test the basic demo */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Basic FlipWords Demo</h2>
          <FlipWordsDemo />
        </div>
      </section>

      {/* Test the AHA-specific hero version */}
      <section className="py-20 bg-black/50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">AHA FlipWords Hero</h2>
          <AHAFlipWordsHero />
        </div>
      </section>

      <ModernFooter />
    </div>
  );
};

export default FlipWordsTest;
