'use client';

import React, { useState, useEffect } from "react";
import { TextEffect } from "@/components/ui/text-effect";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useNavigate } from "react-router-dom";
import { ArrowLeft } from "lucide-react";

function TextEffectPerChar() {
  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold mb-4 text-aha-red">Per Character - Fade Effect</h3>
      <TextEffect per='char' preset='fade' className="text-2xl font-bold">
        Animate your ideas with AHA-Innovations
      </TextEffect>
    </div>
  );
}

function TextEffectWithPreset() {
  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold mb-4 text-aha-red">Per Word - Slide Effect</h3>
      <TextEffect per='word' as='h3' preset='slide' className="text-3xl font-bold">
        Build Websites, Funnels, Workflows
      </TextEffect>
    </div>
  );
}

function TextEffectWithCustomVariants() {
  const getRandomColor = () => {
    const colors = ['#dc2626', '#ea580c', '#d97706', '#65a30d', '#059669', '#0891b2', '#2563eb', '#7c3aed'];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  const fancyVariants = {
    container: {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          staggerChildren: 0.05,
        },
      },
    },
    item: {
      hidden: () => ({
        opacity: 0,
        y: Math.random() * 100 - 50,
        rotate: Math.random() * 90 - 45,
        scale: 0.3,
        color: getRandomColor(),
      }),
      visible: {
        opacity: 1,
        y: 0,
        rotate: 0,
        scale: 1,
        color: '#dc2626', // AHA red
        transition: {
          type: 'spring',
          damping: 12,
          stiffness: 200,
        },
      },
    },
  };

  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold mb-4 text-aha-red">Custom Variants - Spring Animation</h3>
      <TextEffect per='word' variants={fancyVariants} className="text-2xl font-bold">
        AHA-Innovations Platform
      </TextEffect>
    </div>
  );
}

function TextEffectWithCustomDelay() {
  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold mb-4 text-aha-red">Sequential Delays</h3>
      <div className='flex flex-col space-y-0'>
        <TextEffect
          per='char'
          delay={0.5}
          variants={{
            container: {
              hidden: {
                opacity: 0,
              },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.05,
                },
              },
            },
            item: {
              hidden: {
                opacity: 0,
                rotateX: 90,
                y: 10,
              },
              visible: {
                opacity: 1,
                rotateX: 0,
                y: 0,
                transition: {
                  duration: 0.2,
                },
              },
            },
          }}
          className="text-2xl font-bold"
        >
          Build your business
        </TextEffect>
        <TextEffect per='char' delay={1.5} className="text-2xl font-bold">
          with AHA-Innovations
        </TextEffect>
        <TextEffect
          per='char'
          delay={2.5}
          className='pt-4 text-sm text-white/70'
          preset='blur'
        >
          (with perfect timing!)
        </TextEffect>
      </div>
    </div>
  );
}

function TextEffectPerLine() {
  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold mb-4 text-aha-red">Per Line Animation</h3>
      <TextEffect
        per='line'
        as='p'
        segmentWrapperClassName='overflow-hidden block'
        variants={{
          container: {
            hidden: { opacity: 0 },
            visible: {
              opacity: 1,
              transition: { staggerChildren: 0.2 },
            },
          },
          item: {
            hidden: {
              opacity: 0,
              y: 40,
            },
            visible: {
              opacity: 1,
              y: 0,
              transition: {
                duration: 0.4,
              },
            },
          },
        }}
        className="text-xl"
      >
        {`CRM & Contact Management
Website & Funnel Builder
Automation & Analytics`}
      </TextEffect>
    </div>
  );
}

function TextEffectWithExit() {
  const [trigger, setTrigger] = useState(true);

  useEffect(() => {
    const interval = setInterval(() => {
      setTrigger((prev) => !prev);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const blurSlideVariants = {
    container: {
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: { staggerChildren: 0.02, delayChildren: 0.1 },
      },
      exit: {
        transition: { staggerChildren: 0.03, staggerDirection: -1 },
      },
    },
    item: {
      hidden: {
        opacity: 0,
        filter: 'blur(10px) brightness(0%)',
        y: 20,
        rotateX: -90,
      },
      visible: {
        opacity: 1,
        y: 0,
        rotateX: 0,
        filter: 'blur(0px) brightness(100%)',
        transition: {
          type: "spring",
          damping: 12,
          stiffness: 150,
          duration: 0.6,
        },
      },
      exit: {
        opacity: 0,
        y: -25,
        rotateX: 90,
        filter: 'blur(8px) brightness(200%)',
        transition: {
          duration: 0.5,
          ease: "easeIn",
        },
      },
    },
  };

  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold mb-4 text-aha-red">Advanced Exit Animation (Auto-cycling)</h3>
      <TextEffect
        className='inline-flex text-2xl font-bold'
        per='char'
        variants={blurSlideVariants}
        trigger={trigger}
      >
        AHA-Innovations Platform
      </TextEffect>
    </div>
  );
}

function SequentialDelayDemo() {
  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold mb-4 text-aha-red">Sequential Delays with Different Effects</h3>
      <div className='flex flex-col space-y-4'>
        <TextEffect
          per='word'
          variants={{
            container: {
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: { staggerChildren: 0.1, delayChildren: 0.2 },
              },
            },
            item: {
              hidden: { opacity: 0, y: 30, rotateY: -90 },
              visible: {
                opacity: 1,
                y: 0,
                rotateY: 0,
                transition: { type: "spring", damping: 10, stiffness: 120 },
              },
            },
          }}
          className="text-2xl font-bold text-aha-red"
        >
          Build Your Business
        </TextEffect>

        <TextEffect
          per='word'
          variants={{
            container: {
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: { staggerChildren: 0.08, delayChildren: 1.0 },
              },
            },
            item: {
              hidden: { opacity: 0, scale: 0, rotate: -180 },
              visible: {
                opacity: 1,
                scale: 1,
                rotate: 0,
                transition: { type: "spring", damping: 8, stiffness: 100 },
              },
            },
          }}
          className="text-xl font-semibold"
        >
          with AHA-Innovations
        </TextEffect>

        <TextEffect
          per='char'
          variants={{
            container: {
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: { staggerChildren: 0.03, delayChildren: 2.0 },
              },
            },
            item: {
              hidden: { opacity: 0, filter: 'blur(8px)', x: -10 },
              visible: {
                opacity: 1,
                filter: 'blur(0px)',
                x: 0,
                transition: { duration: 0.4 },
              },
            },
          }}
          className="text-sm text-white/70"
        >
          (Perfect timing, every time!)
        </TextEffect>
      </div>
    </div>
  );
}

export default function TextEffectDemo() {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-aha-dark text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => navigate('/')}
            className="mb-4 text-white hover:text-aha-red"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Button>
          
          <Badge className="bg-aha-red/20 hover:bg-aha-red/30 text-aha-red border-none mb-6 py-1.5 px-4">
            Text Effect Demo
          </Badge>
          
          <h1 className="text-4xl md:text-5xl font-bold mb-4 font-gotham-condensed">
            TextEffect Component Demo
          </h1>
          <p className="text-white/90 text-lg mb-8 max-w-2xl font-gotham">
            Explore different text animation effects that can be applied to section titles throughout the site.
          </p>
        </div>

        <div className="max-w-4xl mx-auto space-y-12">
          <TextEffectPerChar />
          <TextEffectWithPreset />
          <TextEffectWithCustomVariants />
          <TextEffectWithCustomDelay />
          <TextEffectPerLine />
          <SequentialDelayDemo />
          <TextEffectWithExit />
        </div>
      </div>
    </div>
  );
}
