
import React, { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DotPattern } from '@/components/ui/dot-pattern';
import { TextEffect } from '@/components/ui/text-effect';
import { CalendarPopup } from '@/components/ui/calendar-popup';
import Tilt3D from '@/components/anim/Tilt3D';
import HeroHeader from '@/components/sections/HeroHeader';
import ModernFooter from '@/components/sections/ModernFooter';
import {
  Settings,
  Globe,
  Code,
  MessageSquare,
  Users,
  Calendar,
  Mail,
  Phone,
  BarChart3,
  Star,
  Building,
  FileText,
  ArrowRight
} from 'lucide-react';

const Features = () => {
  const navigate = useNavigate();
  const [showCalendar, setShowCalendar] = useState(false);
  const heroRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  // Parallax effect for hero section - keep content visible
  const y = useTransform(scrollYProgress, [0, 1], [0, 100]);
  const opacity = useTransform(scrollYProgress, [0, 0.95], [1, 0.98]);

  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden" style={{ background: '#0F0F0F' }}>
      <HeroHeader />

      {/* Hero Section */}
      <section ref={heroRef} className="relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #0F0F0F 0%, rgba(144, 25, 28, 0.8) 100%)' }}>
        {/* Background elements */}
        <div className="absolute inset-0 z-0 overflow-hidden">
          {/* Main gradient background */}
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>

          {/* Animated gradient blobs */}
          <motion.div
            className="absolute -top-[20%] -right-[10%] w-[60%] h-[60%] rounded-full bg-gradient-to-r from-aha-red/20 via-purple-600/10 to-blue-500/5 blur-[120px] opacity-60"
            animate={{
              rotate: 360,
              scale: [1, 1.1, 1],
            }}
            transition={{
              rotate: {
                duration: 40,
                repeat: Infinity,
                ease: "linear"
              },
              scale: {
                duration: 15,
                repeat: Infinity,
                repeatType: "reverse"
              }
            }}
          />

          <motion.div
            className="absolute -bottom-[20%] -left-[10%] w-[50%] h-[50%] rounded-full bg-gradient-to-r from-blue-500/10 via-purple-600/10 to-aha-red/20 blur-[120px] opacity-60"
            animate={{
              rotate: -360,
              scale: [1, 1.2, 1],
            }}
            transition={{
              rotate: {
                duration: 50,
                repeat: Infinity,
                ease: "linear"
              },
              scale: {
                duration: 18,
                repeat: Infinity,
                repeatType: "reverse",
                delay: 5
              }
            }}
          />
        </div>

        <motion.div style={{ y, opacity }} className="container mx-auto px-4 py-24 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="glass-card p-2 rounded-full w-fit mx-auto mb-6"
          >
            <Badge className="bg-white/10 hover:bg-white/20 text-white border-none py-1.5 px-4 mx-auto block w-fit">
              Our Features
            </Badge>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-center mb-10"
          >
            <TextEffect
              per="word"
              as="h1"
              className="text-4xl md:text-6xl font-bold mb-4 font-gotham"
              variants={{
                container: {
                  hidden: { opacity: 0 },
                  visible: {
                    opacity: 1,
                    transition: { staggerChildren: 0.12, delayChildren: 0.5 },
                  },
                  exit: {
                    transition: { staggerChildren: 0.08, staggerDirection: 1 },
                  },
                },
                item: {
                  hidden: {
                    opacity: 0,
                    y: 50,
                    rotateY: -90,
                  },
                  visible: {
                    opacity: 1,
                    y: 0,
                    rotateY: 0,
                    transition: {
                      type: "spring",
                      damping: 15,
                      stiffness: 100,
                      duration: 1,
                    },
                  },
                  exit: {
                    opacity: 0,
                    y: -30,
                    rotateY: 90,
                    transition: {
                      duration: 0.6,
                      ease: "easeIn",
                    },
                  },
                },
              }}
            >
              All-in-one Platform for Business Automation
            </TextEffect>
          </motion.div>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="text-xl text-gray-300 text-center max-w-3xl mx-auto mb-12"
          >
            Discover how AHA-Innovations can streamline your workflows, enhance efficiency, and help your
            business thrive with powerful automation solutions tailored to your needs.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="flex justify-center"
          >
            <Button
              size="lg"
              className="bg-gradient-to-r from-aha-red to-aha-darkred hover:from-aha-darkred hover:to-aha-red text-white text-lg px-8 py-6 relative overflow-hidden group shadow-lg shadow-aha-red/20"
              onClick={() => navigate('/signup')}
            >
              <span className="relative z-10">Try AHA-Innovations Today</span>
              <span className="absolute inset-0 bg-white/20 backdrop-blur-sm transform translate-y-full group-hover:translate-y-0 transition-transform duration-300"></span>
            </Button>
          </motion.div>
        </motion.div>
      </section>

      {/* Main Features */}
      <section className="py-24 relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 z-0 overflow-hidden">
          {/* Dot pattern texture overlay */}
          <DotPattern
            width={32}
            height={32}
            cx={1}
            cy={1}
            cr={0.8}
            className="fill-white/[0.02] [mask-image:radial-gradient(1200px_circle_at_center,white,transparent)]"
          />
          {/* Grid pattern */}
          <div
            className="absolute inset-0 opacity-[0.03] pointer-events-none z-0"
            style={{
              backgroundImage: `linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                              linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px)`,
              backgroundSize: '40px 40px'
            }}
          />

          {/* Animated gradient blobs */}
          <motion.div
            className="absolute top-0 right-0 w-[70%] h-[70%] bg-gradient-to-l from-blue-500/5 via-purple-500/5 to-transparent rounded-full blur-[150px]"
            animate={{
              x: [0, 50, 0],
              opacity: [0.3, 0.5, 0.3],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />

          <motion.div
            className="absolute bottom-0 left-0 w-[70%] h-[70%] bg-gradient-to-r from-aha-red/5 via-purple-500/5 to-transparent rounded-full blur-[150px]"
            animate={{
              x: [0, -50, 0],
              opacity: [0.2, 0.4, 0.2],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 5
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-3xl md:text-5xl font-bold text-center mb-16 font-gotham"
          >
            Core Features
          </motion.h2>

          <div className="grid md:grid-cols-3 gap-8">
            <FeatureCard
              icon={<Users className="text-aha-red" />}
              title="CRM & Pipeline Management"
              description="Organize your contacts, track deals, and streamline customer relationships all in one place."
            />
            <FeatureCard
              icon={<Code className="text-aha-red" />}
              title="Sales Funnels"
              description="Create unlimited sales funnels to convert prospects into customers with our intuitive builder."
            />
            <FeatureCard
              icon={<Mail className="text-aha-red" />}
              title="Email Marketing"
              description="Design, send, and track engaging email campaigns with advanced automation capabilities."
            />
            <FeatureCard
              icon={<Phone className="text-aha-red" />}
              title="2-Way SMS Marketing"
              description="Connect with customers instantly through automated SMS campaigns and conversations."
            />
            <FeatureCard
              icon={<Globe className="text-aha-red" />}
              title="Website Builder"
              description="Create professional websites with our drag-and-drop builder, no coding required."
            />
            <FeatureCard
              icon={<Calendar className="text-aha-red" />}
              title="Booking & Appointments"
              description="Let clients schedule appointments with automated reminders and calendar syncing."
            />
          </div>
        </div>
      </section>

      {/* CRM Section */}
      <section className="py-24 backdrop-blur-sm relative overflow-hidden" style={{ background: 'rgba(15, 15, 15, 0.7)' }}>
        {/* Background elements */}
        <div className="absolute inset-0 z-0 overflow-hidden">
          {/* Noise texture */}
          <div
            className="absolute inset-0 opacity-[0.03] pointer-events-none z-0"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
              backgroundSize: '200px'
            }}
          />

          {/* Main gradient */}
          <div className="absolute top-0 right-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.05),transparent_70%)]"></div>

          {/* Animated gradient blobs - similar to Firebase Studio */}
          <motion.div
            className="absolute top-1/3 right-0 w-1/2 h-1/2 bg-gradient-to-l from-purple-500/5 via-blue-500/5 to-transparent rounded-full blur-[150px]"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.5, 0.3],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />

          <motion.div
            className="absolute -bottom-1/4 -left-1/4 w-1/2 h-1/2 bg-gradient-to-br from-aha-red/10 via-purple-500/5 to-transparent rounded-full blur-[150px]"
            animate={{
              rotate: 360,
              scale: [1, 1.1, 1],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{
              rotate: {
                duration: 40,
                repeat: Infinity,
                ease: "linear"
              },
              scale: {
                duration: 20,
                repeat: Infinity,
                repeatType: "reverse"
              }
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid md:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="glass-card p-1 rounded-full w-fit mb-6">
                <Badge className="bg-aha-red hover:bg-aha-darkred text-white border-none py-1 px-3">
                  CRM & Pipeline Management
                </Badge>
              </div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 font-gotham">
                Organize Your Customer Relationships Like Never Before
              </h2>
              <p className="text-gray-300 mb-8">
                Our powerful CRM system helps you track every client interaction, organize contacts, and visualize
                your sales pipeline in real-time. With customizable stages and automated follow-ups, you'll never
                miss an opportunity again.
              </p>

              <ul className="space-y-4">
                <FeatureItem text="Visual pipeline management with drag-and-drop interface" />
                <FeatureItem text="Contact organization with custom fields and tags" />
                <FeatureItem text="Automated follow-up sequences" />
                <FeatureItem text="Deal tracking and forecasting" />
                <FeatureItem text="Custom reporting and analytics" />
              </ul>

              <Button
                onClick={() => navigate('/signup')}
                className="mt-8 bg-gradient-to-r from-aha-red to-aha-darkred hover:from-aha-darkred hover:to-aha-red group relative overflow-hidden shadow-md shadow-aha-red/10">
                <span className="relative z-10 flex items-center">Try This Feature <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" /></span>
                <span className="absolute inset-0 bg-white/20 backdrop-blur-sm transform translate-y-full group-hover:translate-y-0 transition-transform duration-300"></span>
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              {/* Glow effect background */}
              <div className="absolute -inset-4 bg-gradient-to-r from-aha-red/20 via-purple-500/10 to-transparent rounded-2xl blur-xl opacity-70"></div>

              {/* Mockup frame */}
              <div className="glass-card p-1 rounded-xl shadow-2xl relative overflow-hidden">
                {/* Browser-like header */}
                <div className="bg-black/50 backdrop-blur-sm h-8 w-full flex items-center px-3 rounded-t-lg">
                  <div className="flex gap-2">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  </div>
                  <div className="text-xs text-white/70 mx-auto">AHA-Innovations CRM Dashboard</div>
                </div>

                {/* Abstract CRM visualization */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  className="relative overflow-hidden bg-gradient-to-br from-gray-900 to-black p-6 rounded-b-lg"
                >
                  {/* CRM Dashboard Abstract Visualization */}
                  <div className="relative h-[300px]">
                    {/* Header */}
                    <div className="flex justify-between items-center mb-6">
                      <div className="w-24 h-6 bg-aha-red/20 rounded-md"></div>
                      <div className="flex space-x-2">
                        <div className="w-8 h-8 rounded-full bg-aha-red/30"></div>
                        <div className="w-8 h-8 rounded-full bg-blue-500/30"></div>
                      </div>
                    </div>

                    {/* Pipeline visualization */}
                    <div className="grid grid-cols-4 gap-3 mb-6">
                      <motion.div
                        className="h-32 bg-gradient-to-b from-aha-red/20 to-aha-red/5 rounded-lg p-3"
                        animate={{ y: [0, -5, 0] }}
                        transition={{ duration: 3, repeat: Infinity, repeatType: "reverse" }}
                      >
                        <div className="w-full h-4 bg-white/10 rounded-sm mb-2"></div>
                        <div className="w-3/4 h-3 bg-white/10 rounded-sm mb-4"></div>
                        <div className="w-8 h-8 rounded-full bg-aha-red/40 mb-2"></div>
                        <div className="w-1/2 h-3 bg-white/10 rounded-sm"></div>
                      </motion.div>

                      <motion.div
                        className="h-32 bg-gradient-to-b from-blue-500/20 to-blue-500/5 rounded-lg p-3"
                        animate={{ y: [0, -5, 0] }}
                        transition={{ duration: 3.5, repeat: Infinity, repeatType: "reverse", delay: 0.5 }}
                      >
                        <div className="w-full h-4 bg-white/10 rounded-sm mb-2"></div>
                        <div className="w-3/4 h-3 bg-white/10 rounded-sm mb-4"></div>
                        <div className="w-8 h-8 rounded-full bg-blue-500/40 mb-2"></div>
                        <div className="w-1/2 h-3 bg-white/10 rounded-sm"></div>
                      </motion.div>

                      <motion.div
                        className="h-32 bg-gradient-to-b from-purple-500/20 to-purple-500/5 rounded-lg p-3"
                        animate={{ y: [0, -5, 0] }}
                        transition={{ duration: 2.8, repeat: Infinity, repeatType: "reverse", delay: 1 }}
                      >
                        <div className="w-full h-4 bg-white/10 rounded-sm mb-2"></div>
                        <div className="w-3/4 h-3 bg-white/10 rounded-sm mb-4"></div>
                        <div className="w-8 h-8 rounded-full bg-purple-500/40 mb-2"></div>
                        <div className="w-1/2 h-3 bg-white/10 rounded-sm"></div>
                      </motion.div>

                      <motion.div
                        className="h-32 bg-gradient-to-b from-green-500/20 to-green-500/5 rounded-lg p-3"
                        animate={{ y: [0, -5, 0] }}
                        transition={{ duration: 3.2, repeat: Infinity, repeatType: "reverse", delay: 1.5 }}
                      >
                        <div className="w-full h-4 bg-white/10 rounded-sm mb-2"></div>
                        <div className="w-3/4 h-3 bg-white/10 rounded-sm mb-4"></div>
                        <div className="w-8 h-8 rounded-full bg-green-500/40 mb-2"></div>
                        <div className="w-1/2 h-3 bg-white/10 rounded-sm"></div>
                      </motion.div>
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-2 gap-4">
                      <motion.div
                        className="bg-gradient-to-r from-aha-red/10 to-purple-500/10 rounded-lg p-4"
                        animate={{ opacity: [0.7, 1, 0.7] }}
                        transition={{ duration: 4, repeat: Infinity }}
                      >
                        <div className="text-xs text-white/70 mb-1">Contacts</div>
                        <div className="text-2xl font-bold text-white">10,000+</div>
                        <div className="text-xs text-green-400 mt-1">+12% this month</div>
                      </motion.div>

                      <motion.div
                        className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg p-4"
                        animate={{ opacity: [0.7, 1, 0.7] }}
                        transition={{ duration: 4, repeat: Infinity, delay: 1 }}
                      >
                        <div className="text-xs text-white/70 mb-1">Deals</div>
                        <div className="text-2xl font-bold text-white">32%</div>
                        <div className="text-xs text-green-400 mt-1">Conversion Rate</div>
                      </motion.div>
                    </div>

                    {/* Animated dots to represent data flow */}
                    <motion.div
                      className="absolute top-1/4 left-1/4 w-2 h-2 rounded-full bg-aha-red"
                      animate={{
                        x: [0, 100, 200, 300],
                        y: [0, 50, 100, 50],
                        opacity: [1, 0.8, 0.6, 0]
                      }}
                      transition={{ duration: 4, repeat: Infinity, repeatType: "loop" }}
                    />

                    <motion.div
                      className="absolute top-1/2 left-1/3 w-2 h-2 rounded-full bg-blue-500"
                      animate={{
                        x: [0, 100, 200],
                        y: [0, -50, 0],
                        opacity: [1, 0.8, 0]
                      }}
                      transition={{ duration: 3, repeat: Infinity, repeatType: "loop", delay: 1 }}
                    />

                    <motion.div
                      className="absolute bottom-1/4 left-1/2 w-2 h-2 rounded-full bg-purple-500"
                      animate={{
                        x: [0, 100],
                        y: [0, -100],
                        opacity: [1, 0]
                      }}
                      transition={{ duration: 2, repeat: Infinity, repeatType: "loop", delay: 0.5 }}
                    />
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Sales Funnels Section */}
      <section className="py-24 relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 z-0 overflow-hidden">
          {/* Main gradient */}
          <div className="absolute bottom-0 left-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.05),transparent_70%)]">
          </div>

          {/* Animated gradient blobs - similar to Finofo */}
          <motion.div
            className="absolute top-0 left-0 w-full h-full"
            style={{
              background: 'radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)',
              filter: 'blur(80px)'
            }}
            animate={{
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />

          <motion.div
            className="absolute bottom-0 right-0 w-full h-full"
            style={{
              background: 'radial-gradient(circle at 70% 70%, rgba(234, 56, 76, 0.1) 0%, transparent 50%)',
              filter: 'blur(80px)'
            }}
            animate={{
              opacity: [0.2, 0.4, 0.2]
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 2
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid md:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="order-2 md:order-1 relative"
            >
              {/* Glow effect background */}
              <div className="absolute -inset-4 bg-gradient-to-r from-blue-500/20 via-aha-red/10 to-transparent rounded-2xl blur-xl opacity-70"></div>

              {/* Mockup frame */}
              <div className="glass-card p-1 rounded-xl shadow-2xl relative overflow-hidden">
                {/* Browser-like header */}
                <div className="bg-black/50 backdrop-blur-sm h-8 w-full flex items-center px-3 rounded-t-lg">
                  <div className="flex gap-2">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  </div>
                  <div className="text-xs text-white/70 mx-auto">AHA-Innovations Website Builder</div>
                </div>

                {/* Abstract Website Builder visualization */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  className="relative overflow-hidden bg-gradient-to-br from-gray-900 to-black p-6 rounded-b-lg"
                >
                  {/* Website Builder Abstract Visualization */}
                  <div className="relative h-[300px]">
                    {/* Toolbar */}
                    <div className="flex space-x-3 mb-6">
                      <motion.div
                        className="w-10 h-10 bg-aha-red/30 rounded-md flex items-center justify-center"
                        whileHover={{ scale: 1.1, backgroundColor: "rgba(234, 56, 76, 0.5)" }}
                      >
                        <div className="w-5 h-5 bg-white/30 rounded-sm"></div>
                      </motion.div>
                      <motion.div
                        className="w-10 h-10 bg-blue-500/30 rounded-md flex items-center justify-center"
                        whileHover={{ scale: 1.1, backgroundColor: "rgba(59, 130, 246, 0.5)" }}
                      >
                        <div className="w-5 h-1 bg-white/30 rounded-sm"></div>
                      </motion.div>
                      <motion.div
                        className="w-10 h-10 bg-purple-500/30 rounded-md flex items-center justify-center"
                        whileHover={{ scale: 1.1, backgroundColor: "rgba(139, 92, 246, 0.5)" }}
                      >
                        <div className="w-5 h-5 bg-white/30 rounded-sm"></div>
                      </motion.div>
                      <motion.div
                        className="w-10 h-10 bg-green-500/30 rounded-md flex items-center justify-center"
                        whileHover={{ scale: 1.1, backgroundColor: "rgba(34, 197, 94, 0.5)" }}
                      >
                        <div className="w-5 h-3 bg-white/30 rounded-sm"></div>
                      </motion.div>
                    </div>

                    {/* Canvas area */}
                    <div className="relative border border-white/10 rounded-lg h-[200px] mb-4 overflow-hidden">
                      {/* Header section */}
                      <div className="h-12 border-b border-white/10 bg-gradient-to-r from-aha-red/10 to-transparent px-4 flex items-center">
                        <div className="w-8 h-8 rounded-full bg-aha-red/30 mr-3"></div>
                        <div className="space-y-1">
                          <div className="w-24 h-2 bg-white/20 rounded-full"></div>
                          <div className="w-16 h-2 bg-white/10 rounded-full"></div>
                        </div>
                        <div className="ml-auto flex space-x-3">
                          <div className="w-12 h-6 rounded-md bg-aha-red/30"></div>
                          <div className="w-12 h-6 rounded-md bg-white/10"></div>
                        </div>
                      </div>

                      {/* Hero section */}
                      <div className="p-4">
                        <div className="w-3/4 h-4 bg-white/20 rounded-full mb-2"></div>
                        <div className="w-1/2 h-3 bg-white/10 rounded-full mb-4"></div>
                        <div className="w-32 h-8 rounded-md bg-aha-red/40"></div>
                      </div>

                      {/* Draggable element */}
                      <motion.div
                        className="absolute w-20 h-16 bg-blue-500/20 border border-blue-500/40 rounded-md flex items-center justify-center"
                        style={{ top: '50%', left: '30%' }}
                        drag
                        dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
                        dragElastic={0.2}
                      >
                        <div className="w-10 h-2 bg-white/30 rounded-full mb-1"></div>
                        <div className="w-6 h-2 bg-white/20 rounded-full"></div>
                      </motion.div>

                      {/* Draggable element */}
                      <motion.div
                        className="absolute w-24 h-20 bg-purple-500/20 border border-purple-500/40 rounded-md flex items-center justify-center"
                        style={{ top: '30%', left: '60%' }}
                        drag
                        dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
                        dragElastic={0.2}
                      >
                        <div className="w-12 h-8 bg-white/10 rounded-md"></div>
                      </motion.div>
                    </div>

                    {/* Element panel */}
                    <div className="grid grid-cols-2 gap-2">
                      <div className="bg-white/5 rounded-md p-2">
                        <div className="text-xs text-white/50 mb-2">Elements</div>
                        <div className="grid grid-cols-3 gap-1">
                          <motion.div
                            className="w-full aspect-square bg-aha-red/20 rounded-sm"
                            whileHover={{ scale: 1.1, backgroundColor: "rgba(234, 56, 76, 0.3)" }}
                          ></motion.div>
                          <motion.div
                            className="w-full aspect-square bg-blue-500/20 rounded-sm"
                            whileHover={{ scale: 1.1, backgroundColor: "rgba(59, 130, 246, 0.3)" }}
                          ></motion.div>
                          <motion.div
                            className="w-full aspect-square bg-purple-500/20 rounded-sm"
                            whileHover={{ scale: 1.1, backgroundColor: "rgba(139, 92, 246, 0.3)" }}
                          ></motion.div>
                        </div>
                      </div>

                      <div className="bg-white/5 rounded-md p-2">
                        <div className="text-xs text-white/50 mb-2">Templates</div>
                        <div className="space-y-1">
                          <div className="w-full h-3 bg-white/10 rounded-full"></div>
                          <div className="w-full h-3 bg-white/10 rounded-full"></div>
                          <div className="w-3/4 h-3 bg-white/10 rounded-full"></div>
                        </div>
                      </div>
                    </div>

                    {/* Animated cursor */}
                    <motion.div
                      className="absolute w-4 h-4 border-2 border-white rounded-full"
                      animate={{
                        x: [50, 150, 250, 150, 50],
                        y: [50, 100, 150, 200, 150],
                        scale: [1, 1.2, 0.8, 1.2, 1],
                        opacity: [0.6, 1, 0.8, 1, 0.6]
                      }}
                      transition={{
                        duration: 10,
                        repeat: Infinity,
                        repeatType: "loop"
                      }}
                    />
                  </div>
                </motion.div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="order-1 md:order-2"
            >
              <div className="glass-card p-1 rounded-full w-fit mb-6">
                <Badge className="bg-aha-red hover:bg-aha-darkred text-white border-none py-1 px-3">
                  Sales Funnels
                </Badge>
              </div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 font-gotham">
                Convert More Prospects with Unlimited Sales Funnels
              </h2>
              <p className="text-gray-300 mb-8">
                Design high-converting sales funnels with our intuitive drag-and-drop builder.
                Create landing pages, order forms, upsells, and thank you pages that guide customers
                through their buying journey.
              </p>

              <ul className="space-y-4">
                <FeatureItem text="Unlimited sales funnels with all plans" />
                <FeatureItem text="Drag-and-drop page builder" />
                <FeatureItem text="Pre-made templates for quick setup" />
                <FeatureItem text="A/B testing to optimize conversion rates" />
                <FeatureItem text="Seamless payment processing integration" />
              </ul>

              <Button
                onClick={() => navigate('/signup')}
                className="mt-8 bg-gradient-to-r from-aha-red to-aha-darkred hover:from-aha-darkred hover:to-aha-red group relative overflow-hidden shadow-md shadow-aha-red/10">
                <span className="relative z-10 flex items-center">Try This Feature <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" /></span>
                <span className="absolute inset-0 bg-white/20 backdrop-blur-sm transform translate-y-full group-hover:translate-y-0 transition-transform duration-300"></span>
              </Button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Email Marketing Section */}
      <section className="py-24 relative overflow-hidden" style={{ background: 'rgba(15, 15, 15, 0.9)' }}>
        {/* Background elements */}
        <div className="absolute inset-0 z-0 overflow-hidden">
          {/* Animated gradient blobs - similar to Firebase Studio */}
          <motion.div
            className="absolute -top-1/4 -right-1/4 w-1/2 h-1/2 bg-gradient-to-br from-aha-red/10 via-purple-500/5 to-transparent rounded-full blur-[150px]"
            animate={{
              rotate: 360,
              scale: [1, 1.1, 1],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{
              rotate: {
                duration: 40,
                repeat: Infinity,
                ease: "linear"
              },
              scale: {
                duration: 20,
                repeat: Infinity,
                repeatType: "reverse"
              }
            }}
          />

          <motion.div
            className="absolute -bottom-1/4 -left-1/4 w-1/2 h-1/2 bg-gradient-to-tr from-blue-500/10 via-purple-500/5 to-transparent rounded-full blur-[150px]"
            animate={{
              rotate: -360,
              scale: [1, 1.2, 1],
              opacity: [0.2, 0.3, 0.2],
            }}
            transition={{
              rotate: {
                duration: 50,
                repeat: Infinity,
                ease: "linear"
              },
              scale: {
                duration: 25,
                repeat: Infinity,
                repeatType: "reverse"
              }
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid md:grid-cols-2 gap-16 items-center">
            <div>
              <Badge className="bg-aha-red hover:bg-aha-darkred text-white border-none mb-6 py-1 px-3">
                Email Marketing
              </Badge>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 font-gotham">
                Powerful Email Campaigns That Convert
              </h2>
              <p className="text-gray-300 mb-8">
                Create beautiful email campaigns, automate your marketing sequences, and analyze
                performance with detailed reports. Our email marketing tools make it easy to
                stay connected with your audience.
              </p>

              <ul className="space-y-4">
                <FeatureItem text="Drag-and-drop email builder" />
                <FeatureItem text="Automation workflows and sequences" />
                <FeatureItem text="List segmentation and targeting" />
                <FeatureItem text="Detailed analytics and reporting" />
                <FeatureItem text="High deliverability rates" />
              </ul>

              <Button
                onClick={() => navigate('/signup')}
                className="mt-8 bg-gradient-to-r from-aha-red to-aha-darkred hover:from-aha-darkred hover:to-aha-red group relative overflow-hidden shadow-md shadow-aha-red/10">
                <span className="relative z-10 flex items-center">Try This Feature <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" /></span>
                <span className="absolute inset-0 bg-white/20 backdrop-blur-sm transform translate-y-full group-hover:translate-y-0 transition-transform duration-300"></span>
              </Button>
            </div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              {/* Glow effect background */}
              <div className="absolute -inset-4 bg-gradient-to-r from-purple-500/20 via-aha-red/10 to-transparent rounded-2xl blur-xl opacity-70"></div>

              {/* Mockup frame */}
              <div className="glass-card p-1 rounded-xl shadow-2xl relative overflow-hidden">
                {/* Browser-like header */}
                <div className="bg-black/50 backdrop-blur-sm h-8 w-full flex items-center px-3 rounded-t-lg">
                  <div className="flex gap-2">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  </div>
                  <div className="text-xs text-white/70 mx-auto">AHA-Innovations Email Marketing</div>
                </div>

                {/* Abstract Email Marketing visualization */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  className="relative overflow-hidden bg-gradient-to-br from-gray-900 to-black p-6 rounded-b-lg"
                >
                  {/* Email Marketing Abstract Visualization */}
                  <div className="relative h-[300px]">
                    {/* Email builder interface */}
                    <div className="flex h-full">
                      {/* Left sidebar - email templates */}
                      <div className="w-1/4 border-r border-white/10 pr-3">
                        <div className="text-xs text-white/70 mb-3">Templates</div>
                        <div className="space-y-3">
                          <motion.div
                            className="h-16 bg-aha-red/20 rounded-md"
                            whileHover={{ scale: 1.05, backgroundColor: "rgba(234, 56, 76, 0.3)" }}
                          ></motion.div>
                          <motion.div
                            className="h-16 bg-blue-500/20 rounded-md"
                            whileHover={{ scale: 1.05, backgroundColor: "rgba(59, 130, 246, 0.3)" }}
                          ></motion.div>
                          <motion.div
                            className="h-16 bg-purple-500/20 rounded-md"
                            whileHover={{ scale: 1.05, backgroundColor: "rgba(139, 92, 246, 0.3)" }}
                          ></motion.div>
                          <motion.div
                            className="h-16 bg-green-500/20 rounded-md"
                            whileHover={{ scale: 1.05, backgroundColor: "rgba(34, 197, 94, 0.3)" }}
                          ></motion.div>
                        </div>
                      </div>

                      {/* Main email canvas */}
                      <div className="w-3/4 pl-4">
                        {/* Email header */}
                        <div className="flex justify-between items-center mb-4">
                          <div className="text-sm text-white/70">New Campaign</div>
                          <motion.div
                            className="px-3 py-1 bg-aha-red/40 rounded-md text-xs text-white"
                            whileHover={{ scale: 1.05, backgroundColor: "rgba(234, 56, 76, 0.6)" }}
                          >
                            Send
                          </motion.div>
                        </div>

                        {/* Email preview */}
                        <div className="border border-white/10 rounded-lg p-4 bg-white/5">
                          {/* Email header */}
                          <div className="mb-4 pb-3 border-b border-white/10">
                            <div className="w-full h-4 bg-white/20 rounded-full mb-2"></div>
                            <div className="w-1/2 h-3 bg-white/10 rounded-full"></div>
                          </div>

                          {/* Email content */}
                          <div className="space-y-3">
                            <div className="w-3/4 h-3 bg-white/20 rounded-full"></div>
                            <div className="w-full h-3 bg-white/10 rounded-full"></div>
                            <div className="w-5/6 h-3 bg-white/10 rounded-full"></div>
                            <div className="w-3/4 h-3 bg-white/10 rounded-full"></div>

                            {/* Image placeholder */}
                            <div className="w-full h-24 bg-gradient-to-r from-aha-red/20 to-purple-500/20 rounded-md flex items-center justify-center">
                              <div className="w-8 h-8 rounded-full bg-white/20"></div>
                            </div>

                            <div className="w-full h-3 bg-white/10 rounded-full"></div>
                            <div className="w-5/6 h-3 bg-white/10 rounded-full"></div>

                            {/* CTA button */}
                            <div className="w-32 h-8 mx-auto bg-aha-red/40 rounded-md"></div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Analytics overlay that fades in and out */}
                    <motion.div
                      className="absolute inset-0 bg-black/80 flex items-center justify-center"
                      initial={{ opacity: 0 }}
                      animate={{
                        opacity: [0, 0.9, 0],
                      }}
                      transition={{
                        duration: 8,
                        times: [0, 0.5, 1],
                        repeat: Infinity,
                        repeatDelay: 5
                      }}
                    >
                      <div className="w-full max-w-md">
                        <div className="text-center mb-4">
                          <div className="text-lg font-bold text-white mb-1">Campaign Analytics</div>
                          <div className="text-sm text-white/70">Last 30 days</div>
                        </div>

                        {/* Stats */}
                        <div className="grid grid-cols-3 gap-4 mb-6">
                          <div className="text-center">
                            <div className="text-2xl font-bold text-aha-red">68%</div>
                            <div className="text-xs text-white/70">Open Rate</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-blue-500">42%</div>
                            <div className="text-xs text-white/70">Click Rate</div>
                          </div>
                          <div className="text-center">
                            <div className="text-2xl font-bold text-green-500">12%</div>
                            <div className="text-xs text-white/70">Conversion</div>
                          </div>
                        </div>

                        {/* Chart */}
                        <div className="h-32 flex items-end justify-between px-4">
                          <motion.div
                            className="w-4 bg-aha-red/60 rounded-t-sm"
                            initial={{ height: 0 }}
                            animate={{ height: 80 }}
                            transition={{ duration: 1, delay: 0.1 }}
                          ></motion.div>
                          <motion.div
                            className="w-4 bg-aha-red/60 rounded-t-sm"
                            initial={{ height: 0 }}
                            animate={{ height: 100 }}
                            transition={{ duration: 1, delay: 0.2 }}
                          ></motion.div>
                          <motion.div
                            className="w-4 bg-aha-red/60 rounded-t-sm"
                            initial={{ height: 0 }}
                            animate={{ height: 60 }}
                            transition={{ duration: 1, delay: 0.3 }}
                          ></motion.div>
                          <motion.div
                            className="w-4 bg-aha-red/60 rounded-t-sm"
                            initial={{ height: 0 }}
                            animate={{ height: 90 }}
                            transition={{ duration: 1, delay: 0.4 }}
                          ></motion.div>
                          <motion.div
                            className="w-4 bg-aha-red/60 rounded-t-sm"
                            initial={{ height: 0 }}
                            animate={{ height: 120 }}
                            transition={{ duration: 1, delay: 0.5 }}
                          ></motion.div>
                          <motion.div
                            className="w-4 bg-aha-red/60 rounded-t-sm"
                            initial={{ height: 0 }}
                            animate={{ height: 70 }}
                            transition={{ duration: 1, delay: 0.6 }}
                          ></motion.div>
                          <motion.div
                            className="w-4 bg-aha-red/60 rounded-t-sm"
                            initial={{ height: 0 }}
                            animate={{ height: 110 }}
                            transition={{ duration: 1, delay: 0.7 }}
                          ></motion.div>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Advanced Features */}
      <section className="py-24 relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0 z-0 overflow-hidden">
          {/* Dot pattern texture overlay */}
          <DotPattern
            width={28}
            height={28}
            cx={0.9}
            cy={0.9}
            cr={0.6}
            className="fill-white/[0.025] [mask-image:radial-gradient(1000px_circle_at_center,white,transparent)]"
          />
          {/* Grid pattern */}
          <div
            className="absolute inset-0 opacity-[0.03] pointer-events-none z-0"
            style={{
              backgroundImage: `linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                              linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px)`,
              backgroundSize: '40px 40px'
            }}
          />

          {/* Animated gradient blobs - similar to Finofo */}
          <motion.div
            className="absolute top-0 left-0 w-full h-full"
            style={{
              background: 'radial-gradient(circle at 70% 30%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)',
              filter: 'blur(80px)'
            }}
            animate={{
              opacity: [0.3, 0.5, 0.3]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />

          <motion.div
            className="absolute bottom-0 right-0 w-full h-full"
            style={{
              background: 'radial-gradient(circle at 30% 70%, rgba(234, 56, 76, 0.1) 0%, transparent 50%)',
              filter: 'blur(80px)'
            }}
            animate={{
              opacity: [0.2, 0.4, 0.2]
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 2
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <h2 className="text-3xl md:text-5xl font-bold text-center mb-16 font-gotham">
            Additional Powerful Features
          </h2>

          <div className="grid md:grid-cols-3 gap-8">
            <FeatureCard
              icon={<Settings className="text-aha-red" />}
              title="Workflow Automations"
              description="Create powerful automations to streamline repetitive tasks and save hours every week."
            />
            <FeatureCard
              icon={<FileText className="text-aha-red" />}
              title="Surveys & Forms"
              description="Build custom forms and surveys to collect information from leads and customers."
            />
            <FeatureCard
              icon={<Building className="text-aha-red" />}
              title="Courses & Products"
              description="Sell digital products and create online courses with our integrated tools."
            />
            <FeatureCard
              icon={<BarChart3 className="text-aha-red" />}
              title="Tracking & Analytics"
              description="Gain valuable insights with comprehensive analytics and reporting tools."
            />
            <FeatureCard
              icon={<Star className="text-aha-red" />}
              title="Reputation Management"
              description="Monitor and manage online reviews to maintain a stellar business reputation."
            />
            <FeatureCard
              icon={<MessageSquare className="text-aha-red" />}
              title="Communities"
              description="Build engaged online communities and membership sites for your customers."
            />
          </div>
        </div>
      </section>

      {/* Call To Action */}
      <section className="py-24 relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #0F0F0F 0%, rgba(144, 25, 28, 0.8) 100%)' }}>
        {/* Background elements */}
        <div className="absolute inset-0 z-0 overflow-hidden">
          {/* Animated gradient blobs - similar to Firebase Studio */}
          <motion.div
            className="absolute -top-[20%] -right-[10%] w-[60%] h-[60%] rounded-full bg-gradient-to-r from-aha-red/20 via-purple-600/10 to-blue-500/5 blur-[120px] opacity-60"
            animate={{
              rotate: 360,
              scale: [1, 1.1, 1],
            }}
            transition={{
              rotate: {
                duration: 40,
                repeat: Infinity,
                ease: "linear"
              },
              scale: {
                duration: 15,
                repeat: Infinity,
                repeatType: "reverse"
              }
            }}
          />

          <motion.div
            className="absolute -bottom-[20%] -left-[10%] w-[50%] h-[50%] rounded-full bg-gradient-to-r from-blue-500/10 via-purple-600/10 to-aha-red/20 blur-[120px] opacity-60"
            animate={{
              rotate: -360,
              scale: [1, 1.2, 1],
            }}
            transition={{
              rotate: {
                duration: 50,
                repeat: Infinity,
                ease: "linear"
              },
              scale: {
                duration: 18,
                repeat: Infinity,
                repeatType: "reverse",
                delay: 5
              }
            }}
          />
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <h2 className="text-3xl md:text-5xl font-bold mb-8 font-gotham">
            Ready to Transform Your Business?
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-12">
            Join thousands of businesses that have streamlined their operations and boosted their growth with AHA-Innovations.
          </p>
          <div className="flex flex-col md:flex-row gap-6 justify-center">
            <Button
              size="lg"
              className="bg-gradient-to-r from-aha-red to-aha-darkred hover:from-aha-darkred hover:to-aha-red text-white px-8 py-6 relative overflow-hidden group shadow-lg shadow-aha-red/20"
              onClick={() => navigate('/pricing')}
            >
              <span className="relative z-10">View Pricing</span>
              <span className="absolute inset-0 bg-white/20 backdrop-blur-sm transform translate-y-full group-hover:translate-y-0 transition-transform duration-300"></span>
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white/10 px-8 py-6 relative overflow-hidden group shadow-lg shadow-white/5"
              onClick={() => setShowCalendar(true)}
            >
              <span className="relative z-10">Contact Sales</span>
              <span className="absolute inset-0 bg-white/5 backdrop-blur-sm transform translate-y-full group-hover:translate-y-0 transition-transform duration-300"></span>
            </Button>
          </div>
        </div>
      </section>

      <ModernFooter />

      {/* Calendar Popup */}
      <CalendarPopup
        isOpen={showCalendar}
        onClose={() => setShowCalendar(false)}
      />
    </div>
  );
};

// Note: We're now using AnimatedFeatureCard instead of FeatureCard
// and motion.li elements instead of FeatureItem
// These are kept for reference or future use

const FeatureCard = ({ icon, title, description }) => (
  <Tilt3D intensity={5} className="h-full">
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      viewport={{ once: true }}
      className="glass-card glass-card-hover h-full rounded-xl p-4 md:p-6 transition-all duration-300 relative overflow-hidden"
    >
      {/* Dot pattern for feature cards */}
      <DotPattern
        width={18}
        height={18}
        cx={0.7}
        cy={0.7}
        cr={0.3}
        className="fill-white/[0.015] [mask-image:radial-gradient(200px_circle_at_center,white,transparent)]"
      />
      <div className="mb-3 md:mb-4 p-2 md:p-3 bg-aha-red/10 backdrop-blur-sm rounded-full w-fit">
        <span className="md:hidden">{React.cloneElement(icon, { size: 16 })}</span>
        <span className="hidden md:block">{icon}</span>
      </div>
      <h3 className="text-lg md:text-xl font-bold mb-1 md:mb-2 font-gotham">{title}</h3>
      <p className="text-gray-300 text-sm md:text-base">{description}</p>
    </motion.div>
  </Tilt3D>
);

const FeatureItem = ({ text }) => (
  <motion.li
    initial={{ opacity: 0, x: -20 }}
    whileInView={{ opacity: 1, x: 0 }}
    transition={{ duration: 0.5 }}
    viewport={{ once: true }}
    className="flex items-start"
  >
    <span className="text-aha-red mr-1.5 md:mr-2 mt-0.5 md:mt-1 text-sm md:text-base">✓</span>
    <span className="text-gray-300 text-sm md:text-base">{text}</span>
  </motion.li>
);

export default Features;
