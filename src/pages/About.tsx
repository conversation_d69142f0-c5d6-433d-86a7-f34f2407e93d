import React, { useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import ModernHeader from '@/components/sections/ModernHeader';
import ModernFooter from '@/components/sections/ModernFooter';
import Tilt3D from '@/components/anim/Tilt3D';

const About = () => {
  const navigate = useNavigate();
  const heroRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  // Parallax effect for hero section
  const y = useTransform(scrollYProgress, [0, 1], [0, 200]);
  const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0]);

  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden" style={{ background: '#0F0F0F' }}>
      <ModernHeader />

      {/* Hero Section */}
      <section ref={heroRef} className="py-16 relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #0F0F0F 0%, rgba(144, 25, 28, 0.8) 100%)' }}>
        {/* Background elements */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
        </div>

        <motion.div
          style={{ y, opacity }}
          className="container mx-auto px-4 pt-20 pb-16 relative z-10"
        >
          <div className="text-center max-w-3xl mx-auto">
            <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4 mx-auto block w-fit">
              About Us
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Our Story
            </h1>
            <p className="text-xl text-gray-300">
              Learn about our mission, vision, and the team behind AHA-Innovations
            </p>
          </div>
        </motion.div>
      </section>

      {/* Mission & Vision Section */}
      <section className="py-20 relative">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <Tilt3D intensity={5}>
                <div className="glass-card rounded-xl p-8 border border-white/10 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-aha-red/5 to-transparent opacity-50"></div>
                  <h2 className="text-3xl font-bold mb-6 relative z-10">Our Mission</h2>
                  <p className="text-gray-300 mb-4 relative z-10">
                    AHA-Innovations empowers creators, entrepreneurs, and small businesses by providing easy-to-use, all-in-one tools that simplify growth, streamline operations, and eliminate the need for juggling multiple platforms.
                  </p>
                </div>
              </Tilt3D>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <Tilt3D intensity={5}>
                <div className="glass-card rounded-xl p-8 border border-white/10 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-aha-red/5 to-transparent opacity-50"></div>
                  <h2 className="text-3xl font-bold mb-6 relative z-10">Our Vision</h2>
                  <p className="text-gray-300 mb-4 relative z-10">
                    Our vision is to become the most trusted platform for digital-first businesses by transforming how people launch, manage, and scale their ideas through simple, powerful, and connected solutions.
                  </p>
                </div>
              </Tilt3D>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 relative bg-black/30">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4 mx-auto block w-fit">
              Our Values
            </Badge>
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              What Drives Us
            </h2>
            <p className="text-xl text-gray-300">
              The core principles that guide everything we do at AHA-Innovations
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <ValueCard 
              title="Simplicity" 
              description="We believe powerful tools should be simple to use. We focus on intuitive design that removes complexity without sacrificing functionality."
            />
            <ValueCard 
              title="Innovation" 
              description="We constantly push boundaries to create solutions that help businesses work smarter, not harder."
            />
            <ValueCard 
              title="Customer Success" 
              description="Your success is our success. We're committed to providing the support and tools you need to achieve your goals."
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 relative">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-xl p-12 border border-white/10 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-aha-red/10 to-transparent"></div>
            <div className="relative z-10 text-center max-w-3xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Ready to Transform Your Business?
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                Join thousands of businesses already using AHA-Innovations to streamline their operations and drive growth.
              </p>
              <Button
                className="bg-aha-red hover:bg-aha-darkred text-white group relative overflow-hidden"
                size="lg"
                onClick={() => navigate('/signup')}
              >
                <span className="relative z-10 flex items-center gap-2">
                  Get Started Free
                  <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                </span>
                <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
              </Button>
            </div>
          </div>
        </div>
      </section>

      <ModernFooter />
    </div>
  );
};

// Value Card Component
const ValueCard = ({ title, description }: { title: string; description: string }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6 }}
    >
      <Tilt3D intensity={5}>
        <div className="glass-card rounded-xl p-8 border border-white/10 h-full relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-aha-red/5 to-transparent opacity-30"></div>
          <h3 className="text-2xl font-bold mb-4 relative z-10">{title}</h3>
          <p className="text-gray-300 relative z-10">{description}</p>
        </div>
      </Tilt3D>
    </motion.div>
  );
};

export default About;
