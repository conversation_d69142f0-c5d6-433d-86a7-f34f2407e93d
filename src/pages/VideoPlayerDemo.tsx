import React from 'react';
import { VideoPlayerDemo } from '@/components/ui/video-player-demo';

const VideoPlayerDemoPage = () => {
  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <div className="w-full max-w-6xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
            AHA-Innovations <span className="text-aha-red">Video Player</span>
          </h1>
          <p className="text-gray-300 text-lg max-w-2xl mx-auto">
            Experience our platform showcase with this interactive video player featuring custom controls, 
            smooth animations, and responsive design.
          </p>
        </div>
        
        <VideoPlayerDemo />
        
        <div className="text-center mt-8">
          <p className="text-gray-400 text-sm">
            The original GIF has been preserved and can be used elsewhere in the application.
          </p>
        </div>
      </div>
    </div>
  );
};

export default VideoPlayerDemoPage; 