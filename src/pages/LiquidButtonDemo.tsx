import React from 'react';
import LiquidButtonDemo from '@/components/demo/liquid-button-demo';

const LiquidButtonDemoPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-8">
      <div className="max-w-4xl mx-auto text-center space-y-8">
        <div className="space-y-4">
          <h1 className="text-4xl md:text-6xl font-bold text-white">
            Liquid Button Demo
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Experience the mesmerizing liquid animation effect with this interactive button component.
            Hover over the button to see the liquid animation in action.
          </p>
        </div>
        
        <div className="py-12">
          <LiquidButtonDemo />
        </div>
        
        <div className="space-y-4 text-gray-300">
          <h2 className="text-2xl font-semibold text-white">Features</h2>
          <ul className="space-y-2 max-w-md mx-auto text-left">
            <li>• Animated liquid gradient effects</li>
            <li>• Hover state interactions</li>
            <li>• Responsive design</li>
            <li>• Customizable colors</li>
            <li>• Built with Framer Motion</li>
          </ul>
        </div>
        
        <div className="pt-8">
          <p className="text-sm text-gray-400">
            Component integrated into your shadcn/ui project structure
          </p>
        </div>
      </div>
    </div>
  );
};

export default LiquidButtonDemoPage;
