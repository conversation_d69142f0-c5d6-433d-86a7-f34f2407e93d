import React, { useRef, forwardRef } from 'react';
import { AnimatedBeam } from '../components/ui/animated-beam';
import { cn } from '@/lib/utils';
import CompetitorLogos from '../components/CompetitorLogos';

// Circle component for animated beam
const Circle = forwardRef<
  HTMLDivElement,
  { className?: string; children?: React.ReactNode }
>(({ className, children }, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "z-10 flex size-12 items-center justify-center rounded-full border-2 bg-white p-3 shadow-[0_0_20px_-12px_rgba(0,0,0,0.8)]",
        className,
      )}
    >
      {children}
    </div>
  );
});

Circle.displayName = "Circle";

// AHA Logo component
const AHALogo = () => (
  <img
    src="/Logomark Red.svg"
    alt="AHA-Innovations"
    className="w-8 h-8"
  />
);




const AnimatedBeamDemo: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const div1Ref = useRef<HTMLDivElement>(null);
  const div2Ref = useRef<HTMLDivElement>(null);
  const div3Ref = useRef<HTMLDivElement>(null);
  const div4Ref = useRef<HTMLDivElement>(null);
  const div5Ref = useRef<HTMLDivElement>(null);
  const div6Ref = useRef<HTMLDivElement>(null);
  const div7Ref = useRef<HTMLDivElement>(null);

  return (
    <div className="min-h-screen bg-aha-dark text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8">
          Animated Beam Demo - <span className="text-aha-red">AHA-Innovations</span>
        </h1>
        
        <div
          className="relative flex h-[500px] w-full items-center justify-center overflow-hidden rounded-lg border border-white/10 bg-black/30 backdrop-blur-sm p-10"
          ref={containerRef}
        >
          <div className="flex size-full flex-col max-w-lg max-h-[200px] items-stretch justify-between gap-10">
            <div className="flex flex-row items-center justify-between">
              <Circle ref={div1Ref} className="bg-white/10 border-white/20">
                <CompetitorLogos name="HubSpot" />
              </Circle>
              <Circle ref={div5Ref} className="bg-white/10 border-white/20">
                <CompetitorLogos name="Mailchimp" />
              </Circle>
            </div>
            <div className="flex flex-row items-center justify-between">
              <Circle ref={div2Ref} className="bg-white/10 border-white/20">
                <CompetitorLogos name="Zapier" />
              </Circle>
              <Circle ref={div4Ref} className="size-16 bg-aha-red/20 border-aha-red/30 relative z-[100] shadow-2xl shadow-aha-red/20">
                <AHALogo />
              </Circle>
              <Circle ref={div6Ref} className="bg-white/10 border-white/20">
                <CompetitorLogos name="Calendly" />
              </Circle>
            </div>
            <div className="flex flex-row items-center justify-between">
              <Circle ref={div3Ref} className="bg-white/10 border-white/20">
                <CompetitorLogos name="Messenger" />
              </Circle>
              <Circle ref={div7Ref} className="bg-white/10 border-white/20">
                <CompetitorLogos name="Twilio" />
              </Circle>
            </div>
          </div>

          <AnimatedBeam
            containerRef={containerRef}
            fromRef={div1Ref}
            toRef={div4Ref}
            curvature={-75}
            endXOffset={-22}
            endYOffset={-22}
            gradientStartColor="#FF7A59"
            gradientStopColor="#FF4A00"
            className="z-0"
          />
          <AnimatedBeam
            containerRef={containerRef}
            fromRef={div2Ref}
            toRef={div4Ref}
            endXOffset={-30}
            endYOffset={0}
            gradientStartColor="#FF4A00"
            gradientStopColor="#ef4444"
            className="z-0"
          />
          <AnimatedBeam
            containerRef={containerRef}
            fromRef={div3Ref}
            toRef={div4Ref}
            curvature={75}
            endXOffset={-22}
            endYOffset={22}
            gradientStartColor="#0084FF"
            gradientStopColor="#ef4444"
            className="z-0"
          />
          <AnimatedBeam
            containerRef={containerRef}
            fromRef={div5Ref}
            toRef={div4Ref}
            curvature={-75}
            endXOffset={22}
            endYOffset={-22}
            reverse
            gradientStartColor="#FFE01B"
            gradientStopColor="#FF4A00"
            className="z-0"
          />
          <AnimatedBeam
            containerRef={containerRef}
            fromRef={div6Ref}
            toRef={div4Ref}
            endXOffset={30}
            endYOffset={0}
            reverse
            gradientStartColor="#006BFF"
            gradientStopColor="#FF4A00"
            className="z-0"
          />
          <AnimatedBeam
            containerRef={containerRef}
            fromRef={div7Ref}
            toRef={div4Ref}
            curvature={75}
            endXOffset={22}
            endYOffset={22}
            reverse
            gradientStartColor="#F22F46"
            gradientStopColor="#FF4A00"
            className="z-0"
          />
        </div>
        
        <div className="text-center mt-8">
          <p className="text-gray-400">
            This animated beam component shows how AHA-Innovations connects all your business tools in one platform.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AnimatedBeamDemo;
