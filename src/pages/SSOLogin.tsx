import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/utils/supabaseClient';
import { User } from '@supabase/supabase-js';
import { Github, Mail, Loader2, ExternalLink } from 'lucide-react';
import SocialButton from '@/components/SocialButton';
import { generateGHLOAuthURL } from '@/utils/ghlClient';

const SSOLogin = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [signupEmail, setSignupEmail] = useState('');
  const { toast } = useToast();

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth event:', event, session?.user?.email);
      setUser(session?.user ?? null);
      
      if (event === 'SIGNED_IN' && session?.user?.email) {
        // For SSO flow, we'll handle GHL integration here
        await handleSSOIntegration(session.user);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const handleSSOIntegration = async (user: User) => {
    try {
      console.log('Starting SSO integration for user:', user.email);

      toast({
        title: "SSO Integration",
        description: "Starting GHL marketplace app authentication...",
      });

      // Step 1: Check if user exists in GHL (using Supabase Edge Function)
      const { data: ghlData, error: ghlError } = await supabase.functions.invoke('check-ghl-user-exists', {
        body: { email: user.email }
      });

      if (!ghlError && ghlData) {

        if (ghlData.exists) {
          // User exists - initiate SSO login
          toast({
            title: "Existing User Detected",
            description: "Initiating SSO authentication with your GHL account...",
          });

          // Trigger GHL OAuth flow for existing user
          await initiateGHLOAuth(user, ghlData);
        } else {
          // New user - create GHL account then SSO
          toast({
            title: "New User Setup",
            description: "Creating your GHL account and setting up SSO...",
          });

          // Create GHL account then trigger OAuth
          await createGHLAccountAndSSO(user);
        }
      }

    } catch (error) {
      console.error('SSO integration error:', error);
      toast({
        title: "SSO Error",
        description: "Failed to integrate with GHL marketplace app.",
        variant: "destructive",
      });
    }
  };

  const initiateGHLOAuth = async (user: User, ghlData?: any) => {
    try {
      console.log('Initiating GHL OAuth for user:', user.email, ghlData);

      toast({
        title: "SSO Authentication",
        description: "Redirecting to GHL for seamless authentication...",
      });

      // Check if we have OAuth credentials, otherwise use direct redirect
      try {
        const oauthURL = await generateGHLOAuthURL();
        console.log('Generated GHL OAuth URL:', oauthURL);
        window.location.href = oauthURL;
      } catch (oauthError) {
        console.log('OAuth credentials not available, using direct redirect:', oauthError);

        // Fallback: Direct redirect to GHL dashboard (like existing flow)
        if (ghlData && ghlData.locationIds && ghlData.locationIds.length > 0) {
          const dashboardUrl = `https://app.aha-innovations.com/v2/location/${ghlData.locationIds[0]}`;
          toast({
            title: "Redirecting to GHL",
            description: "Opening your GHL dashboard...",
          });
          window.open(dashboardUrl, '_blank');
        } else {
          toast({
            title: "Setup Required",
            description: "GHL OAuth credentials need to be configured for full SSO.",
            variant: "destructive",
          });
        }
      }

    } catch (error) {
      console.error('Error initiating GHL authentication:', error);
      toast({
        title: "Authentication Error",
        description: "Failed to initiate GHL authentication.",
        variant: "destructive",
      });
    }
  };

  const createGHLAccountAndSSO = async (user: User) => {
    try {
      console.log('Creating GHL account and setting up SSO for:', user.email);

      toast({
        title: "Account Creation",
        description: "Creating your GHL account...",
      });

      // Call create-ghl-account API
      const response = await fetch('/api/create-ghl-account', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: user.email,
          fullName: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User'
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('GHL account created:', result);

        toast({
          title: "Account Created!",
          description: "Now setting up SSO authentication...",
        });

        // After account creation, initiate OAuth
        setTimeout(() => {
          initiateGHLOAuth(user, result);
        }, 1500);
      } else {
        throw new Error('Failed to create GHL account');
      }

    } catch (error) {
      console.error('Error creating GHL account:', error);
      toast({
        title: "Account Creation Error",
        description: "Failed to create GHL account. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSocialSignIn = async (provider: 'github' | 'google') => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback?redirect_to=/sso-login`,
        },
      });

      if (error) throw error;
      
    } catch (error: any) {
      toast({
        title: "Sign in failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEmailSignup = async () => {
    if (!signupEmail.trim()) {
      toast({
        title: "Email required",
        description: "Please enter your email address.",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);

      // For SSO version, we'll integrate with marketplace app
      const { data, error } = await supabase.auth.signUp({
        email: signupEmail,
        password: 'temp-password-' + Math.random().toString(36).substring(2, 15),
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback?redirect_to=/sso-login`,
          data: {
            signup_method: 'email_sso',
          }
        }
      });

      if (error) throw error;

      toast({
        title: "Check your email!",
        description: "We've sent you a confirmation link for SSO setup.",
      });

      setSignupEmail('');

    } catch (error: any) {
      toast({
        title: "Signup failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      setUser(null);
      toast({
        title: "Signed out successfully",
        description: "You have been signed out.",
      });
    } catch (error: any) {
      toast({
        title: "Error signing out",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card className="bg-gray-800/50 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">SSO Login + GHL Marketplace Integration</CardTitle>
            <CardDescription className="text-gray-300">
              Test the complete SSO flow with GHL Marketplace App integration
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {!user ? (
              <div className="space-y-6">
                <div className="bg-blue-900/20 border border-blue-700 rounded-lg p-4">
                  <h3 className="text-blue-300 font-semibold mb-2">🔗 SSO Integration</h3>
                  <p className="text-blue-200 text-sm">
                    This route will use GHL Marketplace App with OAuth 2.0 for seamless SSO authentication.
                  </p>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white">Quick Email Signup (SSO Version)</h3>
                  <div className="flex gap-2">
                    <Input
                      type="email"
                      value={signupEmail}
                      onChange={(e) => setSignupEmail(e.target.value)}
                      placeholder="Enter your email for SSO setup"
                      className="bg-gray-800 border-gray-600 text-white flex-1"
                      onKeyPress={(e) => e.key === 'Enter' && handleEmailSignup()}
                    />
                    <Button
                      onClick={handleEmailSignup}
                      disabled={loading || !signupEmail.trim()}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {loading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        'Start SSO'
                      )}
                    </Button>
                  </div>
                  <p className="text-sm text-gray-400">
                    SSO version - will integrate with GHL Marketplace App
                  </p>
                </div>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t border-gray-600" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-gray-800 px-2 text-gray-400">Or continue with SSO</span>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white">Social Login (SSO Version)</h3>
                  <div className="grid gap-2">
                    <SocialButton
                      icon={Github}
                      label="Continue with GitHub (SSO)"
                      onClick={() => handleSocialSignIn('github')}
                      variant="outline"
                      className="bg-transparent border-gray-700 text-white hover:bg-gray-800"
                    />
                    <SocialButton
                      icon={Mail}
                      label="Continue with Google (SSO)"
                      onClick={() => handleSocialSignIn('google')}
                      variant="outline"
                      className="bg-transparent border-gray-700 text-white hover:bg-gray-800"
                    />
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                <div className="bg-green-900/20 border border-green-700 rounded-lg p-4">
                  <h3 className="text-green-300 font-semibold mb-2">✅ Authenticated</h3>
                  <p className="text-green-200 text-sm">
                    User: {user.email} | Provider: {user.app_metadata.provider}
                  </p>
                </div>

                <div className="bg-yellow-900/20 border border-yellow-700 rounded-lg p-4">
                  <h3 className="text-yellow-300 font-semibold mb-2">🚧 Next: GHL Marketplace App Setup</h3>
                  <p className="text-yellow-200 text-sm mb-3">
                    Now we need to configure the GHL Marketplace App for SSO integration.
                  </p>
                  <div className="text-yellow-200 text-sm space-y-1">
                    <p>1. Create GHL Marketplace App</p>
                    <p>2. Configure OAuth 2.0 External Authentication</p>
                    <p>3. Install app to your agency</p>
                    <p>4. Test SSO flow</p>
                  </div>
                </div>

                <div className="flex gap-4">
                  <Button
                    onClick={() => initiateGHLOAuth(user)}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Test GHL SSO
                  </Button>

                  <Button
                    onClick={handleSignOut}
                    variant="outline"
                    className="bg-transparent border-gray-700 text-white hover:bg-gray-800"
                  >
                    Sign Out
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SSOLogin;
