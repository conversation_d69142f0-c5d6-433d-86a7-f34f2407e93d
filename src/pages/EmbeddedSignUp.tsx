import React, { useEffect, useRef } from 'react';
import ModernHeader from '@/components/sections/ModernHeader';
import ModernFooter from '@/components/sections/ModernFooter';
import { motion } from 'framer-motion';

const EmbeddedSignUp = () => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Load the GHL form script after the component mounts
  useEffect(() => {
    // Create and load the GHL script
    const script = document.createElement("script");
    script.src = "https://link.msgsndr.com/js/form_embed.js";
    script.async = true;
    document.body.appendChild(script);

    // Add a link to our custom CSS for the GHL form
    const styleLink = document.createElement("link");
    styleLink.rel = "stylesheet";
    styleLink.href = "/ghl-form-styles.css";
    document.head.appendChild(styleLink);

    // Cleanup function to remove the script and style when component unmounts
    return () => {
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
      if (document.head.contains(styleLink)) {
        document.head.removeChild(styleLink);
      }
    };
  }, []);

  // Handle iframe transparency by injecting CSS directly into the iframe
  useEffect(() => {
    if (!iframeRef.current) return;

    // Function to inject CSS into the iframe
    const injectCSSIntoIframe = () => {
      try {
        const iframe = iframeRef.current;
        if (!iframe || !iframe.contentWindow || !iframe.contentDocument) return;

        // Create a style element
        const style = iframe.contentDocument.createElement('style');
        style.textContent = `
          body, html, div, form, .container, .row, .col, .form-box, .card, .card-body {
            background-color: transparent !important;
            background: transparent !important;
          }

          input, select, textarea {
            background-color: rgba(26, 31, 44, 0.8) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            border-radius: 8px !important;
          }

          label, p, span {
            color: white !important;
          }

          button {
            background-color: #ea384c !important;
            border: none !important;
          }

          /* Remove any white backgrounds */
          [style*="background-color: rgb(255, 255, 255)"],
          [style*="background-color:#fff"],
          [style*="background-color: #fff"],
          [style*="background-color:white"],
          [style*="background-color: white"],
          [style*="background: white"],
          [style*="background:white"],
          [style*="background: #fff"],
          [style*="background:#fff"] {
            background-color: transparent !important;
            background: transparent !important;
          }
        `;

        // Append the style to the iframe's head
        iframe.contentDocument.head.appendChild(style);

        console.log('CSS injected into iframe');
      } catch (error) {
        console.error('Error injecting CSS into iframe:', error);
      }
    };

    // Add load event listener to the iframe
    const iframe = iframeRef.current;
    iframe.addEventListener('load', injectCSSIntoIframe);

    // Try to inject CSS immediately as well
    injectCSSIntoIframe();

    // Set up an interval to try injecting CSS multiple times
    // This helps with iframes that load content dynamically
    const intervalId = setInterval(injectCSSIntoIframe, 1000);

    // Clear interval after 10 seconds
    const timeoutId = setTimeout(() => {
      clearInterval(intervalId);
    }, 10000);

    return () => {
      iframe.removeEventListener('load', injectCSSIntoIframe);
      clearInterval(intervalId);
      clearTimeout(timeoutId);
    };
  }, [iframeRef.current]);

  return (
    <div className="min-h-screen w-full bg-aha-dark text-white overflow-x-hidden">
      <ModernHeader />

      {/* Hero section with animated title */}
      <section className="pt-16 pb-8 md:pt-24 md:pb-12">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-400">
              Welcome to AHA Innovations
            </h1>
            <p className="text-gray-400 text-lg md:text-xl mb-8">
              The all-in-one platform for creators and agencies to automate their business.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Form section */}
      <section className="pb-16 md:pb-24 bg-aha-dark">
        <div className="container mx-auto px-4" style={{ backgroundColor: "transparent" }}>
          {/* Section title */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-10"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Create Your Account</h2>
            <p className="text-lg text-gray-300 max-w-2xl mx-auto">
              Join AHA Innovations and start automating your business today. Get access to all our powerful tools with a free account.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="max-w-3xl mx-auto"
          >
            {/* Form container with transparent background */}
            <div className="p-6 md:p-8 rounded-xl overflow-hidden shadow-xl relative" style={{ backgroundColor: "transparent" }} ref={containerRef}>
              {/* Subtle background pattern */}
              <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-10 z-0"></div>

              {/* Decorative elements */}
              <div className="absolute -top-20 -right-20 w-40 h-40 bg-aha-red/20 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-aha-red/20 rounded-full blur-3xl"></div>

              <div className="relative z-10" style={{ backgroundColor: "transparent" }}>
                <iframe
                ref={iframeRef}
                src="https://calendar.aha-innovations.com/widget/form/YN86bt2SlADr1BfnDEkq?backgroundColor=transparent"
                style={{
                  width: "100%",
                  height: "100%",
                  border: "none",
                  borderRadius: "8px",
                  minHeight: "500px",
                  backgroundColor: "transparent",
                  background: "transparent"
                }}
                id="inline-YN86bt2SlADr1BfnDEkq"
                data-layout="{'id':'INLINE'}"
                data-trigger-type="alwaysShow"
                data-trigger-value=""
                data-activation-type="alwaysActivated"
                data-activation-value=""
                data-deactivation-type="neverDeactivate"
                data-deactivation-value=""
                data-form-name="AHA Signup Form"
                data-height="500"
                data-layout-iframe-id="inline-YN86bt2SlADr1BfnDEkq"
                data-form-id="YN86bt2SlADr1BfnDEkq"
                data-custom-css-url="/ghl-form-styles.css"
                title="Sign Up Form"
              />
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Fallback link */}
      <section className="pb-16">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="text-center"
          >
            <p className="text-gray-400 mb-4">Having trouble with the form?</p>
            <a
              href="https://calendar.aha-innovations.com/widget/form/YN86bt2SlADr1BfnDEkq"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block px-6 py-3 rounded-lg bg-aha-red hover:bg-aha-red-dark transition-all duration-300 text-white font-medium"
            >
              Open the sign-up form directly
            </a>
            <div className="mt-8 pt-8 border-t border-gray-800">
              <p className="text-gray-400">Already have an account?</p>
              <a
                href="https://app.aha-innovations.com"
                className="mt-3 inline-block px-6 py-3 rounded-lg bg-gray-800 hover:bg-gray-700 transition-all duration-300 text-white font-medium"
              >
                Sign in to your account
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      <ModernFooter />
    </div>
  );
};

export default EmbeddedSignUp;
