import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { supabase, callEdgeFunction } from '@/utils/supabaseClient';
import { Loader2, Building2, Users, MapPin, Phone, User, Navigation } from 'lucide-react';
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';

const Onboarding = () => {
  const [loading, setLoading] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    companyName: '',
    phone: '',
    city: '',
    state: '',
    country: 'US', // Use 2-letter code for GHL API compatibility
    role: '',
    company_size: '',
    referral_source: ''
  });
  const [locationLoading, setLocationLoading] = useState(false);
  const [phoneCountry, setPhoneCountry] = useState<string>('US');
  const [accountCreated, setAccountCreated] = useState(false);

  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    checkUser();
    // Auto-detect country for phone input on component mount
    detectInitialCountry();
  }, []);

  const detectInitialCountry = async () => {
    try {
      // Try to get country from IP geolocation (fallback method)
      const response = await fetch('https://ipapi.co/json/');
      if (response.ok) {
        const data = await response.json();
        if (data.country_code) {
          setPhoneCountry(data.country_code);
        }
      }
    } catch (error) {
      console.log('Could not detect initial country, using default US');
    }
  };

  const checkUser = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        window.location.href = 'https://app.aha-innovations.com';
        return;
      }
      
      setUser(session.user);
      
      // Pre-fill form with user data if available
      const userData = session.user.user_metadata;
      const fullName = userData?.full_name || userData?.name || '';
      const nameParts = fullName.split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      setFormData(prev => ({
        ...prev,
        firstName,
        lastName,
        companyName: userData?.company_name || `${firstName || 'My'} Business`
      }));
      
    } catch (error) {
      console.error('Error checking user:', error);
      window.location.href = 'https://app.aha-innovations.com';
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const requestLocation = async () => {
    if (!navigator.geolocation) {
      toast({
        title: "Geolocation not supported",
        description: "Your browser doesn't support geolocation.",
        variant: "destructive"
      });
      return;
    }

    setLocationLoading(true);

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const { latitude, longitude } = position.coords;

          // Use reverse geocoding to get address details
          const response = await fetch(
            `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`
          );

          if (response.ok) {
            const data = await response.json();



            const countryCode = data.countryCode || 'US';

            // Update both form data and phone country with 2-letter code
            setFormData(prev => ({
              ...prev,
              city: data.city || data.locality || '',
              state: data.principalSubdivision || '',
              country: countryCode // Use 2-letter code directly for GHL API
            }));

            // Set phone country for PhoneInput component
            setPhoneCountry(countryCode);

            toast({
              title: "Location detected",
              description: `Found: ${data.city}, ${data.principalSubdivision}, ${countryCode}`,
            });
          }
        } catch (error) {
          console.error('Error getting location details:', error);
          toast({
            title: "Location error",
            description: "Could not get location details. Please enter manually.",
            variant: "destructive"
          });
        } finally {
          setLocationLoading(false);
        }
      },
      (error) => {
        console.error('Geolocation error:', error);
        toast({
          title: "Location access denied",
          description: "Please allow location access or enter your location manually.",
          variant: "destructive"
        });
        setLocationLoading(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.firstName.trim() || !formData.lastName.trim() || !formData.companyName.trim()) {
      toast({
        title: "Missing required fields",
        description: "Please fill in your first name, last name, and company name.",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    
    try {
      // Create GHL account with complete workflow
      const userData = user?.user_metadata || {};
      // Ensure required fields have defaults for GHL API
      const ghlPayload = {
        email: user.email,
        firstName: formData.firstName || userData.full_name?.split(' ')[0] || userData.name?.split(' ')[0] || user.email.split('@')[0],
        lastName: formData.lastName || userData.full_name?.split(' ').slice(1).join(' ') || userData.name?.split(' ').slice(1).join(' ') || '',
        companyName: formData.companyName,
        phone: formData.phone || '+**********', // Default phone for GHL if empty
        city: formData.city || 'New York', // Default city for GHL if empty
        state: formData.state || 'NY', // Default state for GHL if empty
        country: formData.country || 'US', // Default country for GHL if empty (2-letter code)
        role: formData.role,
        company_size: formData.company_size,
        referral_source: formData.referral_source,
        password: 'AhaDefault*2025' // Default password as per n8n workflow
      };

      const ghlData = await callEdgeFunction('create-ghl-full-account', ghlPayload);

      // Update user profile with GHL data (all columns should now exist after migration)
      const { error: updateError } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          email: user.email,
          first_name: formData.firstName,
          last_name: formData.lastName,
          full_name: `${formData.firstName} ${formData.lastName}`.trim(),
          company_name: formData.companyName,
          phone: formData.phone,
          city: formData.city || 'New York',
          state: formData.state || 'NY',
          country: formData.country || 'US',
          role: formData.role,
          company_size: formData.company_size,
          referral_source: formData.referral_source,
          ghl_location_id: ghlData.locationId,
          ghl_user_id: ghlData.userId,
          stripe_customer_id: ghlData.stripeCustomerId,
          onboarding_completed: true,
          updated_at: new Date().toISOString(),
        });

      if (updateError) {
        throw updateError;
      }

      toast({
        title: "Account Created Successfully! 🎉",
        description: "Your AHA-Innovations account has been created. Please check your email for activation instructions.",
      });

      // Show success message instead of redirecting
      setAccountCreated(true);
      
    } catch (error: any) {
      console.error('Onboarding error:', error);
      toast({
        title: "Setup Error",
        description: error.message || "Failed to complete account setup. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  // Show success screen after account creation
  if (accountCreated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-gray-800/50 border-gray-700">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl text-green-400">Account Created Successfully! 🎉</CardTitle>
            <CardDescription className="text-gray-300">
              Your AHA-Innovations account has been set up
            </CardDescription>
          </CardHeader>

          <CardContent className="text-center space-y-4">
            <div className="bg-green-900/20 border border-green-700 rounded-lg p-4">
              <h3 className="text-green-400 font-semibold mb-2">Next Steps:</h3>
              <p className="text-gray-300 text-sm">
                Please check your email <strong>({user.email})</strong> for activation instructions from AHA-Innovations.
              </p>
            </div>

            <div className="bg-blue-900/20 border border-blue-700 rounded-lg p-4">
              <h3 className="text-blue-400 font-semibold mb-2">What's Included:</h3>
              <ul className="text-gray-300 text-sm text-left space-y-1">
                <li>• Full AHA-Innovations CRM access</li>
                <li>• Premium features enabled</li>
                <li>• Automated workflows</li>
                <li>• Complete marketing suite</li>
              </ul>
            </div>

            <Button
              onClick={() => window.location.href = 'mailto:' + user.email}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              Open Email App
            </Button>

            <p className="text-gray-400 text-xs">
              Didn't receive the email? Check your spam folder or contact support.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-gray-800/50 border-gray-700">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl text-white">Welcome to AHA Innovations!</CardTitle>
          <CardDescription className="text-gray-300">
            Let's set up your GoHighLevel account with just a few details
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="text-white flex items-center gap-2">
                  <User className="h-4 w-4" />
                  First Name *
                </Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  placeholder="John"
                  className="bg-gray-700 border-gray-600 text-white"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName" className="text-white flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Last Name *
                </Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  placeholder="Doe"
                  className="bg-gray-700 border-gray-600 text-white"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="companyName" className="text-white flex items-center gap-2">
                <Building2 className="h-4 w-4" />
                Company Name *
              </Label>
              <Input
                id="companyName"
                value={formData.companyName}
                onChange={(e) => handleInputChange('companyName', e.target.value)}
                placeholder="Your Company Name"
                className="bg-gray-700 border-gray-600 text-white"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone" className="text-white flex items-center gap-2">
                <Phone className="h-4 w-4" />
                Phone Number *
              </Label>
              <PhoneInput
                value={formData.phone}
                onChange={(value) => handleInputChange('phone', value || '')}
                defaultCountry={phoneCountry as any}
                placeholder="Enter phone number"
                className="bg-gray-700 border-gray-600 text-white [&>input]:bg-gray-700 [&>input]:border-gray-600 [&>input]:text-white [&>input]:placeholder-gray-400"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-white flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Location
                </Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={requestLocation}
                  disabled={locationLoading}
                  className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600"
                >
                  {locationLoading ? (
                    <>
                      <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                      Getting Location...
                    </>
                  ) : (
                    <>
                      <Navigation className="mr-2 h-3 w-3" />
                      Auto-fill Location
                    </>
                  )}
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city" className="text-white">
                  City
                </Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  placeholder="New York"
                  className="bg-gray-700 border-gray-600 text-white"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="state" className="text-white">State/Province</Label>
                <Input
                  id="state"
                  value={formData.state}
                  onChange={(e) => handleInputChange('state', e.target.value)}
                  placeholder="California"
                  className="bg-gray-700 border-gray-600 text-white"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="country" className="text-white">Country</Label>
              <Select value={formData.country} onValueChange={(value) => handleInputChange('country', value)}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Select country" />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  <SelectItem value="US">United States</SelectItem>
                  <SelectItem value="CA">Canada</SelectItem>
                  <SelectItem value="GB">United Kingdom</SelectItem>
                  <SelectItem value="AU">Australia</SelectItem>
                  <SelectItem value="DE">Germany</SelectItem>
                  <SelectItem value="FR">France</SelectItem>
                  <SelectItem value="IT">Italy</SelectItem>
                  <SelectItem value="ES">Spain</SelectItem>
                  <SelectItem value="NL">Netherlands</SelectItem>
                  <SelectItem value="SE">Sweden</SelectItem>
                  <SelectItem value="NO">Norway</SelectItem>
                  <SelectItem value="DK">Denmark</SelectItem>
                  <SelectItem value="FI">Finland</SelectItem>
                  <SelectItem value="PL">Poland</SelectItem>
                  <SelectItem value="JP">Japan</SelectItem>
                  <SelectItem value="KR">South Korea</SelectItem>
                  <SelectItem value="SG">Singapore</SelectItem>
                  <SelectItem value="HK">Hong Kong</SelectItem>
                  <SelectItem value="IN">India</SelectItem>
                  <SelectItem value="BR">Brazil</SelectItem>
                  <SelectItem value="MX">Mexico</SelectItem>
                  <SelectItem value="AR">Argentina</SelectItem>
                  <SelectItem value="CL">Chile</SelectItem>
                  <SelectItem value="CO">Colombia</SelectItem>
                  <SelectItem value="PE">Peru</SelectItem>
                  <SelectItem value="ZA">South Africa</SelectItem>
                  <SelectItem value="NG">Nigeria</SelectItem>
                  <SelectItem value="KE">Kenya</SelectItem>
                  <SelectItem value="EG">Egypt</SelectItem>
                  <SelectItem value="MA">Morocco</SelectItem>
                  <SelectItem value="PH">Philippines</SelectItem>
                  <SelectItem value="ID">Indonesia</SelectItem>
                  <SelectItem value="TH">Thailand</SelectItem>
                  <SelectItem value="MY">Malaysia</SelectItem>
                  <SelectItem value="VN">Vietnam</SelectItem>
                  <SelectItem value="OTHER">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="role" className="text-white flex items-center gap-2">
                <Users className="h-4 w-4" />
                Your Role
              </Label>
              <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value)}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Select your role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="business_owner">Business Owner</SelectItem>
                  <SelectItem value="marketing_manager">Marketing Manager</SelectItem>
                  <SelectItem value="sales_manager">Sales Manager</SelectItem>
                  <SelectItem value="consultant">Consultant</SelectItem>
                  <SelectItem value="agency_owner">Agency Owner</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="company_size" className="text-white">Company Size</Label>
              <Select value={formData.company_size} onValueChange={(value) => handleInputChange('company_size', value)}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Select company size" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">Just me</SelectItem>
                  <SelectItem value="2-10">2-10 employees</SelectItem>
                  <SelectItem value="11-50">11-50 employees</SelectItem>
                  <SelectItem value="51-200">51-200 employees</SelectItem>
                  <SelectItem value="200+">200+ employees</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button 
              type="submit" 
              className="w-full bg-red-600 hover:bg-red-700 text-white"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Your Account...
                </>
              ) : (
                'Complete Setup & Create AHA Account'
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default Onboarding;
