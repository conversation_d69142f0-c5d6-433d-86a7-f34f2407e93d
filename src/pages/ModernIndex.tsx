import React from 'react';
import HeroHeader from '../components/sections/HeroHeader';
import ModernHero from '../components/sections/ModernHero';
import TrustedCompanies from '../components/sections/TrustedCompanies';
import PainPoints from '../components/sections/PainPoints';
import ModernFeatures from '../components/sections/ModernFeatures';
import IdealCustomers from '../components/sections/IdealCustomers';
import ModernShowcase from '../components/sections/ModernShowcase';
import EnhancedTestimonials from '../components/sections/EnhancedTestimonials';
import { PricingSectionDemo } from '../components/demo/pricing-section-demo';
import ModernCTA from '../components/sections/ModernCTA';
import ModernFooter from '../components/sections/ModernFooter';
import AnnouncementBanner from '../components/ui/announcement-banner';

const ModernIndex: React.FC = () => {
  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden bg-black relative">
      {/* Hero-style background for entire page */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Dot pattern texture overlay */}
        <div
          className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, white 1px, transparent 0)`,
            backgroundSize: '32px 32px',
            maskImage: 'radial-gradient(800px circle at center, white, transparent)'
          }}
        />

        {/* Soft ambient glow from upper left */}
        <div
          className="absolute -top-[25%] -left-[25%] w-[100%] h-[100%]"
          style={{
            background: `radial-gradient(ellipse 800px 600px at 25% 25%,
              rgba(255, 255, 255, 0.08) 0%,
              rgba(255, 255, 255, 0.04) 30%,
              rgba(255, 255, 255, 0.02) 50%,
              transparent 70%)`
          }}
        />

        {/* Main background gradients */}
        <div
          className="absolute top-1/4 left-1/4 w-64 md:w-96 h-64 md:h-96 bg-gradient-to-br from-aha-red to-transparent rounded-full blur-3xl opacity-[0.05]"
        />

        <div
          className="absolute bottom-1/4 right-1/4 w-64 md:w-96 h-64 md:h-96 bg-gradient-to-tr from-aha-red to-transparent rounded-full blur-3xl opacity-[0.05]"
        />

        <div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[120%] h-[120%] bg-gradient-radial from-aha-red via-transparent to-transparent opacity-[0.1]"
        />

        {/* Additional animated elements */}
        <div
          className="absolute -top-[30%] -right-[10%] w-[70%] h-[70%] rounded-full bg-gradient-to-r from-aha-red/30 via-aha-red/10 to-blue-500/5 blur-[120px] opacity-[0.4] animate-spin-slow"
          style={{ animationDuration: '40s' }}
        />

        <div
          className="absolute -bottom-[30%] -left-[10%] w-[70%] h-[70%] rounded-full bg-gradient-to-r from-blue-500/5 via-aha-red/10 to-aha-red/30 blur-[120px] opacity-[0.4] animate-spin-slow"
          style={{ animationDuration: '50s', animationDirection: 'reverse' }}
        />
      </div>

      <HeroHeader />

      {/* All content sections with relative z-index */}
      <div className="relative z-10">
        {/* 1. Hero Section - Clear value proposition */}
        <ModernHero
          title="All-in-One Business Platform"
          subtitle={
            <a
              href="https://www.millennialbusinessinnovations.com/"
              target="_blank"
              rel="noopener noreferrer"
              className="outline-none"
            >
              Need custom solutions? Visit Millennial Business Innovations
            </a>
          }
          description="Replace 10+ tools with one simple platform. Grow faster, work smarter."
          ctaText="Get Started Free"
          ctaLink="/signup"
        />

        {/* 2. Social Proof - Build trust early */}
        <TrustedCompanies />

        {/* 3. Problem/Solution - Combined pain points with solution */}
        <PainPoints />
        <ModernFeatures />

        {/* 4. Target Customers - Show who it's for */}
        <IdealCustomers />

        {/* 5. Product Demo - Show don't tell */}
        <ModernShowcase />

        {/* 6. Testimonials - Customer success stories */}
        <EnhancedTestimonials />

        {/* 7. Pricing Preview - Simple pricing overview */}
        <section className="py-20 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-aha-red/3 to-transparent"></div>
          <div className="container mx-auto px-4 relative z-10">
            <PricingSectionDemo />
          </div>
        </section>

        {/* 8. Final CTA - Clear next step */}
        <ModernCTA />

        <ModernFooter />
      </div>
    </div>
  );
};

export default ModernIndex;
