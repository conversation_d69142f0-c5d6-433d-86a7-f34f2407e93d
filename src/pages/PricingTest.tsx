import React from 'react'
import { PricingSection } from "@/components/ui/pricing-section"

// Updated pricing data with Stripe links
const PAYMENT_FREQUENCIES = ["monthly", "yearly"]

const TIERS_WITH_STRIPE = [
  {
    id: "free",
    name: "Free",
    price: {
      monthly: "$0",
      yearly: "$0",
    },
    description: "Perfect for getting started",
    features: [
      "20 Contacts",
      "10 Users/Teammates", 
      "Social Planner",
      "Email Support",
      "Basic Analytics",
    ],
    cta: "Get Started Free",
  },
  {
    id: "basic",
    name: "Basic Plan",
    price: {
      monthly: 5,
      yearly: 4.58,
    },
    description: "Perfect for freelancers",
    features: [
      "15 Pipelines",
      "Unlimited Sales Funnel",
      "1,000 Email Marketing",
      "Unlimited Contacts",
      "Basic Automation",
    ],
    cta: "Start Free Trial",
    stripeLinks: {
      monthly: "https://buy.stripe.com/bJe5kE7Za5Ea9XZ37U4801F",
      yearly: "https://buy.stripe.com/3cI14o7Za0jQgmndMy4801G",
    },
  },
  {
    id: "agency",
    name: "Agency Plan",
    price: {
      monthly: 25,
      yearly: 23.75,
    },
    description: "Ideal for small agencies",
    features: [
      "50 Pipelines",
      "Unlimited Sales Funnel",
      "7,500 Email Marketing",
      "Workflow Triggers & Actions",
      "Advanced Analytics",
    ],
    cta: "Start Free Trial",
    popular: true,
    stripeLinks: {
      monthly: "https://buy.stripe.com/fZu3cw7Za0jQ6LN37U4801H",
      yearly: "https://buy.stripe.com/00wbJ26V6aYuc677oa4801L",
    },
  },
  {
    id: "enterprise",
    name: "Enterprise Plan",
    price: {
      monthly: 5,
      yearly: 4.58,
    },
    originalPrice: {
      monthly: 39,
      yearly: 37.50,
    },
    description: "Recommended for business",
    limitedOffer: "Limited offer, get Enterprise for the price of Basic!",
    features: [
      "Unlimited Pipelines",
      "Unlimited Sales Funnel",
      "15,000 Email Marketing",
      "Reputation Management",
      "Priority Support",
    ],
    cta: "Start Free Trial",
    highlighted: true,
    stripeLinks: {
      monthly: "https://buy.stripe.com/4gM3cwenyeaGfijbEq4801O",
      yearly: "https://buy.stripe.com/4gM5kEcfq5Ea4DF8se4801P",
    },
  },
]

const PricingTest = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-aha-red/10 via-transparent to-transparent"></div>
      
      <div className="relative z-10 container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-white mb-4 font-gotham">
            Pricing Test with Stripe Integration
          </h1>
          <p className="text-xl text-white/80 font-gotham">
            Test the new dual-button pricing cards
          </p>
        </div>

        <PricingSection
          title="Choose Your Plan"
          subtitle="Free plan stays free, paid plans get dual buttons: Start Free Trial + Buy Now"
          frequencies={PAYMENT_FREQUENCIES}
          tiers={TIERS_WITH_STRIPE}
        />

        <div className="mt-16 text-center">
          <div className="bg-black/20 backdrop-blur-sm border border-white/10 rounded-xl p-8 max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-white mb-4 font-gotham">How It Works</h2>
            <div className="grid md:grid-cols-3 gap-6 text-left">
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-aha-red font-gotham">Free Plan</h3>
                <p className="text-white/80 text-sm">
                  Single "Start Free" button → Routes to /signup for lead collection
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-aha-red font-gotham">Paid Plans</h3>
                <p className="text-white/80 text-sm">
                  Two buttons: "Start Free Trial" (→ /signup) + "Buy Now" (→ Stripe checkout)
                </p>
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-aha-red font-gotham">Enterprise</h3>
                <p className="text-white/80 text-sm">
                  "Contact Us" (→ Calendar popup) + "Buy Now" (→ Stripe) for immediate purchase option
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PricingTest
