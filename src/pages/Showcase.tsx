import React from 'react';
import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink, ArrowLeft } from 'lucide-react';
import Tilt3D from '@/components/anim/Tilt3D';
import ModernHeader from '@/components/sections/ModernHeader';
import ModernFooter from '@/components/sections/ModernFooter';
import EnhancedTestimonials from '@/components/sections/EnhancedTestimonials';
import { useNavigate } from 'react-router-dom';

// Project interface
interface Project {
  name: string;
  url: string;
  domain: string;
  image: string;
  description?: string;
}

// Project card component
const ProjectCard: React.FC<{ project: Project; index: number }> = ({ project, index }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.05 }}
      viewport={{ once: true, margin: "-50px" }}
      className="flex flex-col h-full"
    >
      <Tilt3D className="h-full" intensity={5}>
        <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden h-full transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/5 hover:border-aha-red/20 group">
          {/* Browser mockup header */}
          <div className="bg-black/50 backdrop-blur-sm h-8 flex items-center px-4">
            <div className="flex gap-2">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
            </div>
            <div className="text-xs text-white/70 mx-auto truncate max-w-[150px] md:max-w-[200px]">{project.domain}</div>
          </div>

          {/* Website screenshot */}
          <div className="relative overflow-hidden">
            <img
              src={project.image}
              alt={project.name}
              className="w-full aspect-[16/10] object-cover object-top transition-transform duration-500 group-hover:scale-105"
            />

            {/* Overlay on hover */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-between p-4">
              <span className="text-white font-medium">{project.name}</span>
              <Button
                size="icon"
                variant="ghost"
                className="h-8 w-8 rounded-full bg-white/20 hover:bg-white/30 text-white"
                onClick={() => window.open(project.url, '_blank')}
              >
                <ExternalLink size={14} />
              </Button>
            </div>
          </div>

          {/* Site name and domain */}
          <div className="p-4">
            <h4 className="font-bold text-white">{project.name}</h4>
            <p className="text-sm text-gray-400 mb-2">{project.domain}</p>
            {project.description && (
              <p className="text-sm text-gray-300">{project.description}</p>
            )}
            <div className="mt-4">
              <Button
                variant="outline"
                size="sm"
                className="text-xs border-white/10 hover:bg-white/10 text-white"
                onClick={() => window.open(project.url, '_blank')}
              >
                <ExternalLink size={12} className="mr-1" />
                Visit Site
              </Button>
            </div>
          </div>
        </div>
      </Tilt3D>
    </motion.div>
  );
};

const Showcase: React.FC = () => {
  const navigate = useNavigate();

  // Projects data
  const projects: Project[] = [
    {
      name: "Launchpad Website Craft",
      url: "https://launchpad-website-craft.vercel.app/",
      domain: "launchpad-website-craft.vercel.app",
      image: "/launchpad.jpg",
      description: "A modern website builder platform with drag-and-drop functionality."
    },
    {
      name: "Image Source Finder",
      url: "https://image-source-finder.vercel.app/",
      domain: "image-source-finder.vercel.app",
      image: "/image-source-finder.jpg",
      description: "Tool for finding original sources of images across the web."
    },
    {
      name: "TimePiece",
      url: "https://timepiece.site/",
      domain: "timepiece.site",
      image: "/timepiece.jpg",
      description: "Elegant time management and productivity application."
    },
    {
      name: "Millennial Business Academy",
      url: "https://start.millennialbusinessacademy.net/",
      domain: "start.millennialbusinessacademy.net",
      image: "/millennial.jpg",
      description: "Educational platform for modern business strategies."
    },
    {
      name: "Millennial Business Innovations",
      url: "https://millennialbusinessinnovations.com/",
      domain: "millennialbusinessinnovations.com",
      image: "/MilllennialBusinessAcademy.png",
      description: "Business consulting and innovation services."
    },
    {
      name: "AHA Innovations",
      url: "https://aha-innovations.com/",
      domain: "aha-innovations.com",
      image: "/aha-innovations.jpg",
      description: "All-in-one business management platform."
    },
    {
      name: "RR Twins",
      url: "https://rrtwins.com/",
      domain: "rrtwins.com",
      image: "/rrtwins.jpg",
      description: "Personal brand and content creation platform."
    },
    {
      name: "Undertake PH",
      url: "https://undertakeph.com/",
      domain: "undertakeph.com",
      image: "/undertake.jpg",
      description: "Business services and solutions provider in the Philippines."
    },
    {
      name: "Stephen Lovino Portfolio",
      url: "https://www.stephenlovino.com/",
      domain: "stephenlovino.com",
      image: "/StephenLovino.png",
      description: "Professional portfolio showcasing development projects and skills."
    }
  ];

  return (
    <div className="min-h-screen w-full bg-aha-dark text-white overflow-x-hidden">
      <ModernHeader />

      <section className="pt-32 pb-24 relative overflow-hidden" style={{ background: '#0F0F0F' }}>
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/3 left-0 w-1/2 h-1/2 bg-gradient-to-r from-aha-red/5 via-blue-500/5 to-transparent rounded-full blur-[150px]" />
          <div className="absolute bottom-1/3 right-0 w-1/2 h-1/2 bg-gradient-to-l from-aha-red/5 via-blue-500/5 to-transparent rounded-full blur-[150px]" />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <Button
            variant="ghost"
            className="mb-8 text-white hover:bg-white/10 group flex items-center"
            onClick={() => navigate('/')}
          >
            <ArrowLeft className="mr-2 h-4 w-4 transition-transform group-hover:-translate-x-1" />
            Back to Home
          </Button>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4 mx-auto block w-fit">
              Customer Showcase
            </Badge>
            <h1 className="text-3xl md:text-5xl font-bold mb-6">
              See what we help our <span className="text-aha-red">customers build</span>
            </h1>
            <p className="text-center text-gray-300 max-w-3xl mx-auto">
              Discover the diverse range of projects and ideas that our customers have built with AHA-Innovations.
              From simple landing pages to complex business applications, our platform powers it all.
            </p>
          </motion.div>

          {/* Projects grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-24">
            {projects.map((project, index) => (
              <ProjectCard key={index} project={project} index={index} />
            ))}
          </div>
        </div>
      </section>

      <EnhancedTestimonials />

      <ModernFooter />
    </div>
  );
};

export default Showcase;
