import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { supabase, callEdgeFunction } from '@/utils/supabaseClient';
import { Github, Mail, Loader2, CheckCircle, XCircle } from 'lucide-react';
import SocialButton from '@/components/SocialButton';
import GHLSSOService from '../services/ghlSSOService';
import GHLSSOTest from '@/components/GHLSSOTest';

const TestSocialLogin = () => {
  const [user, setUser] = useState<any>(null);
  const [profile, setProfile] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<any[]>([]);
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [signupEmail, setSignupEmail] = useState('');
  const [userLocation, setUserLocation] = useState<{
    city?: string;
    state?: string;
    country?: string;
    latitude?: number;
    longitude?: number;
  } | null>(null);
  const { toast } = useToast();

  // Function to get user's geolocation
  const getUserLocation = async (): Promise<{
    city?: string;
    state?: string;
    country?: string;
    latitude?: number;
    longitude?: number;
  }> => {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        console.log('Geolocation not supported');
        resolve({});
        return;
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;

          try {
            // Use reverse geocoding to get address details
            const response = await fetch(
              `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`
            );
            const data = await response.json();

            resolve({
              city: data.city || data.locality || '',
              state: data.principalSubdivision || '',
              country: data.countryName || '',
              latitude,
              longitude
            });
          } catch (error) {
            console.error('Error getting location details:', error);
            resolve({ latitude, longitude });
          }
        },
        (error) => {
          console.log('Geolocation error:', error);
          resolve({});
        },
        { timeout: 10000, enableHighAccuracy: false }
      );
    });
  };

  // Function to generate user details from email
  const generateUserDetailsFromEmail = (email: string) => {
    const emailParts = email.split('@');
    const username = emailParts[0];

    // Try to extract first and last name from username
    const nameParts = username.split(/[._-]/);
    const firstName = nameParts[0] || username;
    const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : 'User';

    // Capitalize first letters
    const capitalizedFirstName = firstName.charAt(0).toUpperCase() + firstName.slice(1).toLowerCase();
    const capitalizedLastName = lastName.charAt(0).toUpperCase() + lastName.slice(1).toLowerCase();

    const companyName = `${capitalizedFirstName}'s Business`;

    return {
      firstName: capitalizedFirstName,
      lastName: capitalizedLastName,
      companyName
    };
  };

  useEffect(() => {
    checkUser();
    
    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session);
      if (session) {
        setUser(session.user);
        await fetchProfile(session.user.id);
        // Populate form fields
        const userData = session.user.user_metadata || {};
        setFullName(userData.full_name || userData.name || '');
        setEmail(session.user.email || '');

        // Handle different sign-in scenarios
        if (event === 'SIGNED_IN' && session.user.email) {
          const signupMethod = session.user.user_metadata?.signup_method;

          if (signupMethod === 'email_only') {
            // This is a new user who signed up via email - create GHL account
            console.log('New email signup detected, creating GHL account...');
            await handleCompleteGHLAccountCreation(session.user.email, session.user.id);
          } else {
            // This is a social login - check if user exists in GHL
            await checkGHLUserAndRedirect(session.user.email, session.user.id);
          }
        }
      } else {
        setUser(null);
        setProfile(null);
        setFullName('');
        setEmail('');
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const checkUser = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        setUser(session.user);
        await fetchProfile(session.user.id);
        // Populate form fields
        const userData = session.user.user_metadata || {};
        setFullName(userData.full_name || userData.name || '');
        setEmail(session.user.email || '');

        // Check if user exists in GHL on page load
        if (session.user.email) {
          await checkGHLUserAndRedirect(session.user.email, session.user.id);
        }
      }
    } catch (error) {
      console.error('Error checking user:', error);
    }
  };

  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      setProfile(data);
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };

  const checkGHLUserAndRedirect = async (email: string, supabaseUserId?: string) => {
    try {
      console.log('Checking if user exists in GHL via SSO service:', email);

      // Use the new GHL SSO service to check for existing users
      const result = await GHLSSOService.checkUserExists(email, supabaseUserId);

      if (result.exists && result.user) {
        console.log('User exists in GHL, redirecting to dashboard:', result);

        toast({
          title: "Welcome back!",
          description: `Found your existing GHL account. Redirecting to dashboard...`,
        });

        // For existing users, authenticate and redirect to their dashboard
        const authResult = await GHLSSOService.authenticateExistingUser(
          email,
          supabaseUserId || user?.id || ''
        );

        if (authResult.success && authResult.dashboardUrl) {
          // Redirect after a short delay to show the toast
          setTimeout(() => {
            window.open(authResult.dashboardUrl, '_blank');
          }, 2000);
        } else {
          // Fallback to basic dashboard URL
          const dashboardUrl = result.locationIds && result.locationIds.length > 0
            ? `https://app.aha-innovations.com/v2/location/${result.locationIds[0]}`
            : 'https://app.aha-innovations.com/v2/dashboard';

          setTimeout(() => {
            window.open(dashboardUrl, '_blank');
          }, 2000);
        }

        return true; // User exists, handled
      } else {
        console.log('User does not exist in GHL, continuing with signup flow');
        return false; // User doesn't exist, continue with signup
      }
    } catch (error) {
      console.error('Error checking GHL user:', error);
      toast({
        title: "Error",
        description: "Failed to check existing account. Please try again.",
        variant: "destructive",
      });
      return false;
    }
  };

  const handleSocialSignIn = async (provider: 'github' | 'google') => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback?redirect_to=${encodeURIComponent('/test-social-login')}`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent'
          }
        },
      });

      if (error) throw error;

      toast({
        title: "Redirecting...",
        description: `Redirecting to ${provider} for authentication.`,
      });

    } catch (error: any) {
      toast({
        title: "Error with social login",
        description: error.message || `Failed to sign in with ${provider}.`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const testGHLCredentials = async () => {
    setLoading(true);
    const results: any[] = [];

    try {
      results.push({ step: 'Testing GHL Credentials', status: 'loading', timestamp: new Date() });

      const credentialsResponse = await callEdgeFunction('get-ghl-credentials', {});
      console.log('Credentials response:', credentialsResponse);

      results.push({
        step: 'GHL Credentials Retrieved',
        status: 'success',
        data: {
          hasClientId: !!credentialsResponse.clientId,
          hasClientSecret: !!credentialsResponse.clientSecret,
          hasAgencyToken: !!credentialsResponse.agencyToken,
          hasCompanyId: !!credentialsResponse.companyId,
        },
        timestamp: new Date()
      });

    } catch (error: any) {
      console.error('Credentials test error:', error);
      results.push({
        step: 'GHL Credentials Test Failed',
        status: 'error',
        error: error.message || 'Unknown error occurred',
        details: error,
        timestamp: new Date()
      });

      toast({
        title: "Credentials Test Failed",
        description: error.message || "Could not retrieve GHL credentials.",
        variant: "destructive",
      });
    } finally {
      setTestResults(results);
      setLoading(false);
    }
  };

  const createGHLAccountAutomatically = async (userEmail: string) => {
    setLoading(true);
    const results: any[] = [];

    try {
      results.push({ step: 'Getting user location...', status: 'loading', timestamp: new Date() });
      setTestResults([...results]);

      // Get user's location
      const location = await getUserLocation();
      setUserLocation(location);

      results.push({
        step: 'Location obtained',
        status: 'success',
        data: location,
        timestamp: new Date()
      });
      setTestResults([...results]);

      // Generate user details from email
      const userDetails = generateUserDetailsFromEmail(userEmail);

      results.push({
        step: 'Generated user details from email',
        status: 'success',
        data: userDetails,
        timestamp: new Date()
      });
      setTestResults([...results]);

      // Check if user already exists in GHL
      results.push({ step: 'Checking if user exists in GHL...', status: 'loading', timestamp: new Date() });
      setTestResults([...results]);

      const existingUserCheck = await callEdgeFunction('check-ghl-user-exists', { email: userEmail });
      console.log('Existing user check:', existingUserCheck);

      if (existingUserCheck?.exists) {
        results.push({
          step: 'User already exists in GHL',
          status: 'info',
          data: existingUserCheck,
          timestamp: new Date()
        });
        setTestResults([...results]);

        toast({
          title: "Account Already Exists",
          description: "Your GHL account is already set up. You should receive login details via email.",
          variant: "default",
        });
        return;
      }

      results.push({
        step: 'User does not exist, creating GHL account...',
        status: 'loading',
        timestamp: new Date()
      });
      setTestResults([...results]);

      // Create GHL account with generated details
      const ghlAccountData = {
        email: userEmail,
        firstName: userDetails.firstName,
        lastName: userDetails.lastName,
        companyName: userDetails.companyName,
        phone: '+**********', // Default phone number
        city: location.city || 'Default City',
        state: location.state || 'CA',
        country: location.country || 'United States',
        role: 'business_owner',
        company_size: '1-10',
        referral_source: 'social_login',
        password: `${userDetails.firstName}123!` // Generate a simple password
      };

      console.log('Creating GHL account with data:', ghlAccountData);

      const ghlResponse = await callEdgeFunction('create-ghl-full-account', ghlAccountData);
      console.log('GHL account creation response:', ghlResponse);

      results.push({
        step: 'GHL Account Created Successfully!',
        status: 'success',
        data: ghlResponse,
        timestamp: new Date()
      });
      setTestResults([...results]);

      toast({
        title: "Account Created Successfully!",
        description: "Your GHL account has been created. Check your email for login details.",
        variant: "default",
      });

    } catch (error: any) {
      console.error('Auto GHL account creation error:', error);
      results.push({
        step: 'GHL Account Creation Failed',
        status: 'error',
        error: error.message || 'Unknown error occurred',
        details: error,
        timestamp: new Date()
      });
      setTestResults([...results]);

      toast({
        title: "Account Creation Failed",
        description: error.message || "Failed to create GHL account automatically.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const testGHLIntegration = async () => {
    if (!user) {
      toast({
        title: "Please sign in first",
        description: "You need to be authenticated to test GHL integration.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    const results: any[] = [];

    try {
      // Test Edge Function call
      results.push({ step: 'Starting GHL Integration Test', status: 'info', timestamp: new Date() });

      // First check if user already exists in GHL
      results.push({ step: 'Checking if user exists in GHL', status: 'loading', timestamp: new Date() });

      const existingUserCheck = await callEdgeFunction('check-ghl-user-exists', { email: user.email });
      console.log('Existing user check response:', existingUserCheck);

      if (existingUserCheck?.exists) {
        results.push({ step: 'User Already Exists in GHL', status: 'success', data: existingUserCheck, timestamp: new Date() });

        // Update profile with existing GHL data
        const { error: updateError } = await supabase
          .from('profiles')
          .upsert({
            id: user.id,
            email: user.email,
            ghl_location_id: existingUserCheck.locationId,
            ghl_user_id: existingUserCheck.userId,
            onboarding_completed: true,
            updated_at: new Date().toISOString(),
          });

        if (updateError) {
          results.push({ step: 'Profile Update Failed', status: 'error', error: updateError, timestamp: new Date() });
        } else {
          results.push({ step: 'Profile Updated with Existing GHL Data', status: 'success', timestamp: new Date() });
          await fetchProfile(user.id); // Refresh profile data
        }

        // Redirect to GHL dashboard
        const ghlDashboardUrl = `https://app.gohighlevel.com/location/${existingUserCheck.locationId}/dashboard`;
        results.push({ step: 'Redirecting to GHL Dashboard', status: 'success', data: { redirectUrl: ghlDashboardUrl }, timestamp: new Date() });

        setTestResults(results);

        // Show success message and redirect after 3 seconds
        toast({
          title: "Account Found!",
          description: "Redirecting you to your existing AHA-Innovations dashboard...",
        });

        setTimeout(() => {
          window.open(ghlDashboardUrl, '_blank');
        }, 3000);

        return;
      }

      // User doesn't exist, create new account
      results.push({ step: 'Creating New GHL Account', status: 'loading', timestamp: new Date() });

      const testData = {
        email: email || user.email,
        firstName: fullName.split(' ')[0] || 'Test',
        lastName: fullName.split(' ').slice(1).join(' ') || 'User',
        companyName: `${fullName || 'Test'} Company`,
        phone: '+**********',
        city: 'Test City',
        state: 'CA',
        country: 'US',
        role: 'business_owner',
        company_size: '1',
        referral_source: 'test',
        password: 'TestPassword123!'
      };

      console.log('Calling create-ghl-full-account with data:', testData);
      const ghlResponse = await callEdgeFunction('create-ghl-full-account', testData);
      console.log('GHL Response:', ghlResponse);

      if (ghlResponse.success) {
        results.push({ step: 'GHL Account Created Successfully', status: 'success', data: ghlResponse, timestamp: new Date() });

        // Update profile with GHL data
        const { error: updateError } = await supabase
          .from('profiles')
          .upsert({
            id: user.id,
            email: user.email,
            full_name: userData.full_name || userData.name,
            company_name: testData.companyName,
            phone: testData.phone,
            city: testData.city,
            state: testData.state,
            country: testData.country,
            role: testData.role,
            company_size: testData.company_size,
            referral_source: testData.referral_source,
            ghl_location_id: ghlResponse.locationId,
            ghl_user_id: ghlResponse.userId,
            stripe_customer_id: ghlResponse.stripeCustomerId,
            onboarding_completed: true,
            updated_at: new Date().toISOString(),
          });

        if (updateError) {
          results.push({ step: 'Profile Update Failed', status: 'error', error: updateError, timestamp: new Date() });
        } else {
          results.push({ step: 'Profile Updated Successfully', status: 'success', timestamp: new Date() });
          await fetchProfile(user.id); // Refresh profile data
        }

        // Redirect to new GHL dashboard
        const ghlDashboardUrl = `https://app.gohighlevel.com/location/${ghlResponse.locationId}/dashboard`;
        results.push({ step: 'Redirecting to New GHL Dashboard', status: 'success', data: { redirectUrl: ghlDashboardUrl }, timestamp: new Date() });

        toast({
          title: "Account Created!",
          description: "Redirecting you to your new AHA-Innovations dashboard...",
        });

        setTimeout(() => {
          window.open(ghlDashboardUrl, '_blank');
        }, 3000);

      } else {
        results.push({ step: 'GHL Account Creation Failed', status: 'error', data: ghlResponse, timestamp: new Date() });
      }

    } catch (error: any) {
      console.error('GHL Integration Error:', error);
      results.push({
        step: 'Integration Test Failed',
        status: 'error',
        error: error.message || 'Unknown error occurred',
        details: error,
        timestamp: new Date()
      });

      toast({
        title: "Integration Failed",
        description: error.message || "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setTestResults(results);
      setLoading(false);
    }
  };

  const handleEmailSignup = async () => {
    if (!signupEmail.trim()) {
      toast({
        title: "Email required",
        description: "Please enter your email address.",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);

      // First, check if user already exists in GHL
      const existsInGHL = await checkGHLUserAndRedirect(signupEmail);

      if (existsInGHL) {
        // User exists in GHL, redirect handled by checkGHLUserAndRedirect
        setSignupEmail('');
        return;
      }

      // User doesn't exist in GHL, proceed with signup
      // Sign up with email only (systeme.io style)
      const { data, error } = await supabase.auth.signUp({
        email: signupEmail,
        password: 'temp-password-' + Math.random().toString(36).substring(2, 15), // Temporary password
        options: {
          emailRedirectTo: `${window.location.origin}/test-social-login`,
          data: {
            signup_method: 'email_only',
            full_name: '', // Will be collected later
          }
        }
      });

      if (error) throw error;

      toast({
        title: "Check your email!",
        description: "We've sent you a confirmation link to complete your signup and AHA-Innovations account creation.",
      });

      // Clear the email field
      setSignupEmail('');

    } catch (error: any) {
      toast({
        title: "Signup failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // New function to handle complete GHL account creation after email confirmation
  const handleCompleteGHLAccountCreation = async (userEmail: string, userId: string) => {
    try {
      console.log('Creating complete GHL account for user:', userEmail);

      // Generate user details from email and location data
      const emailParts = userEmail.split('@');
      const firstName = emailParts[0].charAt(0).toUpperCase() + emailParts[0].slice(1);
      const lastName = 'User'; // Default last name

      const accountData = {
        firstName: firstName,
        lastName: lastName,
        email: userEmail,
        phone: '', // Will be collected later if needed
        companyName: `${firstName}'s Business`,
        city: userLocation?.city || 'New York',
        state: userLocation?.state || 'NY',
        country: userLocation?.country || 'US',
        supabaseUserId: userId
      };

      // Create complete GHL account using our SSO service
      const result = await GHLSSOService.createCompleteAccount(accountData);

      if (result.success) {
        toast({
          title: "Account Created!",
          description: "Your AHA-Innovations account has been created. Redirecting to dashboard...",
        });

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          window.open(result.dashboardUrl, '_blank');
        }, 2000);

        return result;
      } else {
        throw new Error('Failed to create AHA-Innovations account');
      }

    } catch (error) {
      console.error('Error creating complete GHL account:', error);
      toast({
        title: "Account Creation Failed",
        description: "Failed to create your AHA-Innovations account. Please contact support.",
        variant: "destructive",
      });
      throw error;
    }
  };

  const handleSignOut = async () => {
    try {
      console.log('Signing out user...');

      // Force sign out with scope 'global' to clear all sessions
      await supabase.auth.signOut({ scope: 'global' });

      // Clear all state immediately
      setUser(null);
      setProfile(null);
      setTestResults([]);
      setFullName('');
      setEmail('');
      setSignupEmail('');

      // Clear any cached data
      localStorage.removeItem('supabase.auth.token');
      sessionStorage.clear();

      toast({
        title: "Signed out successfully",
        description: "You have been signed out.",
      });

      console.log('User signed out successfully');

      // Force page refresh to ensure clean state
      setTimeout(() => {
        window.location.reload();
      }, 1000);

    } catch (error: any) {
      console.error('Error signing out:', error);

      // Even if there's an error, try to clear local state
      setUser(null);
      setProfile(null);
      setTestResults([]);
      setFullName('');
      setEmail('');
      setSignupEmail('');

      toast({
        title: "Error signing out",
        description: error.message,
        variant: "destructive",
      });

      // Force refresh even on error
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card className="bg-gray-800/50 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">Social Login + GHL Integration Test</CardTitle>
            <CardDescription className="text-gray-300">
              Test the complete flow from social login to GHL account creation
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {!user ? (
              <div className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white">Step 1A: Quick Email Signup (systeme.io style)</h3>
                  <div className="flex gap-2">
                    <Input
                      type="email"
                      value={signupEmail}
                      onChange={(e) => setSignupEmail(e.target.value)}
                      placeholder="Enter your email to get started"
                      className="bg-gray-800 border-gray-600 text-white flex-1"
                      onKeyPress={(e) => e.key === 'Enter' && handleEmailSignup()}
                    />
                    <Button
                      onClick={handleEmailSignup}
                      disabled={loading || !signupEmail.trim()}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      {loading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        'Get Started'
                      )}
                    </Button>
                  </div>
                  <p className="text-sm text-gray-400">
                    Just enter your email - we'll send you a link to complete your account setup.
                  </p>
                </div>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t border-gray-600" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-gray-800 px-2 text-gray-400">Or continue with</span>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white">Step 1B: Sign in with Social Provider</h3>
                  <div className="grid gap-2">
                    <SocialButton
                      icon={Github}
                      label="Continue with GitHub"
                      onClick={() => handleSocialSignIn('github')}
                      variant="outline"
                      className="bg-transparent border-gray-700 text-white hover:bg-gray-800"
                    />
                    <SocialButton
                      icon={Mail}
                      label="Continue with Google"
                      onClick={() => handleSocialSignIn('google')}
                      variant="outline"
                      className="bg-transparent border-gray-700 text-white hover:bg-gray-800"
                    />
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-white">✅ Signed in as: {user.email}</h3>
                  <div className="flex gap-2">
                    <Button
                      onClick={(e) => {
                        e.preventDefault();
                        console.log('Sign out button clicked!');
                        handleSignOut();
                      }}
                      variant="outline"
                      className="border-gray-600 text-white hover:bg-gray-700"
                    >
                      Sign Out
                    </Button>
                    <button
                      onClick={() => {
                        console.log('HTML button clicked!');
                        handleSignOut();
                      }}
                      className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 text-sm"
                    >
                      Logout
                    </button>
                  </div>
                </div>
                
                <div className="bg-gray-700/50 p-4 rounded-lg">
                  <h4 className="font-semibold text-white mb-2">User Data:</h4>
                  <pre className="text-sm text-gray-300 overflow-auto">
                    {JSON.stringify({ 
                      email: user.email, 
                      metadata: user.user_metadata,
                      provider: user.app_metadata?.provider 
                    }, null, 2)}
                  </pre>
                </div>

                {profile && (
                  <div className="bg-gray-700/50 p-4 rounded-lg">
                    <h4 className="font-semibold text-white mb-2">Profile Data:</h4>
                    <pre className="text-sm text-gray-300 overflow-auto">
                      {JSON.stringify(profile, null, 2)}
                    </pre>
                  </div>
                )}

                <div className="bg-gray-700/50 p-4 rounded-lg space-y-4">
                  <h4 className="font-semibold text-white mb-2">Step 2: Complete Your Information</h4>
                  <div className="grid gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="fullName" className="text-white">Full Name</Label>
                      <Input
                        id="fullName"
                        type="text"
                        value={fullName}
                        onChange={(e) => setFullName(e.target.value)}
                        placeholder="Enter your full name"
                        className="bg-gray-800 border-gray-600 text-white"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-white">Email (from login)</Label>
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        disabled
                        className="bg-gray-700 border-gray-600 text-gray-400 cursor-not-allowed"
                      />
                      <p className="text-xs text-gray-400">Email is automatically filled from your login</p>
                    </div>
                  </div>
                </div>

                {userLocation && (
                  <div className="p-3 bg-blue-900/20 border border-blue-500/20 rounded">
                    <h4 className="text-sm font-medium text-blue-300 mb-2">Detected Location:</h4>
                    <div className="text-xs text-blue-200 space-y-1">
                      {userLocation.city && <div>City: {userLocation.city}</div>}
                      {userLocation.state && <div>State: {userLocation.state}</div>}
                      {userLocation.country && <div>Country: {userLocation.country}</div>}
                      <div className="text-blue-400 mt-2">
                        This will be used for your GHL account location
                      </div>
                    </div>
                  </div>
                )}

                <div className="space-y-2">
                  <Button
                    onClick={testGHLCredentials}
                    disabled={loading}
                    className="w-full bg-blue-600 hover:bg-blue-700"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Testing Credentials...
                      </>
                    ) : (
                      'Test GHL Credentials'
                    )}
                  </Button>

                  <Button
                    onClick={() => createGHLAccountAutomatically(email)}
                    disabled={loading || !email}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating Account...
                      </>
                    ) : (
                      'Auto-Create GHL Account (Email Only)'
                    )}
                  </Button>

                  <Button
                    onClick={testGHLIntegration}
                    disabled={loading || !fullName.trim()}
                    className="w-full bg-red-600 hover:bg-red-700"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Testing GHL Integration...
                      </>
                    ) : (
                      'Test GHL Account Creation (Manual Details)'
                    )}
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {testResults.length > 0 && (
          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-gray-700/30 rounded-lg">
                    {result.status === 'success' && <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />}
                    {result.status === 'error' && <XCircle className="h-5 w-5 text-red-500 mt-0.5" />}
                    {result.status === 'loading' && <Loader2 className="h-5 w-5 text-blue-500 animate-spin mt-0.5" />}
                    {result.status === 'info' && <div className="h-5 w-5 bg-blue-500 rounded-full mt-0.5" />}
                    
                    <div className="flex-1">
                      <div className="text-white font-medium">{result.step}</div>
                      <div className="text-xs text-gray-400">{result.timestamp.toLocaleTimeString()}</div>
                      {result.data && (
                        <details className="mt-2">
                          <summary className="text-sm text-gray-300 cursor-pointer">View Data</summary>
                          <pre className="text-xs text-gray-400 mt-1 overflow-auto bg-gray-800 p-2 rounded">
                            {JSON.stringify(result.data, null, 2)}
                          </pre>
                        </details>
                      )}
                      {result.error && (
                        <div className="mt-2 p-2 bg-red-900/20 border border-red-500/20 rounded">
                          <div className="text-sm text-red-400 font-medium">Error:</div>
                          <div className="text-sm text-red-300 mt-1">{result.error}</div>
                          {result.details && (
                            <details className="mt-2">
                              <summary className="text-xs text-red-400 cursor-pointer">Show Error Details</summary>
                              <pre className="text-xs text-red-300 mt-1 whitespace-pre-wrap overflow-auto">
                                {JSON.stringify(result.details, null, 2)}
                              </pre>
                            </details>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* GHL SSO Service Test Component */}
        <GHLSSOTest />
      </div>
    </div>
  );
};

export default TestSocialLogin;
