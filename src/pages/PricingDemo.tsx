import React from 'react';
import { PricingSectionDemo } from '@/components/demo/pricing-section-demo';
import HeroHeader from '@/components/sections/HeroHeader';
import ModernFooter from '@/components/sections/ModernFooter';

const PricingDemo = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <HeroHeader />
      
      <main className="pt-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
              New Pricing Component Demo
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Testing the modern pricing section with your custom pricing tiers including the free Social Planner plan.
            </p>
          </div>
          
          <PricingSectionDemo />
        </div>
      </main>
      
      <ModernFooter />
    </div>
  );
};

export default PricingDemo;
