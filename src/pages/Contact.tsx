import React, { useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, useScroll, useTransform } from 'framer-motion';
import Logo from '@/components/Logo';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DotPattern } from '@/components/ui/dot-pattern';
import { useToast } from '@/hooks/use-toast';
import { Mail, Phone } from 'lucide-react';
import Tilt3D from '@/components/anim/Tilt3D';
import HeroHeader from '@/components/sections/HeroHeader';
import ModernFooter from '@/components/sections/ModernFooter';

const Contact = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  const heroRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  // Parallax effect for hero section
  const y = useTransform(scrollYProgress, [0, 1], [0, 200]);
  const opacity = useTransform(scrollYProgress, [0, 0.8], [1, 0]);

  // Load the form embed script
  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://calendar.aha-innovations.com/js/form_embed.js';
    script.async = true;
    document.body.appendChild(script);

    return () => {
      // Cleanup script when component unmounts
      const existingScript = document.querySelector(`script[src="${script.src}"]`);
      if (existingScript) {
        document.body.removeChild(existingScript);
      }
    };
  }, []);

  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden bg-black relative">
      {/* Hero-style background for entire page (copied from ModernIndex) */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Dot pattern texture overlay */}
        <div
          className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, white 1px, transparent 0)`,
            backgroundSize: '32px 32px',
            maskImage: 'radial-gradient(800px circle at center, white, transparent)'
          }}
        />
        {/* Soft ambient glow from upper left */}
        <div
          className="absolute -top-[25%] -left-[25%] w-[100%] h-[100%]"
          style={{
            background: `radial-gradient(ellipse 800px 600px at 25% 25%, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%)`
          }}
        />
        {/* Main background gradients */}
        <div className="absolute top-1/4 left-1/4 w-64 md:w-96 h-64 md:h-96 bg-gradient-to-br from-aha-red to-transparent rounded-full blur-3xl opacity-[0.05]" />
        <div className="absolute bottom-1/4 right-1/4 w-64 md:w-96 h-64 md:h-96 bg-gradient-to-tr from-aha-red to-transparent rounded-full blur-3xl opacity-[0.05]" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[120%] h-[120%] bg-gradient-radial from-aha-red via-transparent to-transparent opacity-[0.1]" />
        {/* Additional animated elements */}
        <div className="absolute -top-[30%] -right-[10%] w-[70%] h-[70%] rounded-full bg-gradient-to-r from-aha-red/30 via-aha-red/10 to-blue-500/5 blur-[120px] opacity-[0.4] animate-spin-slow" style={{ animationDuration: '40s' }} />
        <div className="absolute -bottom-[30%] -left-[10%] w-[70%] h-[70%] rounded-full bg-gradient-to-r from-blue-500/5 via-aha-red/10 to-aha-red/30 blur-[120px] opacity-[0.4] animate-spin-slow" style={{ animationDuration: '50s', animationDirection: 'reverse' }} />
      </div>

      <div className="relative z-10">
        <HeroHeader />

        {/* Contact Section */}
        <section ref={heroRef} className="py-16 relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #0F0F0F 0%, rgba(144, 25, 28, 0.8) 100%)' }}>
          {/* Background elements */}
          <div className="absolute inset-0 z-0">
            {/* Dot pattern texture overlay */}
            <DotPattern
              width={28}
              height={28}
              cx={1}
              cy={1}
              cr={0.7}
              className="fill-white/[0.02] [mask-image:radial-gradient(1000px_circle_at_center,white,transparent)]"
            />
            <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
            <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
          </div>

          <motion.div style={{ y, opacity }} className="container mx-auto px-4 relative z-10">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-6xl font-bold text-center mb-16"
            >
              Contact
            </motion.h1>

            <div className="max-w-5xl mx-auto">
              <div className="grid md:grid-cols-3 gap-12 mb-16">
                <div className="col-span-2">
                  <motion.h2
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.2 }}
                    className="text-3xl font-bold mb-4"
                  >
                    Contact Us
                  </motion.h2>
                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 }}
                    className="mb-8 text-gray-300"
                  >
                    Complete the form below to contact us and generate a ticket
                  </motion.p>

                  {/* Contact Form */}
                  <motion.div
                    initial={{ opacity: 0, x: -30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.8, delay: 0.6 }}
                  >
                    <Card className="glass-card border border-gray-800/50 relative overflow-hidden">
                      {/* Dot pattern for contact form */}
                      <DotPattern
                        width={20}
                        height={20}
                        cx={0.8}
                        cy={0.8}
                        cr={0.4}
                        className="fill-white/[0.015] [mask-image:radial-gradient(400px_circle_at_center,white,transparent)]"
                      />
                      <div className="absolute inset-0 bg-gradient-to-br from-aha-red/5 to-transparent opacity-50"></div>
                      <CardHeader className="relative z-10">
                        <CardTitle className="text-2xl text-center">AHA Support Ticket</CardTitle>
                      </CardHeader>
                      <CardContent className="relative z-10">
                        <div className="w-full h-[676px]">
                          <iframe
                            src="https://calendar.aha-innovations.com/widget/form/BR0ndq4PSkAUsJLdp7io"
                            style={{ width: '100%', height: '100%', border: 'none', borderRadius: '3px' }}
                            id="inline-BR0ndq4PSkAUsJLdp7io" 
                            data-layout="{'id':'INLINE'}"
                            data-trigger-type="alwaysShow"
                            data-trigger-value=""
                            data-activation-type="alwaysActivated"
                            data-activation-value=""
                            data-deactivation-type="neverDeactivate"
                            data-deactivation-value=""
                            data-form-name="Support Ticket"
                            data-height="676"
                            data-layout-iframe-id="inline-BR0ndq4PSkAUsJLdp7io"
                            data-form-id="BR0ndq4PSkAUsJLdp7io"
                            title="Support Ticket"
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                </div>

                <div>
                  <motion.div
                    initial={{ opacity: 0, x: 30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.8, delay: 0.6 }}
                    className="space-y-8"
                  >
                    <Tilt3D intensity={5}>
                      <div className="flex justify-center mb-6">
                        <div className="inline-flex items-center justify-center bg-aha-red/80 backdrop-blur-sm rounded-lg relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:shadow-aha-red/20 p-1" style={{ minWidth: '56px', minHeight: '56px' }}>
                          <div className="absolute inset-0 bg-gradient-to-br from-aha-red to-aha-darkred opacity-80"></div>
                          <div className="relative z-10 flex items-center justify-center">
                            <img
                              src="/AHA-logomodern.png"
                              alt="AHA Innovations"
                              className="h-10 w-auto"
                            />
                          </div>
                        </div>
                      </div>
                    </Tilt3D>

                    <Tilt3D intensity={5}>
                      <div className="glass-card rounded-xl p-6 transition-all duration-300 hover:shadow-lg relative overflow-hidden">
                        {/* Dot pattern for contact info cards */}
                        <DotPattern
                          width={16}
                          height={16}
                          cx={0.6}
                          cy={0.6}
                          cr={0.3}
                          className="fill-white/[0.01] [mask-image:radial-gradient(200px_circle_at_center,white,transparent)]"
                        />
                        <div className="flex items-start relative z-10">
                          <div className="bg-aha-red/80 backdrop-blur-sm p-3 rounded-full mr-4">
                            <Mail className="h-6 w-6 text-white" />
                          </div>
                          <div>
                            <h4 className="text-xl font-bold mb-2">Email Us</h4>
                            <p className="text-gray-300 mb-1 break-all"><EMAIL></p>
                          </div>
                        </div>
                      </div>
                    </Tilt3D>

                    <Tilt3D intensity={5}>
                      <div className="glass-card rounded-xl p-6 transition-all duration-300 hover:shadow-lg relative overflow-hidden">
                        {/* Dot pattern for contact info cards */}
                        <DotPattern
                          width={16}
                          height={16}
                          cx={0.6}
                          cy={0.6}
                          cr={0.3}
                          className="fill-white/[0.01] [mask-image:radial-gradient(200px_circle_at_center,white,transparent)]"
                        />
                        <div className="flex items-start relative z-10">
                          <div className="bg-aha-red/80 backdrop-blur-sm p-3 rounded-full mr-4">
                            <Phone className="h-6 w-6 text-white" />
                          </div>
                          <div>
                            <h4 className="text-xl font-bold mb-2">Call Us</h4>
                            <p className="text-gray-300 mb-1">+****************</p>
                          </div>
                        </div>
                      </div>
                    </Tilt3D>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>
        </section>

        {/* Location Section - moved outside scroll effect */}
        <div className="container mx-auto px-4 relative z-10 max-w-5xl">
          <h2 className="text-3xl font-bold mb-6">Our Location</h2>
          <p className="mb-8 text-gray-300">Visit us to explore innovative solutions today.</p>
          <div className="grid md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="text-xl font-bold mb-4">Get In Touch</h3>
              <p className="text-gray-300">6921, 1021 E Lincolnway, Cheyenne, Wyoming, United States</p>
            </div>
            <div>
              <h3 className="text-xl font-bold mb-4">Contact Us</h3>
              <p className="text-gray-300">Monday – Friday 8:00 a.m. – 5:00 p.m.</p>
            </div>
          </div>
          <div className="border border-gray-700 rounded-lg overflow-hidden my-8" style={{ minHeight: '340px', marginBottom: '4rem' }}>
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3014.8696474487305!2d-72.58243812352329!3d42.14982804776801!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89e6dc14c61a7d57%3A0x3e120e65458b678c!2sHilltop%20St%2C%20Springfield%2C%20MA%2001128!5e0!3m2!1sen!2sus!4v1701286737151!5m2!1sen!2sus"
              width="100%"
              height="300"
              style={{ border: 0 }}
              allowFullScreen
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade" />
          </div>
        </div>

        <ModernFooter />
      </div>
    </div>
  );
};

export default Contact;
