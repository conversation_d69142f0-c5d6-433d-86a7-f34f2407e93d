import React, { useRef, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, Heart, Sparkles, ArrowRight, Home, Mail, RefreshCw } from 'lucide-react';
import ModernHeader from '@/components/sections/ModernHeader';
import ModernFooter from '@/components/sections/ModernFooter';
import Tilt3D from '@/components/anim/Tilt3D';

const ThankYou = () => {
  const navigate = useNavigate();
  const heroRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  // Parallax effect for hero section - keep content visible
  const y = useTransform(scrollYProgress, [0, 1], [0, 100]);
  const opacity = useTransform(scrollYProgress, [0, 0.95], [1, 0.98]);

  // Confetti animation effect
  useEffect(() => {
    // Create floating particles effect
    const createParticle = () => {
      const particle = document.createElement('div');
      particle.className = 'fixed pointer-events-none z-10';
      particle.style.left = Math.random() * 100 + 'vw';
      particle.style.top = '-10px';
      particle.style.width = '4px';
      particle.style.height = '4px';
      particle.style.background = `hsl(${Math.random() * 60 + 340}, 70%, 60%)`;
      particle.style.borderRadius = '50%';
      particle.style.animation = `fall ${3 + Math.random() * 2}s linear forwards`;
      
      document.body.appendChild(particle);
      
      setTimeout(() => {
        particle.remove();
      }, 5000);
    };

    // Add CSS animation for falling particles
    const style = document.createElement('style');
    style.textContent = `
      @keyframes fall {
        to {
          transform: translateY(100vh) rotate(360deg);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);

    // Create particles periodically
    const interval = setInterval(createParticle, 300);
    
    // Clean up after 10 seconds
    setTimeout(() => {
      clearInterval(interval);
      style.remove();
    }, 10000);

    return () => {
      clearInterval(interval);
      style.remove();
    };
  }, []);

  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden" style={{ background: '#0F0F0F' }}>
      <ModernHeader />

      {/* Hero Section */}
      <section ref={heroRef} className="py-16 relative overflow-hidden min-h-screen flex items-center" style={{ background: 'linear-gradient(135deg, #0F0F0F 0%, rgba(144, 25, 28, 0.8) 100%)' }}>
        {/* Background elements */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
          
          {/* Animated background shapes */}
          <motion.div
            className="absolute top-1/4 left-1/4 w-32 h-32 rounded-full bg-aha-red/10"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className="absolute bottom-1/4 right-1/4 w-24 h-24 rounded-full bg-purple-500/10"
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 1
            }}
          />
        </div>

        <motion.div style={{ y, opacity }} className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            {/* Success Icon */}
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ duration: 0.8, type: "spring", stiffness: 200 }}
              className="mb-8"
            >
              <div className="inline-flex items-center justify-center w-24 h-24 rounded-full bg-green-500/20 backdrop-blur-sm border border-green-500/30 mb-6">
                <CheckCircle className="w-12 h-12 text-green-400" />
              </div>
            </motion.div>

            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-2 px-6 text-lg">
                <Sparkles className="w-4 h-4 mr-2" />
                Account Created!
              </Badge>
            </motion.div>

            {/* Main Heading */}
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-4xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white via-aha-red to-purple-400 bg-clip-text text-transparent"
            >
              Thank You!
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto"
            >
              Your account has been created successfully! Please check your email to activate your account.
            </motion.p>

            {/* Thank You Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="mb-12"
            >
              <Tilt3D intensity={5}>
                <Card className="glass-card border border-gray-800/50 relative overflow-hidden max-w-2xl mx-auto">
                  <div className="absolute inset-0 bg-gradient-to-br from-aha-red/10 to-purple-500/5 opacity-50"></div>
                  <CardContent className="relative z-10 p-8">
                    <div className="flex items-center justify-center mb-6">
                      <Mail className="w-8 h-8 text-aha-red mr-3" />
                      <h2 className="text-2xl font-bold">Check Your Email!</h2>
                    </div>
                    <p className="text-gray-300 mb-6 leading-relaxed">
                      We've sent an activation email to your inbox. Please click the activation link
                      in the email to complete your account setup and start using AHA-Innovations.
                      Don't forget to check your spam folder if you don't see it within a few minutes.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <Button
                        className="bg-aha-red hover:bg-aha-darkred text-white group relative overflow-hidden"
                        onClick={() => window.location.href = 'https://app.aha-innovations.com/'}
                      >
                        <span className="relative z-10 flex items-center">
                          <RefreshCw className="w-4 h-4 mr-2" />
                          Sign In to Your Account
                          <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                        </span>
                        <span className="absolute inset-0 bg-white/20 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300"></span>
                      </Button>
                      <Button
                        variant="outline"
                        className="border-gray-600 text-white hover:bg-white/10 group"
                        onClick={() => navigate('/')}
                      >
                        <Home className="w-4 h-4 mr-2" />
                        Back to Home
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </Tilt3D>
            </motion.div>

            {/* Additional Info */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 1.2 }}
              className="text-center"
            >
              <p className="text-gray-400 text-sm">
                Need help? Contact us at{' '}
                <a href="mailto:<EMAIL>" className="text-aha-red hover:text-aha-darkred transition-colors">
                  <EMAIL>
                </a>
              </p>
            </motion.div>
          </div>
        </motion.div>
      </section>

      <ModernFooter />
    </div>
  );
};

export default ThankYou;
