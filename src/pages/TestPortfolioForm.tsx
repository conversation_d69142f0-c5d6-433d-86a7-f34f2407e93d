import React, { useEffect } from 'react';
import ModernHeader from '@/components/sections/ModernHeader';
import ModernFooter from '@/components/sections/ModernFooter';
import { motion } from 'framer-motion';

const TestPortfolioForm = () => {
  useEffect(() => {
    // Load the GHL form script
    const script = document.createElement('script');
    script.src = 'https://link.msgsndr.com/js/form_embed.js';
    script.async = true;
    document.body.appendChild(script);

    // Add a link to our custom CSS for the GHL form
    const styleLink = document.createElement('link');
    styleLink.rel = 'stylesheet';
    styleLink.href = '/ghl-form-styles.css';
    document.head.appendChild(styleLink);

    return () => {
      // Clean up when component unmounts
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
      if (document.head.contains(styleLink)) {
        document.head.removeChild(styleLink);
      }
    };
  }, []);

  return (
    <div className="min-h-screen w-full bg-aha-dark text-white overflow-x-hidden">
      <ModernHeader />

      {/* Hero section with animated title */}
      <section className="pt-16 pb-8 md:pt-24 md:pb-12">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-400">
              Test Portfolio Form
            </h1>
            <p className="text-gray-400 text-lg md:text-xl mb-8">
              Testing the form from your portfolio to see if it works with transparent background.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Form section with glass effect */}
      <section className="pb-16 md:pb-24">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="max-w-3xl mx-auto"
          >
            <div className="rounded-xl overflow-hidden shadow-2xl border border-gray-800/30 relative">
              {/* Decorative elements */}
              <div className="absolute -top-20 -right-20 w-40 h-40 bg-aha-red/20 rounded-full blur-3xl"></div>
              <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-aha-red/20 rounded-full blur-3xl"></div>
              
              {/* Form background with gradient */}
              <div className="bg-gradient-to-b from-gray-900/90 to-black/90 p-6 md:p-8 relative z-10">
                {/* Subtle grid overlay */}
                <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] opacity-10 z-0"></div>
                
                {/* Portfolio Form */}
                <div className="relative z-10" style={{ height: "500px" }}>
                  <iframe
                    src="https://api.leadconnectorhq.com/widget/form/ASjvJgX8xJwcGHozC0s3"
                    style={{ width: "100%", height: "100%", border: "none", borderRadius: "4px" }}
                    id="inline-ASjvJgX8xJwcGHozC0s3" 
                    data-layout="{'id':'INLINE'}"
                    data-trigger-type="alwaysShow"
                    data-trigger-value=""
                    data-activation-type="alwaysActivated"
                    data-activation-value=""
                    data-deactivation-type="neverDeactivate"
                    data-deactivation-value=""
                    data-form-name="Form 0"
                    data-height="500"
                    data-layout-iframe-id="inline-ASjvJgX8xJwcGHozC0s3"
                    data-form-id="ASjvJgX8xJwcGHozC0s3"
                    title="Form 0"
                  />
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      <ModernFooter />
    </div>
  );
};

export default TestPortfolioForm;
