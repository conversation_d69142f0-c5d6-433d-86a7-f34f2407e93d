import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Sparkles, Zap, Star } from 'lucide-react';
import { motion } from 'framer-motion';

export default function FontDemo() {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-aha-dark text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => navigate('/')}
            className="mb-4 text-white hover:text-aha-red"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Button>
          
          <Badge className="bg-aha-red/20 hover:bg-aha-red/30 text-aha-red border-none mb-6 py-1.5 px-4">
            Modern Typography
          </Badge>
          
          <h1 className="text-4xl md:text-6xl font-bold mb-4 font-gotham-condensed">
            AHA-Innovations Typography System
          </h1>
          <p className="text-white/90 text-lg mb-8 max-w-2xl font-gotham">
            Showcasing our modern Gotham font family: Gotham Condensed for headlines, Gotham for body text, and Gotham Narrow for accents.
          </p>
        </div>

        <div className="max-w-6xl mx-auto space-y-16">
          
          {/* Schabo Showcase */}
          <motion.section
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="glass-card p-8 rounded-xl"
          >
            <div className="flex items-center gap-3 mb-6">
              <Sparkles className="text-aha-red" size={24} />
              <h2 className="text-2xl font-bold font-gotham-condensed text-aha-red">Gotham Condensed - Headlines</h2>
            </div>
            
            <div className="space-y-6">
              <div>
                <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Condensed - Weight 900</p>
                <h1 className="text-6xl md:text-8xl font-black font-gotham-condensed leading-tight">
                  Build <span className="text-aha-red">Websites</span>
                </h1>
              </div>

              <div>
                <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Condensed - Weight 700</p>
                <h2 className="text-4xl md:text-5xl font-bold font-gotham-condensed">
                  What You <span className="text-aha-red">Get</span>
                </h2>
              </div>

              <div>
                <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Condensed - Weight 500</p>
                <h3 className="text-3xl md:text-4xl font-medium font-gotham-condensed">
                  AHA-Innovations Platform
                </h3>
              </div>

              <div>
                <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Ultra - Weight 950</p>
                <h1 className="text-6xl md:text-8xl font-gotham leading-tight" style={{ fontWeight: 950 }}>
                  Build <span className="text-aha-red">Websites</span>
                </h1>
              </div>

              <div>
                <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Bold - Weight 700</p>
                <h2 className="text-4xl md:text-5xl font-bold font-gotham">
                  What You <span className="text-aha-red">Get</span>
                </h2>
              </div>

              <div>
                <p className="text-sm text-white/60 mb-2 font-gotham">Gotham with Letter Spacing</p>
                <h3 className="text-3xl md:text-4xl font-bold font-gotham tracking-tighter">
                  AHA-Innovations Platform
                </h3>
              </div>

              <div>
                <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Medium - Balanced Weight</p>
                <h1 className="text-6xl md:text-8xl font-medium font-gotham leading-tight">
                  Build <span className="text-aha-red">Websites</span>
                </h1>
              </div>

              {/* Gotham Font Family Section */}
              <div className="border-t border-white/10 pt-8 mt-8">
                <h3 className="text-xl font-bold text-white mb-6 font-gotham">Gotham Font Family - Modern Professional</h3>

                <div className="space-y-6">
                  <div>
                    <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Black - Ultra Bold</p>
                    <h1 className="text-6xl md:text-8xl font-black font-gotham leading-tight tracking-tight">
                      Build <span className="text-aha-red">Websites</span>
                    </h1>
                  </div>

                  <div>
                    <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Condensed Black - Modern Headers</p>
                    <h1 className="text-6xl md:text-8xl font-black font-gotham-condensed leading-tight tracking-tight">
                      Build <span className="text-aha-red">Websites</span>
                    </h1>
                  </div>

                  <div>
                    <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Narrow Black - Space Efficient</p>
                    <h1 className="text-6xl md:text-8xl font-black font-gotham-narrow leading-tight tracking-tight">
                      Build <span className="text-aha-red">Websites</span>
                    </h1>
                  </div>

                  <div>
                    <p className="text-sm text-white/60 mb-2 font-gotham">Gotham ScreenSmart Black - Digital Optimized</p>
                    <h1 className="text-6xl md:text-8xl font-black font-gotham-screen leading-tight tracking-tight">
                      Build <span className="text-aha-red">Websites</span>
                    </h1>
                  </div>

                  <div>
                    <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Bold - Professional</p>
                    <h1 className="text-6xl md:text-8xl font-bold font-gotham leading-tight">
                      Build <span className="text-aha-red">Websites</span>
                    </h1>
                  </div>

                  <div>
                    <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Condensed Bold - Compact Impact</p>
                    <h1 className="text-6xl md:text-8xl font-bold font-gotham-condensed leading-tight">
                      Build <span className="text-aha-red">Websites</span>
                    </h1>
                  </div>
                </div>
              </div>

              {/* Gotham Weight Variations Section */}
              <div className="border-t border-white/10 pt-8 mt-8">
                <h3 className="text-xl font-bold text-white mb-6 font-gotham">Gotham Weight Variations</h3>

                <div className="space-y-6">
                  <div>
                    <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Book - Light Weight</p>
                    <h1 className="text-6xl md:text-8xl font-normal font-gotham leading-tight tracking-tight">
                      Build <span className="text-aha-red">Websites</span>
                    </h1>
                  </div>

                  <div>
                    <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Medium - Balanced</p>
                    <h1 className="text-6xl md:text-8xl font-medium font-gotham leading-tight">
                      Build <span className="text-aha-red">Websites</span>
                    </h1>
                  </div>

                  <div>
                    <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Condensed Medium - Compact</p>
                    <h1 className="text-6xl md:text-8xl font-medium font-gotham-condensed leading-tight tracking-wide">
                      Build <span className="text-aha-red">Websites</span>
                    </h1>
                  </div>

                  <div>
                    <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Narrow Bold - Space Efficient</p>
                    <h1 className="text-6xl md:text-8xl font-bold font-gotham-narrow leading-tight">
                      Build <span className="text-aha-red">Websites</span>
                    </h1>
                  </div>

                  <div>
                    <p className="text-sm text-white/60 mb-2 font-gotham">Gotham ScreenSmart Bold - Digital</p>
                    <h1 className="text-6xl md:text-8xl font-bold font-gotham-screen leading-tight">
                      Build <span className="text-aha-red">Websites</span>
                    </h1>
                  </div>

                  <div>
                    <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Ultra - Maximum Impact</p>
                    <h1 className="text-6xl md:text-8xl font-gotham leading-tight tracking-tight" style={{ fontWeight: 950 }}>
                      Build <span className="text-aha-red">Websites</span>
                    </h1>
                  </div>
                </div>
              </div>

              <div>
                <p className="text-sm text-white/60 mb-2 font-gotham">Gotham - Condensed Style</p>
                <h3 className="text-2xl md:text-3xl font-normal font-gotham-condensed tracking-wide">
                  Need help getting started?
                </h3>
              </div>

              <div>
                <p className="text-sm text-white/60 mb-2 font-gotham">Gotham Family Comparison</p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p className="text-xs text-white/40 mb-1">Gotham Regular</p>
                    <h4 className="text-xl font-gotham">Professional & Clean</h4>
                  </div>
                  <div>
                    <p className="text-xs text-white/40 mb-1">Gotham Condensed</p>
                    <h4 className="text-xl font-gotham-condensed">Compact & Modern</h4>
                  </div>
                  <div>
                    <p className="text-xs text-white/40 mb-1">Gotham Narrow</p>
                    <h4 className="text-xl font-gotham-narrow">Space Efficient</h4>
                  </div>
                </div>
              </div>
            </div>
          </motion.section>

          {/* Gotham Body Text Showcase */}
          <motion.section
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="glass-card p-8 rounded-xl"
          >
            <div className="flex items-center gap-3 mb-6">
              <Zap className="text-blue-400" size={24} />
              <h2 className="text-2xl font-bold font-gotham text-blue-400">Gotham - Body Text</h2>
            </div>

            <div className="space-y-6">
              <div>
                <p className="text-sm text-white/60 mb-2 font-gotham">Large Paragraph (xl)</p>
                <p className="text-xl font-gotham leading-relaxed">
                  AHA-Innovations provides a comprehensive platform for businesses to manage clients,
                  automate workflows, build sales funnels, and scale their operations with powerful tools
                  and AI-driven insights.
                </p>
              </div>

              <div>
                <p className="text-sm text-white/60 mb-2 font-gotham">Regular Paragraph (base)</p>
                <p className="text-base font-gotham leading-relaxed text-white/90">
                  Everything you need to build, launch, and grow your business in one simple platform. 
                  No more juggling between different software solutions. Replace 10+ tools with one 
                  simple platform that grows with your business.
                </p>
              </div>
              
              <div>
                <p className="text-sm text-white/60 mb-2 font-gotham">Small Text (sm)</p>
                <p className="text-sm font-gotham text-white/70">
                  Experience all features with no limitations. No credit card required to start.
                  Upgrade to any plan when you're ready to scale your business.
                </p>
              </div>
            </div>
          </motion.section>

          {/* Gotham Narrow Showcase */}
          <motion.section
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="glass-card p-8 rounded-xl"
          >
            <div className="flex items-center gap-3 mb-6">
              <Star className="text-purple-400" size={24} />
              <h2 className="text-2xl font-bold font-gotham-narrow text-purple-400">Gotham Narrow - Accents</h2>
            </div>

            <div className="space-y-6">
              <div>
                <p className="text-sm text-white/60 mb-2 font-gotham">Feature Cards</p>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="bg-white/5 p-4 rounded-lg border border-white/10">
                    <h4 className="font-semibold font-gotham-narrow text-aha-red mb-2">CRM & Contact Management</h4>
                    <p className="text-sm font-gotham text-white/80">Manage all your contacts in one place</p>
                  </div>
                  <div className="bg-white/5 p-4 rounded-lg border border-white/10">
                    <h4 className="font-semibold font-gotham-narrow text-blue-400 mb-2">Website & Funnel Builder</h4>
                    <p className="text-sm font-gotham text-white/80">Build beautiful, high-converting websites</p>
                  </div>
                </div>
              </div>

              <div>
                <p className="text-sm text-white/60 mb-2 font-gotham">Navigation & Buttons</p>
                <div className="flex flex-wrap gap-3">
                  <Badge className="bg-aha-red/20 text-aha-red font-gotham-narrow">Features</Badge>
                  <Badge className="bg-blue-500/20 text-blue-400 font-gotham-narrow">Pricing</Badge>
                  <Badge className="bg-purple-500/20 text-purple-400 font-gotham-narrow">Resources</Badge>
                  <Badge className="bg-green-500/20 text-green-400 font-gotham-narrow">Contact</Badge>
                </div>
              </div>
              
              <div>
                <p className="text-sm text-white/60 mb-2 font-gotham">Stats & Numbers</p>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-3xl font-bold font-gotham-narrow text-aha-red">10+</div>
                    <div className="text-sm font-gotham text-white/70">Tools Replaced</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold font-gotham-narrow text-blue-400">$2,000</div>
                    <div className="text-sm font-gotham text-white/70">Monthly Savings</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold font-gotham-narrow text-purple-400">14</div>
                    <div className="text-sm font-gotham text-white/70">Day Free Trial</div>
                  </div>
                </div>
              </div>
            </div>
          </motion.section>

          {/* Typography Hierarchy */}
          <motion.section
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="glass-card p-8 rounded-xl"
          >
            <h2 className="text-2xl font-bold font-gotham-condensed mb-6 text-center">Complete Gotham Typography Hierarchy</h2>

            <div className="space-y-4">
              <div className="text-6xl font-black font-gotham-condensed">H1 - Gotham Condensed Hero</div>
              <div className="text-5xl font-bold font-gotham-condensed">H2 - Gotham Condensed Section</div>
              <div className="text-4xl font-medium font-gotham-condensed">H3 - Gotham Condensed Subsection</div>
              <div className="text-3xl font-bold font-gotham">H4 - Gotham Card</div>
              <div className="text-2xl font-bold font-gotham-narrow">H5 - Gotham Narrow Accent</div>
              <div className="text-xl font-gotham">Large Body Text - Gotham</div>
              <div className="text-base font-gotham">Regular Body Text - Gotham</div>
              <div className="text-sm font-gotham text-white/80">Small Text - Gotham</div>
              <div className="text-xs font-gotham-narrow text-white/60">Caption - Gotham Narrow</div>
            </div>
          </motion.section>

        </div>
      </div>
    </div>
  );
}
