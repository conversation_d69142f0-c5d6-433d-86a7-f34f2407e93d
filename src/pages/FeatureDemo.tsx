import React from "react";
import { FeaturesSectionWithCardGradient } from "@/components/ui/feature-section-with-card-gradient";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";

const FeatureDemo = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen w-full bg-aha-dark text-white">
      {/* Header */}
      <header className="w-full py-4 px-6 border-b border-white/10">
        <div className="container mx-auto flex justify-between items-center">
          <Button
            variant="ghost"
            className="text-white hover:bg-white/10"
            onClick={() => navigate('/')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Button>
          <h1 className="text-xl font-bold">Feature Section Demo</h1>
        </div>
      </header>

      {/* Demo Section */}
      <section className="py-24 bg-aha-dark relative">
        {/* Background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 left-0 w-1/2 h-1/2 bg-gradient-to-br from-aha-red/5 to-transparent rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-1/2 h-1/2 bg-gradient-to-tr from-purple-500/5 to-transparent rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-5xl font-bold mb-6">
              How <span className="text-aha-red">AHA-Innovations</span> Help You Grow
            </h2>
            <p className="text-gray-300 max-w-3xl mx-auto">
              Discover the powerful ways AHA-Innovations transforms your business operations, streamlines workflows, 
              and accelerates your growth with cutting-edge automation and integration solutions.
            </p>
          </div>

          <FeaturesSectionWithCardGradient />
        </div>
      </section>
    </div>
  );
};

export default FeatureDemo;
