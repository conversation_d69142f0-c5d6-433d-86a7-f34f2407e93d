import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Player } from '@lottiefiles/react-lottie-player';
import {
  Briefcase,
  Settings,
  Globe,
  Code,
  MessageSquare,
  Users,
  ChevronDown,
  Calendar,
  Mail,
  Phone,
  BarChart3,
  Star,
  Building,
  FileText,
  ArrowRight,
  Check
} from 'lucide-react';

// Import our custom components
import Logo from '@/components/Logo';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import TrustedCompanies from '@/components/sections/TrustedCompanies';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { FeaturesSectionWithCardGradient } from '@/components/ui/feature-section-with-card-gradient';

const Index = () => {
  const navigate = useNavigate();

  // Mock data for product demos
  const demoTabs = [
    {
      id: "crm",
      label: "CRM",
      image: "/lovable-uploads/c32a6868-d00e-4a6e-b758-8ac0467695f9.png",
      description: "Manage your customer relationships with our powerful CRM. Track leads, deals, and customer interactions all in one place."
    },
    {
      id: "website",
      label: "Website Builder",
      image: "/lovable-uploads/1824c50d-b631-40b3-a652-8e58088106ce.png",
      description: "Create stunning websites with our drag-and-drop builder. No coding required."
    },
    {
      id: "automation",
      label: "Automation",
      image: "/lovable-uploads/c32a6868-d00e-4a6e-b758-8ac0467695f9.png",
      description: "Automate your workflows and save time with our powerful automation tools."
    },
    {
      id: "calendar",
      label: "Calendar",
      image: "/lovable-uploads/1824c50d-b631-40b3-a652-8e58088106ce.png",
      description: "Manage your appointments and bookings with our integrated calendar system."
    }
  ];

  // FAQ data
  const faqItems = [
    {
      question: "What is AHA-Innovations, and how can it help my business?",
      answer: "AHA-Innovations is an all-in-one platform designed to streamline your business processes, automate workflows, and enhance customer relationships. Whether managing leads, automating marketing campaigns, or simplifying team communication, AHA-Innovations helps save time and increase efficiency so you can focus on scaling your business. Our platform integrates essential tools like calendars, automation workflows, CRM, email, and SMS communication—ensuring everything is in one place."
    },
    {
      question: "How do I get started with AHA-Innovations?",
      answer: "Getting started with AHA-Innovations is simple. You can sign up for our free trial to explore our tools without any commitment. Once registered, you'll have access to intuitive features to help set up your business workflows, build landing pages, and automate communication processes. We also provide detailed tutorials and support to help you maximize the platform's benefits."
    },
    {
      question: "Can I integrate AHA-Innovations with my existing tools?",
      answer: "Absolutely! AHA-Innovations supports a range of integrations with popular tools like Google Workspace, payment gateways, Facebook, and more. These integrations allow you to connect with the apps you're already using, ensuring seamless data flow between AHA-Innovations and your current business setup."
    },
    {
      question: "What kind of businesses benefit most from AHA-Innovations?",
      answer: "AHA-Innovations is versatile and suitable for businesses of all sizes—from startups to growing enterprises. It's especially valuable for service-based businesses, agencies, consultants, coaches, and local businesses looking to automate repetitive tasks, manage client communications, and improve marketing efficiency—all in one easy-to-use interface."
    },
    {
      question: "Is AHA-Innovations secure for storing business data?",
      answer: "Yes, security is a top priority at AHA-Innovations. We use industry-standard encryption to protect your data. Regular backups and secure data handling practices ensure that all your sensitive information is stored safely, while giving you peace of mind regarding your client information and business workflows."
    },
    {
      question: "Do you offer a free trial for new users?",
      answer: "Yes! AHA-Innovations offers a 14-day free trial for new users, giving you full access to explore the platform's features. During the trial, you can test how AHA-Innovations fits into your business needs—from automation and client management to appointment booking."
    }
  ];

  // Mock logos for client section
  const clientLogos = [
    { src: "https://via.placeholder.com/150x50?text=LOGO+1", alt: "Client 1" },
    { src: "https://via.placeholder.com/150x50?text=LOGO+2", alt: "Client 2" },
    { src: "https://via.placeholder.com/150x50?text=LOGO+3", alt: "Client 3" },
    { src: "https://via.placeholder.com/150x50?text=LOGO+4", alt: "Client 4" },
    { src: "https://via.placeholder.com/150x50?text=LOGO+5", alt: "Client 5" },
    { src: "https://via.placeholder.com/150x50?text=LOGO+6", alt: "Client 6" },
  ];

  return (
    <div className="min-h-screen w-full bg-aha-dark text-white overflow-x-hidden">
      {/* Header/Nav */}
      <header className="w-full py-4 px-6 fixed top-0 left-0 right-0 z-50 bg-aha-dark/80 backdrop-blur-md border-b border-white/10">
        <div className="container mx-auto flex justify-between items-center">
          <Logo />
          <div className="hidden md:flex items-center gap-6">
            <Button
              variant="ghost"
              className="text-white hover:bg-white/10 relative group"
              onClick={() => navigate('/')}
            >
              Home
              <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-aha-red group-hover:w-full transition-all duration-300"></div>
            </Button>
            <Button
              variant="ghost"
              className="text-white hover:bg-white/10 relative group"
              onClick={() => navigate('/features')}
            >
              Features
              <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-aha-red group-hover:w-full transition-all duration-300"></div>
            </Button>
            <Button
              variant="ghost"
              className="text-white hover:bg-white/10 relative group"
              onClick={() => navigate('/pricing')}
            >
              Pricing
              <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-aha-red group-hover:w-full transition-all duration-300"></div>
            </Button>
            <Button
              variant="ghost"
              className="text-white hover:bg-white/10 relative group"
              onClick={() => navigate('/contact')}
            >
              Contact
              <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-aha-red group-hover:w-full transition-all duration-300"></div>
            </Button>
            <Button
              variant="ghost"
              className="text-white hover:bg-white/10 relative group"
              onClick={() => { window.location.href = 'https://app.aha-innovations.com'; }}
            >
              Sign In
              <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-aha-red group-hover:w-full transition-all duration-300"></div>
            </Button>
          </div>
          <Button
            className="bg-aha-red hover:bg-aha-darkred text-white"
            onClick={() => navigate('/signup')}
          >
            Try Now!
          </Button>
        </div>
      </header>

      {/* Spacer for fixed header */}
      <div className="h-16"></div>

      {/* Hero Section */}
      <section className="auth-gradient relative">
        {/* Background elements */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
        </div>

        <div className="container mx-auto px-4 pt-20 pb-32 relative z-10">
          <div className="flex flex-col items-center text-center mb-12">
            <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4 animate-fade-in-down">
              Welcome to AHA-Innovations
            </Badge>

            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-center mb-4 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              <span className="text-white">All-in-one platform </span>
              <span className="text-aha-red">for</span>
            </h1>
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-aha-red mb-4 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
              automating your
            </h1>
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-8 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
              Business
            </h1>

            <p className="text-xl text-gray-300 mb-10 max-w-3xl animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
              AHA-Innovations helps you streamline workflows, automate tasks, and get things done efficiently,
              so you can focus on growth
            </p>

            <div className="flex flex-col sm:flex-row gap-4 animate-fade-in-up" style={{ animationDelay: '1s' }}>
              <Button
                size="lg"
                className="bg-aha-red hover:bg-aha-darkred text-white text-lg px-8 py-7 group relative overflow-hidden"
                onClick={() => navigate('/signup')}
              >
                <span className="relative z-10 flex items-center gap-2">
                  Try AHA Free
                  <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                </span>
                <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
              </Button>

              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white/10 text-lg px-8 py-7"
                onClick={() => navigate('/features')}
              >
 Learn more
              </Button>
            </div>
          </div>

          {/* Dashboard Preview Image */}
          <div className="relative w-full max-w-5xl mx-auto mt-12 animate-fade-in-up" style={{ animationDelay: '1.2s' }}>
            <div className="relative group">
              <img
                src="/lovable-uploads/c32a6868-d00e-4a6e-b758-8ac0467695f9.png"
                alt="AHA-Innovations Dashboard"
                className="w-full rounded-lg shadow-2xl border border-white/10 transition-transform duration-500 hover:scale-[1.02] group-hover:shadow-aha-red/20"
              />

              {/* Glow effect */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-aha-red/20 to-purple-500/20 rounded-lg blur-xl opacity-0 group-hover:opacity-50 transition duration-500"></div>
            </div>

            {/* Floating elements for depth */}
            <div className="absolute -left-20 top-1/4 hidden lg:block">
              <div className="w-40 h-40 bg-gradient-to-br from-aha-red/20 to-transparent rounded-full blur-xl animate-pulse-slow"></div>
            </div>

            <div className="absolute -right-10 bottom-1/4 hidden lg:block">
              <div className="w-32 h-32 bg-gradient-to-bl from-purple-500/20 to-transparent rounded-full blur-xl animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
            </div>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce-slow">
          <div className="flex flex-col items-center">
            <span className="text-white/50 text-sm mb-2">Scroll to explore</span>
            <ChevronDown className="text-white/50 w-6 h-6" />
          </div>
        </div>
      </section>

      {/* Enhanced Trusted Companies Section - Seamless black background */}
      <TrustedCompanies />

      {/* Features Section - Seamless black background */}
      <section className="py-24 bg-black relative">
        {/* Background elements - Hero-style lighting */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Subtle ambient glow similar to hero */}
          <div
            className="absolute top-0 right-0 w-1/2 h-1/2"
            style={{
              background: `radial-gradient(ellipse 400px 300px at 75% 25%,
                rgba(234, 56, 76, 0.08) 0%,
                rgba(234, 56, 76, 0.04) 30%,
                rgba(234, 56, 76, 0.02) 50%,
                transparent 70%)`
            }}
          />
          <div
            className="absolute bottom-0 left-0 w-1/2 h-1/2"
            style={{
              background: `radial-gradient(ellipse 400px 300px at 25% 75%,
                rgba(255, 255, 255, 0.04) 0%,
                rgba(255, 255, 255, 0.02) 30%,
                transparent 50%)`
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4 mx-auto block w-fit animate-fade-in-up">
            Our Features
          </Badge>

          <h2 className="text-3xl md:text-5xl font-bold text-center mb-6 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            Empower Your Business with <span className="text-aha-red">Top-Tier</span> Automation
          </h2>

          <p className="text-center text-gray-300 mb-16 max-w-3xl mx-auto animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
            Discover how AHA-Innovations can streamline your workflows, enhance efficiency, and help your
            business thrive with powerful automation solutions tailored to your needs.
          </p>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-16">
            {[
              {
                icon: <Settings size={24} />,
                title: "Drag-and-drop Builder",
                description: "Create websites, funnels, and forms with our intuitive drag-and-drop interface. No coding required."
              },
              {
                icon: <Globe size={24} />,
                title: "Omnichannel Messaging",
                description: "Reach your customers through email, SMS, and social media from a single platform."
              },
              {
                icon: <Calendar size={24} />,
                title: "Appointment Scheduling",
                description: "Let clients book appointments directly into your calendar with automated reminders."
              },
              {
                icon: <Mail size={24} />,
                title: "Email/SMS Automation",
                description: "Create powerful automation workflows that nurture leads and convert customers."
              },
              {
                icon: <Users size={24} />,
                title: "CRM Pipelines",
                description: "Manage your leads and customers with customizable pipelines and stages."
              },
              {
                icon: <Code size={24} />,
                title: "API Integrations",
                description: "Connect with your favorite tools and services through our robust API."
              },
              {
                icon: <BarChart3 size={24} />,
                title: "Analytics Dashboard",
                description: "Track your performance with detailed analytics and reporting tools."
              },
              {
                icon: <MessageSquare size={24} />,
                title: "AI-Powered Tools",
                description: "Leverage artificial intelligence to automate tasks and gain insights."
              }
            ].map((feature, index) => (
              <div
                key={index}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 h-full transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/5 hover:border-aha-red/20 group animate-fade-in-up"
                style={{ animationDelay: `${0.1 * index}s` }}
              >
                <div className="w-12 h-12 rounded-lg bg-aha-red/10 flex items-center justify-center mb-4 text-aha-red transition-transform duration-300 group-hover:scale-110">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold mb-2 text-white group-hover:text-aha-red transition-colors duration-300">{feature.title}</h3>
                <p className="text-gray-300">{feature.description}</p>
              </div>
            ))}
          </div>

          <div className="mt-16 text-center animate-fade-in-up" style={{ animationDelay: '1s' }}>
            <Button
              className="bg-aha-red hover:bg-aha-darkred text-white px-8 py-4 group relative overflow-hidden"
              onClick={() => navigate('/features')}
            >
              <span className="relative z-10 flex items-center gap-2">
                Explore All Features
                <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
              </span>
              <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
            </Button>
          </div>
        </div>
      </section>

      {/* How AHA-Innovations Help You Grow Section - Seamless black continuation */}
      <section className="py-24 bg-black relative">
        {/* Background elements - Hero-style lighting */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Subtle ambient glow similar to hero */}
          <div
            className="absolute top-0 left-0 w-1/2 h-1/2"
            style={{
              background: `radial-gradient(ellipse 400px 300px at 25% 25%,
                rgba(234, 56, 76, 0.06) 0%,
                rgba(234, 56, 76, 0.03) 30%,
                rgba(234, 56, 76, 0.01) 50%,
                transparent 70%)`
            }}
          />
          <div
            className="absolute bottom-0 right-0 w-1/2 h-1/2"
            style={{
              background: `radial-gradient(ellipse 400px 300px at 75% 75%,
                rgba(255, 255, 255, 0.03) 0%,
                rgba(255, 255, 255, 0.015) 30%,
                transparent 50%)`
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4 mx-auto block w-fit animate-fade-in-up">
            How We Help You Grow
          </Badge>

          <h2 className="text-3xl md:text-5xl font-bold text-center mb-6 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            How <span className="text-aha-red">AHA-Innovations</span> Help You Grow
          </h2>

          <p className="text-center text-gray-300 mb-16 max-w-3xl mx-auto animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
            Discover the powerful ways AHA-Innovations transforms your business operations, streamlines workflows,
            and accelerates your growth with cutting-edge automation and integration solutions.
          </p>

          <div className="animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
            <FeaturesSectionWithCardGradient />
          </div>
        </div>
      </section>

      {/* Product Demo Section - Seamless black with enhanced red accent */}
      <section className="py-24 bg-black relative overflow-hidden">
        {/* Background elements - Enhanced hero-style lighting for demo section */}
        <div className="absolute inset-0">

          {/* Enhanced red accent for demo section */}
          <div
            className="absolute top-0 right-0 w-full h-full"
            style={{
              background: `radial-gradient(ellipse 600px 400px at 75% 25%,
                rgba(234, 56, 76, 0.12) 0%,
                rgba(234, 56, 76, 0.06) 30%,
                rgba(234, 56, 76, 0.03) 50%,
                transparent 70%)`
            }}
          />

          {/* Complementary white glow */}
          <div
            className="absolute bottom-0 left-0 w-2/3 h-2/3"
            style={{
              background: `radial-gradient(ellipse 500px 300px at 25% 75%,
                rgba(255, 255, 255, 0.04) 0%,
                rgba(255, 255, 255, 0.02) 30%,
                transparent 50%)`
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4 mx-auto block w-fit animate-fade-in-up">
            Interactive Product Demos
          </Badge>

          <h2 className="text-3xl md:text-5xl font-bold text-center mb-6 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            See AHA-Innovations in Action
          </h2>

          <p className="text-center text-gray-300 mb-16 max-w-3xl mx-auto animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
            Explore our powerful tools and see how they can transform your business operations.
            Each feature is designed to save you time and increase your productivity.
          </p>

          <div className="mt-16 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {demoTabs.slice(0, 2).map((tab, index) => (
                <div
                  key={tab.id}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl overflow-hidden transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/5 hover:border-aha-red/20 group animate-fade-in-up"
                  style={{ animationDelay: `${0.8 + (index * 0.2)}s` }}
                >
                  <div className="relative">
                    <img
                      src={tab.image}
                      alt={tab.label}
                      className="w-full aspect-video object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent flex items-end">
                      <div className="p-6">
                        <h3 className="text-xl font-bold text-white mb-2">{tab.label}</h3>
                      </div>
                    </div>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-300 mb-6">{tab.description}</p>
                    <Button
                      className="bg-aha-red hover:bg-aha-darkred text-white group relative overflow-hidden"
                    >
                      <span className="relative z-10 flex items-center gap-2">
                        View Demo
                        <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                      </span>
                      <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="mt-24">
            <div className="grid md:grid-cols-2 gap-16 items-center">
              <div className="animate-fade-in-up" style={{ animationDelay: '1.2s' }}>
                <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8">
                  <h3 className="text-2xl font-bold mb-6">AI-Powered Automation</h3>
                  <p className="text-gray-300 mb-6">
                    Our AI-powered tools help you automate repetitive tasks, generate content, and gain insights from your data. Let artificial intelligence do the heavy lifting while you focus on growing your business.
                  </p>

                  <ul className="space-y-4 mb-8">
                    {[
                      "Smart content generation for emails and social media",
                      "Automated lead scoring and qualification",
                      "Intelligent customer segmentation",
                      "Predictive analytics for business insights"
                    ].map((item, index) => (
                      <li
                        key={index}
                        className="flex items-start gap-3 animate-fade-in-left"
                        style={{ animationDelay: `${1.4 + (index * 0.1)}s` }}
                      >
                        <div className="mt-1 text-aha-red"><Check size={16} /></div>
                        <span className="text-gray-300">{item}</span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    className="bg-aha-red hover:bg-aha-darkred text-white group relative overflow-hidden"
                  >
                    <span className="relative z-10 flex items-center gap-2">
                      Explore AI Features
                      <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                    </span>
                    <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
                  </Button>
                </div>
              </div>

              <div className="animate-fade-in-up" style={{ animationDelay: '1.4s' }}>
                <div className="relative group">
                  <img
                    src="/lovable-uploads/1824c50d-b631-40b3-a652-8e58088106ce.png"
                    alt="AI Automation"
                    className="w-full rounded-xl shadow-2xl border border-white/10 transition-transform duration-500 hover:scale-[1.02]"
                  />

                  {/* Animated overlay elements */}
                  <div className="absolute inset-0 pointer-events-none">
                    <div className="absolute top-1/4 left-1/4 w-16 h-16 bg-aha-red/20 rounded-full animate-pulse-slow"></div>
                    <div className="absolute bottom-1/3 right-1/4 w-12 h-12 bg-purple-500/20 rounded-full animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
                  </div>

                  {/* Floating stats card */}
                  <div className="absolute -bottom-10 -left-10 bg-white/10 backdrop-blur-md border border-white/20 rounded-lg p-4 shadow-xl animate-fade-in-up" style={{ animationDelay: '1.8s' }}>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-aha-red/20 flex items-center justify-center text-aha-red">
                        <BarChart3 size={20} />
                      </div>
                      <div>
                        <div className="text-sm text-gray-400">Productivity Increase</div>
                        <div className="text-xl font-bold text-white">+147%</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section - Seamless black continuation */}
      <section className="py-24 bg-black relative">
        {/* Background elements - Hero-style lighting */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Subtle ambient glow similar to hero */}
          <div
            className="absolute top-1/4 left-1/4 w-96 h-96"
            style={{
              background: `radial-gradient(ellipse 400px 400px at center,
                rgba(234, 56, 76, 0.05) 0%,
                rgba(234, 56, 76, 0.025) 30%,
                rgba(234, 56, 76, 0.01) 50%,
                transparent 70%)`
            }}
          />
          <div
            className="absolute bottom-1/4 right-1/4 w-96 h-96"
            style={{
              background: `radial-gradient(ellipse 400px 400px at center,
                rgba(255, 255, 255, 0.03) 0%,
                rgba(255, 255, 255, 0.015) 30%,
                transparent 50%)`
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4 mx-auto block w-fit animate-fade-in-up">
            Testimonials
          </Badge>

          <h2 className="text-3xl md:text-5xl font-bold text-center mb-6 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            Real Stories from Satisfied Clients
          </h2>

          <p className="text-center text-gray-300 mb-16 max-w-3xl mx-auto animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
            Curious how AHA-Innovations has made a difference for others? Hear directly from our
            clients about how we've helped them streamline their business, save time, and achieve real
            success.
          </p>

          <div className="grid md:grid-cols-3 gap-8 mt-16">
            {[
              {
                quote: "I can't believe how much time AHA-Innovations has saved me. Instead of juggling my calendar or manually following up with clients, it's all done automatically. The reminders, the organization, it's like magic.",
                name: "Marcus Thompson",
                title: "Fitness Digital Marketing Consultant",
                avatarSrc: "https://randomuser.me/api/portraits/men/32.jpg",
                rating: 5
              },
              {
                quote: "Honestly, I didn't expect automation to have such a huge impact. AHA-Innovations is like having an extra pair of hands around. I can handle customer inquiries, automate my sales follow-ups, and keep my appointments organized all from one place.",
                name: "Olivia Harper",
                title: "Small Business Owner",
                avatarSrc: "https://randomuser.me/api/portraits/women/44.jpg",
                rating: 5
              },
              {
                quote: "Details are my life as an event planner, and AHA-Innovations helps me keep everything straight. It's like having a personal assistant, but without the extra overhead. I can easily track my appointments, send follow-ups, and make sure nothing slips through the cracks.",
                name: "Justine Meyers",
                title: "Content Creator",
                avatarSrc: "https://randomuser.me/api/portraits/women/68.jpg",
                rating: 5
              }
            ].map((testimonial, index) => (
              <div
                key={index}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 h-full transition-all duration-300 hover:shadow-xl hover:shadow-aha-red/5 hover:border-aha-red/20 animate-fade-in-up"
                style={{ animationDelay: `${0.6 + (index * 0.2)}s` }}
              >
                {/* Quote marks */}
                <div className="text-aha-red/20 text-5xl font-gotham mb-4">"</div>

                {/* Rating stars */}
                <div className="flex mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      size={16}
                      className={i < testimonial.rating ? "text-yellow-400 fill-yellow-400" : "text-gray-600"}
                    />
                  ))}
                </div>

                {/* Quote text */}
                <p className="text-gray-300 mb-6 italic">{testimonial.quote}</p>

                {/* Author info */}
                <div className="flex items-center mt-auto">
                  <div className="h-12 w-12 mr-4 border-2 border-aha-red/20 rounded-full overflow-hidden">
                    <img src={testimonial.avatarSrc} alt={testimonial.name} className="h-full w-full object-cover" />
                  </div>
                  <div>
                    <h4 className="font-bold text-white">{testimonial.name}</h4>
                    <p className="text-sm text-gray-400">{testimonial.title}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Stats section */}
          <div className="mt-24">
            <h3 className="text-2xl md:text-3xl font-bold text-center mb-12 animate-fade-in-up" style={{ animationDelay: '1.2s' }}>
              Trusted by Thousands of Businesses
            </h3>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {[
                {
                  value: 10000,
                  suffix: "+",
                  label: "Active Users",
                  icon: <Users size={24} />
                },
                {
                  value: 98,
                  suffix: "%",
                  label: "Customer Satisfaction",
                  icon: <Star size={24} />
                },
                {
                  value: 5,
                  suffix: "M+",
                  label: "Automations Run",
                  icon: <Settings size={24} />
                },
                {
                  value: 147,
                  suffix: "%",
                  label: "Avg. Productivity Increase",
                  icon: <BarChart3 size={24} />
                }
              ].map((stat, index) => (
                <div
                  key={index}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 text-center hover:border-aha-red/20 transition-all duration-300 animate-fade-in-up"
                  style={{ animationDelay: `${1.4 + (index * 0.1)}s` }}
                >
                  <div className="flex justify-center mb-4">
                    <div className="w-12 h-12 rounded-full bg-aha-red/10 flex items-center justify-center text-aha-red">
                      {stat.icon}
                    </div>
                  </div>
                  <div className="text-4xl font-bold mb-2 text-white">
                    {stat.value}{stat.suffix}
                  </div>
                  <div className="text-gray-400">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Usage Scenarios Section - Seamless black with enhanced accent */}
      <section className="py-24 bg-black relative overflow-hidden">
        {/* Background elements - Enhanced hero-style lighting */}
        <div className="absolute inset-0">

          {/* Enhanced red accent for scenarios section */}
          <div
            className="absolute bottom-0 left-0 w-full h-full"
            style={{
              background: `radial-gradient(ellipse 700px 500px at 25% 75%,
                rgba(234, 56, 76, 0.10) 0%,
                rgba(234, 56, 76, 0.05) 30%,
                rgba(234, 56, 76, 0.025) 50%,
                transparent 70%)`
            }}
          />

          {/* Complementary white glow */}
          <div
            className="absolute top-0 right-0 w-2/3 h-2/3"
            style={{
              background: `radial-gradient(ellipse 500px 300px at 75% 25%,
                rgba(255, 255, 255, 0.04) 0%,
                rgba(255, 255, 255, 0.02) 30%,
                transparent 50%)`
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4 mx-auto block w-fit animate-fade-in-up">
            Who Uses AHA-Innovations?
          </Badge>

          <h2 className="text-3xl md:text-5xl font-bold text-center mb-6 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            Perfect for Businesses of All Sizes
          </h2>

          <p className="text-center text-gray-300 mb-16 max-w-3xl mx-auto animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
            Whether you're a solopreneur, small business, or enterprise, AHA-Innovations scales to meet your needs.
            See how different businesses leverage our platform.
          </p>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mt-16">
            {[
              {
                title: "Solopreneurs",
                icon: <Briefcase size={24} />,
                description: "Manage your entire business single-handedly with automated workflows, appointment scheduling, and client management.",
                features: ["Client management", "Automated follow-ups", "Website builder", "Calendar integration"]
              },
              {
                title: "Coaches & Consultants",
                icon: <Users size={24} />,
                description: "Focus on your clients while AHA-Innovations handles booking, billing, and follow-up communications.",
                features: ["Appointment scheduling", "Client portal", "Payment processing", "Course delivery"]
              },
              {
                title: "Local Businesses",
                icon: <Building size={24} />,
                description: "Attract more local customers with automated marketing, reputation management, and customer engagement tools.",
                features: ["Local SEO tools", "Review management", "SMS marketing", "Customer loyalty"]
              },
              {
                title: "Marketing Agencies",
                icon: <Globe size={24} />,
                description: "Manage multiple client campaigns efficiently with our comprehensive marketing and reporting tools.",
                features: ["White-label solutions", "Client reporting", "Campaign automation", "Multi-account management"]
              }
            ].map((scenario, index) => (
              <div
                key={index}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 h-full hover:border-aha-red/20 transition-all duration-300 group animate-fade-in-up"
                style={{ animationDelay: `${0.6 + (index * 0.1)}s` }}
              >
                <div className="w-12 h-12 rounded-lg bg-aha-red/10 flex items-center justify-center mb-4 text-aha-red transition-transform duration-300 group-hover:scale-110">
                  {scenario.icon}
                </div>
                <h3 className="text-xl font-bold mb-3 group-hover:text-aha-red transition-colors duration-300">{scenario.title}</h3>
                <p className="text-gray-300 mb-4">{scenario.description}</p>

                <ul className="space-y-2 mb-6">
                  {scenario.features.map((feature, featureIndex) => (
                    <li
                      key={featureIndex}
                      className="flex items-center gap-2 animate-fade-in-left"
                      style={{ animationDelay: `${0.8 + (index * 0.1) + (featureIndex * 0.05)}s` }}
                    >
                      <Check size={14} className="text-aha-red" />
                      <span className="text-gray-400 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>

                <div className="mt-auto">
                  <Button
                    variant="ghost"
                    className="text-aha-red hover:bg-aha-red/10 p-0 h-auto group"
                  >
                    <span className="flex items-center gap-1">
                      Learn more
                      <ArrowRight className="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </span>
                  </Button>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-16 text-center animate-fade-in-up" style={{ animationDelay: '1.2s' }}>
            <Button
              className="bg-aha-red hover:bg-aha-darkred text-white text-lg px-8 py-6 group relative overflow-hidden"
              size="lg"
              onClick={() => navigate('/pricing')}
            >
              <span className="relative z-10 flex items-center gap-2">
                Try AHA-Innovations Today!
                <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
              </span>
              <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
            </Button>
          </div>
        </div>
      </section>

      {/* FAQ Section - Seamless black continuation */}
      <section className="py-24 bg-gradient-to-b from-black/96 via-black/98 to-black/96 relative">
        {/* Background elements - Hero-style lighting */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Subtle ambient glow similar to hero */}
          <div
            className="absolute top-0 right-0 w-1/2 h-1/2"
            style={{
              background: `radial-gradient(ellipse 400px 300px at 75% 25%,
                rgba(234, 56, 76, 0.04) 0%,
                rgba(234, 56, 76, 0.02) 30%,
                rgba(234, 56, 76, 0.01) 50%,
                transparent 70%)`
            }}
          />
          <div
            className="absolute bottom-0 left-0 w-1/2 h-1/2"
            style={{
              background: `radial-gradient(ellipse 400px 300px at 25% 75%,
                rgba(255, 255, 255, 0.03) 0%,
                rgba(255, 255, 255, 0.015) 30%,
                transparent 50%)`
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4 mx-auto block w-fit animate-fade-in-up">
            FAQ
          </Badge>

          <h2 className="text-3xl md:text-5xl font-bold text-center mb-6 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            Frequently Asked Questions
          </h2>

          <p className="text-center text-gray-300 mb-16 max-w-3xl mx-auto animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
            Have questions about AHA-Innovations? Find answers to the most frequently asked questions about our
            services and how we can help you automate and grow your business.
          </p>

          <div className="mt-16 max-w-3xl mx-auto animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
            <Accordion type="single" collapsible className="space-y-4">
              {faqItems.map((item, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className="border border-white/10 rounded-lg bg-white/5 backdrop-blur-sm px-6 overflow-hidden animate-fade-in-up"
                  style={{ animationDelay: `${0.8 + (index * 0.1)}s` }}
                >
                  <AccordionTrigger className="text-white hover:text-aha-red transition-colors duration-300 py-4 text-left">
                    {item.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-300 pb-4">
                    {item.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>

          <div className="mt-16 text-center animate-fade-in-up" style={{ animationDelay: '1.4s' }}>
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8 max-w-3xl mx-auto">
              <h3 className="text-2xl font-bold mb-4">Still have questions?</h3>
              <p className="text-gray-300 mb-6">
                Our team is here to help you get the most out of AHA-Innovations. Contact us for personalized assistance.
              </p>
              <Button
                className="bg-aha-red hover:bg-aha-darkred text-white group relative overflow-hidden"
                onClick={() => navigate('/contact')}
              >
                <span className="relative z-10 flex items-center gap-2">
                  Contact Support
                  <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                </span>
                <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section - Seamless black with enhanced finale lighting */}
      <section className="py-32 bg-black relative overflow-hidden">
        {/* Background gradient - Enhanced hero-style finale */}
        <div className="absolute inset-0"></div>

        {/* Animated background elements - Enhanced hero-style lighting for finale */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Enhanced red accent for CTA finale */}
          <div
            className="absolute -top-1/4 -right-1/4 w-1/2 h-1/2 animate-rotate-slow"
            style={{
              background: `radial-gradient(ellipse 600px 600px at center,
                rgba(234, 56, 76, 0.15) 0%,
                rgba(234, 56, 76, 0.08) 30%,
                rgba(234, 56, 76, 0.04) 50%,
                transparent 70%)`
            }}
          />
          <div
            className="absolute -bottom-1/4 -left-1/4 w-1/2 h-1/2 animate-rotate-slow-reverse"
            style={{
              background: `radial-gradient(ellipse 600px 600px at center,
                rgba(255, 255, 255, 0.08) 0%,
                rgba(255, 255, 255, 0.04) 30%,
                rgba(255, 255, 255, 0.02) 50%,
                transparent 70%)`
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-8 md:p-12 shadow-2xl animate-fade-in-up">
              <div className="text-center mb-8">
                <Badge className="bg-aha-red/20 hover:bg-aha-red/30 text-aha-red border-none mb-6 py-1.5 px-4 animate-fade-in-down">
                  Limited Time Offer
                </Badge>

                <h2 className="text-3xl md:text-5xl font-bold mb-6">
                  <div className="animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
                    <span className="text-white">Your All-in-One Solution for</span>
                  </div>
                  <div className="animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
                    <span className="text-aha-red">Business Growth</span>
                  </div>
                </h2>

                <p className="text-gray-300 mb-8 text-lg max-w-2xl mx-auto animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
                  Stay on top of your business operations effortlessly—manage everything in one place
                  with AHA-Innovations.
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
                  <Button
                    className="bg-aha-red hover:bg-aha-darkred text-white text-lg px-8 py-6 group relative overflow-hidden"
                    size="lg"
                    onClick={() => navigate('/signup')}
                  >
                    <span className="relative z-10 flex items-center gap-2">
                      Start Free Trial
                      <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                    </span>
                    <span className="absolute inset-0 bg-white/10 z-0 translate-x-[-100%] group-hover:translate-x-0 transition-transform duration-300"></span>
                  </Button>

                  <Button
                    variant="outline"
                    className="border-white text-white hover:bg-white/10 text-lg px-8 py-6"
                    size="lg"
                    onClick={() => navigate('/contact')}
                  >
                    Schedule a Demo
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8 pt-8 border-t border-white/10">
                {[
                  { text: "14-day free trial", icon: <Calendar size={18} /> },
                  { text: "No credit card required", icon: <Check size={18} /> },
                  { text: "Cancel anytime", icon: <Check size={18} /> },
                  { text: "24/7 support", icon: <MessageSquare size={18} /> }
                ].map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-center gap-2 text-gray-300 animate-fade-in-up"
                    style={{ animationDelay: `${1 + (index * 0.1)}s` }}
                  >
                    <span className="text-aha-red">{item.icon}</span>
                    <span className="text-sm">{item.text}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-aha-dark py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-12">
            <div>
              <Logo />
              <p className="text-gray-400 mt-4">
                AHA-Innovations – Empowering your business with
                automation solutions that save time, drive growth,
                and boost productivity.
              </p>
              <div className="flex gap-4 mt-6">
                {/* Social Icons */}
                <div className="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center"></div>
                <div className="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center"></div>
                <div className="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center"></div>
                <div className="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center"></div>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-bold mb-4">Quick Links</h3>
              <ul className="space-y-2">
                <li><Link to="/" className="text-gray-400 hover:text-white">Home</Link></li>
                <li><Link to="/pricing" className="text-gray-400 hover:text-white">Pricing</Link></li>
                <li><Link to="/features" className="text-gray-400 hover:text-white">Features</Link></li>
                <li><Link to="/contact" className="text-gray-400 hover:text-white">Contact</Link></li>
              </ul>
            </div>

            <div>
              <h3 className="text-xl font-bold mb-4">Contact Us</h3>
              <p className="text-gray-400">
                Visit our <Link to="/contact" className="text-aha-red hover:underline">Contact Page</Link> to create a ticket
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
