import React, { useRef, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { useLocation } from 'react-router-dom';
import { Badge } from '@/components/ui/badge';
import HeroHeader from '@/components/sections/HeroHeader';
import ModernFooter from '@/components/sections/ModernFooter';

const Terms = () => {
  const heroRef = useRef(null);
  const location = useLocation();
  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  // Parallax effect for hero section - keep content visible
  const y = useTransform(scrollYProgress, [0, 1], [0, 100]);
  const opacity = useTransform(scrollYProgress, [0, 0.95], [1, 0.98]);

  // Handle anchor navigation
  useEffect(() => {
    if (location.hash) {
      const element = document.getElementById(location.hash.substring(1));
      if (element) {
        setTimeout(() => {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }, 100);
      }
    }
  }, [location.hash]);

  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden" style={{ background: '#0F0F0F' }}>
      <HeroHeader />

      {/* Hero Section */}
      <section ref={heroRef} className="py-16 relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #0F0F0F 0%, rgba(144, 25, 28, 0.8) 100%)' }}>
        {/* Background elements */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
        </div>

        <motion.div
          style={{ y, opacity }}
          className="container mx-auto px-4 pt-20 pb-16 relative z-10"
        >
          <div className="text-center max-w-3xl mx-auto">
            <Badge className="bg-white/10 hover:bg-white/20 text-white border-none mb-6 py-1.5 px-4 mx-auto block w-fit">
              Legal
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Terms of Service
            </h1>
            <p className="text-xl text-gray-300">
              Last updated: {new Date().toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}
            </p>
          </div>
        </motion.div>
      </section>

      {/* Quick Navigation */}
      <section className="py-10 relative">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            <div className="glass-card rounded-xl p-6 border border-white/10 bg-white/5 backdrop-blur-sm">
              <h3 className="text-white font-gotham font-semibold text-lg mb-4">Quick Navigation</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm">
                <a href="#introduction" className="text-white/80 hover:text-aha-red transition-colors font-gotham">1. Introduction</a>
                <a href="#definitions" className="text-white/80 hover:text-aha-red transition-colors font-gotham">2. Definitions</a>
                <a href="#account-registration" className="text-white/80 hover:text-aha-red transition-colors font-gotham">3. Account Registration</a>
                <a href="#user-responsibilities" className="text-white/80 hover:text-aha-red transition-colors font-gotham">4. User Responsibilities</a>
                <a href="#acceptable-use" className="text-white/80 hover:text-aha-red transition-colors font-gotham">5. Acceptable Use</a>
                <a href="#intellectual-property" className="text-white/80 hover:text-aha-red transition-colors font-gotham">6. Intellectual Property</a>
                <a href="#termination" className="text-white/80 hover:text-aha-red transition-colors font-gotham">7. Termination</a>
                <a href="#limitation-liability" className="text-white/80 hover:text-aha-red transition-colors font-gotham">8. Limitation of Liability</a>
                <a href="#usage-fees" className="text-white/80 hover:text-aha-red transition-colors font-gotham font-semibold bg-aha-red/10 px-2 py-1 rounded">9. Usage Fees</a>
                <a href="#changes-terms" className="text-white/80 hover:text-aha-red transition-colors font-gotham">10. Changes to Terms</a>
                <a href="#contact" className="text-white/80 hover:text-aha-red transition-colors font-gotham">11. Contact Information</a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Terms Content */}
      <section className="py-10 relative">
        <div className="container mx-auto px-4">
          <div className="glass-card rounded-xl p-12 border border-white/10 relative overflow-hidden max-w-5xl mx-auto">
            <div className="absolute inset-0 bg-gradient-to-br from-aha-red/5 to-transparent opacity-30"></div>
            <div className="relative z-10 prose prose-invert prose-lg max-w-none">
              <style jsx>{`
                .prose h2 {
                  color: #ffffff;
                  font-family: 'Gotham', sans-serif;
                  font-size: 1.875rem;
                  font-weight: 700;
                  margin-top: 4rem;
                  margin-bottom: 2rem;
                  padding-bottom: 1rem;
                  border-bottom: 3px solid rgba(234, 56, 76, 0.4);
                  position: relative;
                }
                .prose h2::before {
                  content: '';
                  position: absolute;
                  bottom: -3px;
                  left: 0;
                  width: 60px;
                  height: 3px;
                  background: linear-gradient(90deg, #ea384c, #901c1c);
                  border-radius: 2px;
                }
                .prose p {
                  color: rgba(255, 255, 255, 0.92);
                  font-family: 'Gotham', sans-serif;
                  line-height: 1.9;
                  margin-bottom: 2rem;
                  font-size: 1.05rem;
                  font-weight: 400;
                }
                .prose ul {
                  margin: 2rem 0;
                  padding-left: 1.5rem;
                }
                .prose li {
                  color: rgba(255, 255, 255, 0.88);
                  font-family: 'Gotham', sans-serif;
                  margin-bottom: 1rem;
                  line-height: 1.8;
                  font-size: 1rem;
                  position: relative;
                }
                .prose li::marker {
                  color: rgba(234, 56, 76, 0.8);
                }
                .prose strong {
                  color: #ffffff;
                  font-weight: 700;
                }
                .prose #usage-fees {
                  scroll-margin-top: 140px;
                  background: linear-gradient(135deg, rgba(234, 56, 76, 0.15) 0%, rgba(234, 56, 76, 0.08) 100%);
                  padding: 3rem;
                  border-radius: 16px;
                  border: 2px solid rgba(234, 56, 76, 0.3);
                  margin: 3rem 0;
                  position: relative;
                  box-shadow: 0 8px 32px rgba(234, 56, 76, 0.1);
                }
                .prose #usage-fees::before {
                  content: '';
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  height: 4px;
                  background: linear-gradient(90deg, #ea384c, #901c1c);
                  border-radius: 16px 16px 0 0;
                }
              `}</style>
              <h2 id="introduction">1. Introduction</h2>
              <p>
                Welcome to AHA-Innovations. These Terms of Service govern your use of our website and services. By accessing or using AHA-Innovations, you agree to be bound by these Terms.
              </p>

              <h2 id="definitions">2. Definitions</h2>
              <p>
                <strong>"Service"</strong> refers to the AHA-Innovations platform, including all features, functionalities, and user interfaces.
                <br />
                <strong>"User"</strong> refers to individuals who access or use the Service.
                <br />
                <strong>"Content"</strong> refers to all information displayed, transmitted, or otherwise made available via the Service.
              </p>

              <h2 id="account-registration">3. Account Registration</h2>
              <p>
                To access certain features of the Service, you may be required to register for an account. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete.
              </p>

              <h2 id="user-responsibilities">4. User Responsibilities</h2>
              <p>
                You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account. You agree to notify us immediately of any unauthorized use of your account or any other breach of security.
              </p>

              <h2 id="acceptable-use">5. Acceptable Use</h2>
              <p>
                You agree not to use the Service for any illegal or unauthorized purpose. You agree to comply with all laws, rules, and regulations applicable to your use of the Service.
              </p>

              <h2 id="intellectual-property">6. Intellectual Property</h2>
              <p>
                The Service and its original content, features, and functionality are owned by AHA-Innovations and are protected by international copyright, trademark, patent, trade secret, and other intellectual property or proprietary rights laws.
              </p>

              <h2 id="termination">7. Termination</h2>
              <p>
                We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever and without limitation, including but not limited to a breach of the Terms.
              </p>

              <h2 id="limitation-liability">8. Limitation of Liability</h2>
              <p>
                In no event shall AHA-Innovations, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your access to or use of or inability to access or use the Service.
              </p>

              <h2 id="usage-fees">9. Usage-Based Fees and Pay-As-You-Go Services</h2>
              <p>
                In addition to your subscription plan, certain features and services are billed separately based on actual usage. These pay-as-you-go services include:
              </p>
              <ul>
                <li><strong>SMS Services:</strong> Text messaging services are charged per message sent, with rates varying by destination country and carrier.</li>
                <li><strong>Voice Calls:</strong> Phone calls are billed per minute based on destination and call duration.</li>
                <li><strong>AI Credits:</strong> Advanced AI features, including content generation, analysis, and automation, consume AI credits based on complexity and processing requirements.</li>
                <li><strong>Premium Workflow Triggers:</strong> Advanced automation triggers that integrate with third-party services may incur additional fees based on usage volume.</li>
                <li><strong>Custom Email Services:</strong> Enhanced email delivery, advanced templates, and high-volume sending may require additional credits.</li>
                <li><strong>Third-Party Integrations:</strong> Certain integrations with external platforms may incur usage-based charges from those providers.</li>
              </ul>
              <p>
                <strong>Billing and Credits:</strong> Usage-based services operate on a prepaid credit system. Your account will be automatically recharged with $10 USD when your credit balance falls below the minimum threshold. You will receive notifications before automatic recharges occur, and you may adjust your recharge settings in your account dashboard.
              </p>
              <p>
                <strong>Rate Transparency:</strong> Current rates for all usage-based services are available in your account dashboard. Rates may change with 30 days' notice, and you will be notified of any rate changes via email and in-app notifications.
              </p>
              <p>
                <strong>Usage Monitoring:</strong> You can monitor your usage and remaining credits in real-time through your account dashboard. Usage reports are available for download and include detailed breakdowns of all charges.
              </p>

              <h2 id="changes-terms">10. Changes to Terms</h2>
              <p>
                We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days' notice prior to any new terms taking effect.
              </p>

              <h2 id="contact">11. Contact Us</h2>
              <p>
                If you have any questions about these Terms, please contact <NAME_EMAIL>.
              </p>
            </div>
          </div>
        </div>
      </section>

      <ModernFooter />
    </div>
  );
};

export default Terms;
