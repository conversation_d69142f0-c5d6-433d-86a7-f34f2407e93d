import React, { useRef, useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { DotPattern } from '@/components/ui/dot-pattern';
import { TextEffect } from '@/components/ui/text-effect';

import { PlanSelectionSection } from '@/components/ui/plan-selection-section';
import { ArrowRight, Check, Sparkles, User } from 'lucide-react';
import { TIERS, PAYMENT_FREQUENCIES } from '@/components/demo/pricing-section-demo';
import { Tab } from '@/components/ui/pricing-tab';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { supabase } from '@/utils/supabaseClient';
import '@/styles/pricing.css';

const PlanSelection = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const heroRef = useRef(null);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [selectedFrequency, setSelectedFrequency] = useState(PAYMENT_FREQUENCIES[0]);

  // Get user info from signup (email, name, etc.)
  const userInfo = location.state?.userInfo || {};
  const userEmail = userInfo.email || 'there';
  const userName = userInfo.name || userEmail.split('@')[0];

  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  // Parallax effect for hero section
  const y = useTransform(scrollYProgress, [0, 1], [0, 100]);
  const opacity = useTransform(scrollYProgress, [0, 0.95], [1, 0.98]);

  // Auth protection - check if user is logged in
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Auth error:', error);
          navigate('/signup', { replace: true });
          return;
        }

        if (!session) {
          // No session - redirect to signup
          navigate('/signup', { replace: true });
          return;
        }

        setUser(session.user);
      } catch (error) {
        console.error('Error checking auth:', error);
        navigate('/signup', { replace: true });
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [navigate]);

  useEffect(() => {
    document.body.classList.add('pricing-page');
    return () => {
      document.body.classList.remove('pricing-page');
    };
  }, []);

  // Show loading state while checking auth
  if (loading) {
    return (
      <div className="min-h-screen bg-aha-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-aha-red mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    );
  }

  const handlePlanSelection = async (tier) => {
    if (tier.id === 'free') {
      // Redirect to onboarding for free plan
      navigate('/onboarding', {
        state: {
          userInfo,
          selectedPlan: tier
        }
      });
    } else {
      // Redirect to Stripe checkout for paid plans (stays in same tab)
      const stripeLink = tier.stripeLinks?.[selectedFrequency];
      if (stripeLink) {
        window.location.href = stripeLink;
      } else {
        console.error('No Stripe link found for plan:', tier.id, selectedFrequency);
      }
    }
  };



  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden pricing-content" style={{ background: '#0F0F0F' }}>
      {/* Hero Section */}
      <section ref={heroRef} className="py-16 relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #0F0F0F 0%, #1a1a1a 100%)' }}>
        {/* Background elements */}
        <div className="absolute inset-0 z-0">
          <DotPattern
            width={30}
            height={30}
            cx={1}
            cy={1}
            cr={0.8}
            className="fill-white/[0.025] [mask-image:radial-gradient(1000px_circle_at_center,white,transparent)]"
          />
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-6xl mx-auto">
            {/* Welcome Message */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <div className="flex items-center justify-center gap-3 mb-4">
                <div className="bg-aha-red/20 rounded-full p-2">
                  <User className="h-5 w-5 text-aha-red" />
                </div>
                <span className="text-white/90">Welcome, {userName}!</span>
              </div>

              <TextEffect
                per="word"
                as="h1"
                className="text-3xl md:text-5xl font-bold text-white font-gotham mb-4"
                variants={{
                  container: {
                    hidden: { opacity: 0 },
                    visible: {
                      opacity: 1,
                      transition: { staggerChildren: 0.05 },
                    },
                  },
                  item: {
                    hidden: { opacity: 0, x: -20 },
                    visible: { opacity: 1, x: 0 },
                  },
                }}
              >
                Choose Your Plan
              </TextEffect>

              <p className="text-lg text-white/90 max-w-2xl mx-auto mb-2">
                Select the plan that best fits your needs. You can always upgrade or downgrade later.
              </p>

              <div className="flex items-center justify-center gap-2 text-sm text-white/70">
                <div className="w-2 h-2 bg-aha-red rounded-full animate-pulse"></div>
                <span>Complete your signup by selecting a plan below</span>
              </div>
            </motion.div>

            {/* Use the PlanSelectionSection component */}
            <PlanSelectionSection
              title=""
              subtitle=""
              tiers={TIERS}
              frequencies={PAYMENT_FREQUENCIES}
              userInfo={userInfo}
              onPlanSelect={handlePlanSelection}
            />
          </div>
        </div>
      </section>
    </div>
  );
};

export default PlanSelection;
