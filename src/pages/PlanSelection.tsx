import React, { useRef, useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { DotPattern } from '@/components/ui/dot-pattern';
import { TextEffect } from '@/components/ui/text-effect';
import { useToast } from '@/hooks/use-toast';

import { PlanSelectionSection } from '@/components/ui/plan-selection-section';
import { ArrowRight, Check, Sparkles, User, Loader2 } from 'lucide-react';
import { TIERS, PAYMENT_FREQUENCIES } from '@/components/demo/pricing-section-demo';
import { Tab } from '@/components/ui/pricing-tab';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { supabase } from '@/utils/supabaseClient';
import { stripeService, PlanGroup } from '@/services/stripeService';
import { subscriptionService } from '@/services/subscriptionService';
import '@/styles/pricing.css';

const PlanSelection = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const heroRef = useRef(null);
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [selectedFrequency, setSelectedFrequency] = useState(PAYMENT_FREQUENCIES[0]);
  const [planGroups, setPlanGroups] = useState<PlanGroup[]>([]);
  const [creatingSubscription, setCreatingSubscription] = useState(false);

  // Get user info from signup (email, name, etc.)
  const userInfo = location.state?.userInfo || {};
  const userEmail = userInfo.email || 'there';
  const userName = userInfo.name || userEmail.split('@')[0];

  const { scrollYProgress } = useScroll({
    target: heroRef,
    offset: ["start start", "end start"]
  });

  // Parallax effect for hero section
  const y = useTransform(scrollYProgress, [0, 1], [0, 100]);
  const opacity = useTransform(scrollYProgress, [0, 0.95], [1, 0.98]);

  // Auth protection and plan loading
  useEffect(() => {
    const initializePage = async () => {
      try {
        // Check auth
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Auth error:', error);
          navigate('/signup', { replace: true });
          return;
        }

        if (!session) {
          navigate('/signup', { replace: true });
          return;
        }

        setUser(session.user);

        // Load plans from Stripe
        console.log('Loading plans from Stripe...');
        const plans = await stripeService.fetchPlanGroups();
        setPlanGroups(plans);
        console.log('Plans loaded:', plans);

      } catch (error) {
        console.error('Page initialization failed:', error);
        toast({
          title: "Error Loading Plans",
          description: "Failed to load pricing plans. Using fallback data.",
          variant: "destructive",
        });
        // Use fallback plans on error
        setPlanGroups([]);
      } finally {
        setLoading(false);
      }
    };

    initializePage();
  }, [navigate, toast]);

  useEffect(() => {
    document.body.classList.add('pricing-page');
    return () => {
      document.body.classList.remove('pricing-page');
    };
  }, []);

  // Show loading state while checking auth
  if (loading) {
    return (
      <div className="min-h-screen bg-aha-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-aha-red mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    );
  }

  const handlePlanSelection = async (tier) => {
    if (creatingSubscription) return; // Prevent double clicks

    setCreatingSubscription(true);

    try {
      console.log('Creating subscription for plan:', tier);

      // Get the price ID for the selected plan and frequency
      let priceId = '';

      if (tier.id === 'free') {
        // Free plan - use hardcoded price ID
        priceId = 'price_1RbMOCL5UMPPQRhs2NiED6ml';
      } else {
        // For paid plans, we need to find the right price ID from our dynamic plans
        const planGroup = planGroups.find(group => group.name.toLowerCase().includes(tier.id));
        if (planGroup) {
          const plan = planGroup.plans.find(p => p.interval === (selectedFrequency === 'yearly' ? 'year' : 'month'));
          priceId = plan?.id || '';
        }

        // Fallback to static price IDs if dynamic loading failed
        if (!priceId) {
          const fallbackPrices = {
            'basic': selectedFrequency === 'yearly' ? 'price_1RgPvyL5UMPPQRhsztBmRmsl' : 'price_1RgPvyL5UMPPQRhsztBmRmsl',
            'agency': selectedFrequency === 'yearly' ? 'price_1RgQ6zL5UMPPQRhs3qb0X73a' : 'price_1RgQ6zL5UMPPQRhs3qb0X73a',
            'enterprise': selectedFrequency === 'yearly' ? 'price_1Rhhm1L5UMPPQRhsfirgJolC' : 'price_1Rhhm1L5UMPPQRhsfirgJolC'
          };
          priceId = fallbackPrices[tier.id] || '';
        }
      }

      if (!priceId) {
        throw new Error(`No price ID found for plan: ${tier.id}`);
      }

      // Create subscription using unified service
      const result = await subscriptionService.createSubscription({
        firstName: userInfo.firstName || userName,
        lastName: userInfo.lastName || 'User',
        email: userInfo.email || userEmail,
        priceId: priceId,
        companyName: userInfo.companyName || `${userName}'s Business`
      });

      console.log('Subscription created successfully:', result);

      toast({
        title: "Account Created Successfully! 🎉",
        description: result.message,
        variant: "default",
      });

      // Redirect to thank you page with success info
      navigate('/thank-you', {
        state: {
          subscriptionResult: result,
          planName: tier.name,
          userInfo: userInfo
        }
      });

    } catch (error) {
      console.error('Error creating subscription:', error);
      toast({
        title: "Subscription Creation Failed",
        description: error.message || "Please try again or contact support.",
        variant: "destructive",
      });
    } finally {
      setCreatingSubscription(false);
    }
  };



  return (
    <div className="min-h-screen w-full text-white overflow-x-hidden pricing-content" style={{ background: '#0F0F0F' }}>
      {/* Hero Section */}
      <section ref={heroRef} className="py-16 relative overflow-hidden" style={{ background: 'linear-gradient(135deg, #0F0F0F 0%, #1a1a1a 100%)' }}>
        {/* Background elements */}
        <div className="absolute inset-0 z-0">
          <DotPattern
            width={30}
            height={30}
            cx={1}
            cy={1}
            cr={0.8}
            className="fill-white/[0.025] [mask-image:radial-gradient(1000px_circle_at_center,white,transparent)]"
          />
          <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_top_right,rgba(234,56,76,0.15),transparent_50%)]"></div>
          <div className="absolute bottom-0 right-0 w-full h-full bg-[radial-gradient(circle_at_bottom_left,rgba(234,56,76,0.1),transparent_50%)]"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-6xl mx-auto">
            {/* Welcome Message */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <div className="flex items-center justify-center gap-3 mb-4">
                <div className="bg-aha-red/20 rounded-full p-2">
                  <User className="h-5 w-5 text-aha-red" />
                </div>
                <span className="text-white/90">Welcome, {userName}!</span>
              </div>

              <TextEffect
                per="word"
                as="h1"
                className="text-3xl md:text-5xl font-bold text-white font-gotham mb-4"
                variants={{
                  container: {
                    hidden: { opacity: 0 },
                    visible: {
                      opacity: 1,
                      transition: { staggerChildren: 0.05 },
                    },
                  },
                  item: {
                    hidden: { opacity: 0, x: -20 },
                    visible: { opacity: 1, x: 0 },
                  },
                }}
              >
                Choose Your Plan
              </TextEffect>

              <p className="text-lg text-white/90 max-w-2xl mx-auto mb-2">
                Select the plan that best fits your needs. You can always upgrade or downgrade later.
              </p>

              <div className="flex items-center justify-center gap-2 text-sm text-white/70">
                <div className="w-2 h-2 bg-aha-red rounded-full animate-pulse"></div>
                <span>Complete your signup by selecting a plan below</span>
              </div>
            </motion.div>

            {/* Use the PlanSelectionSection component */}
            <PlanSelectionSection
              title=""
              subtitle=""
              tiers={TIERS}
              frequencies={PAYMENT_FREQUENCIES}
              userInfo={userInfo}
              onPlanSelect={handlePlanSelection}
            />
          </div>
        </div>
      </section>
    </div>
  );
};

export default PlanSelection;
