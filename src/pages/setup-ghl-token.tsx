
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { storePrivateIntegrationToken, getPrivateIntegrationToken } from '@/utils/ghlPrivateIntegration';
import { useToast } from '@/hooks/use-toast';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { InfoIcon } from 'lucide-react';

export default function SetupGHLToken() {
  const [token, setToken] = useState('');
  const [hasToken, setHasToken] = useState(false);
  const [loading, setLoading] = useState(false);
  const [checking, setChecking] = useState(true);
  const { toast } = useToast();

  // Check if token already exists
  useEffect(() => {
    const checkToken = async () => {
      try {
        await getPrivateIntegrationToken();
        setHasToken(true);
      } catch (error) {
        setHasToken(false);
      } finally {
        setChecking(false);
      }
    };

    checkToken();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!token) {
      toast({
        title: "Error",
        description: "Please enter the Private Integration token",
        variant: "destructive",
      });
      return;
    }
    
    setLoading(true);
    
    try {
      // Store the token
      await storePrivateIntegrationToken(token);
      
      toast({
        title: "Success!",
        description: "Private Integration token has been stored successfully. You can now use the GHL API.",
      });
      
      // Update state
      setHasToken(true);
      
      // Clear the form
      setToken('');
    } catch (error: any) {
      console.error('Error storing token:', error);
      toast({
        title: "Error",
        description: `Failed to store token: ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  
  if (checking) {
    return (
      <div className="container mx-auto py-10">
        <p>Checking token status...</p>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Set Up GoHighLevel Private Integration</CardTitle>
          <CardDescription>
            Enter your Private Integration token to enable API access.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {hasToken && (
            <Alert className="mb-4">
              <InfoIcon className="h-4 w-4" />
              <AlertTitle>Token Already Set</AlertTitle>
              <AlertDescription>
                A Private Integration token is already configured. You can update it below if needed.
              </AlertDescription>
            </Alert>
          )}
          
          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="token" className="text-sm font-medium">
                  Private Integration Token
                </label>
                <Input
                  id="token"
                  placeholder="Enter your GHL Private Integration token"
                  value={token}
                  onChange={(e) => setToken(e.target.value)}
                  required
                />
              </div>
            </div>
            
            <Button type="submit" className="w-full mt-4" disabled={loading}>
              {loading ? 'Saving...' : hasToken ? 'Update Token' : 'Save Token'}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col items-start">
          <h3 className="text-sm font-medium mb-2">How to get a Private Integration token:</h3>
          <ol className="text-xs text-gray-500 list-decimal pl-4 space-y-1">
            <li>Log in to your GoHighLevel account</li>
            <li>Go to Settings &gt; Private Integrations</li>
            <li>Click "Create new Integration"</li>
            <li>Give it a name and description</li>
            <li>Select the required permissions (locations.readonly, locations.write, users.readonly, users.write)</li>
            <li>Copy the generated token</li>
            <li>Paste it in the field above</li>
          </ol>
        </CardFooter>
      </Card>
    </div>
  );
}
