import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { exchangeAuthCodeForToken } from '@/utils/ghlTokenManager';
import { useToast } from '@/hooks/use-toast';

export default function SetupGHLOAuth() {
  const [authCode, setAuthCode] = useState('');
  const [redirectUri, setRedirectUri] = useState('https://app.aha-innovations.com/ghl-auth-callback');
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!authCode) {
      toast({
        title: "Error",
        description: "Please enter the authorization code",
        variant: "destructive",
      });
      return;
    }
    
    setLoading(true);
    
    try {
      // Exchange the authorization code for tokens
      const result = await exchangeAuthCodeForToken(authCode, redirectUri);
      
      toast({
        title: "Success!",
        description: "OAuth tokens have been stored successfully. You can now use the GHL API.",
      });
      
      // Clear the form
      setAuthCode('');
    } catch (error: any) {
      console.error('Error setting up OAuth:', error);
      toast({
        title: "Error",
        description: `Failed to set up OAuth: ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="container mx-auto py-10">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Set Up GoHighLevel OAuth</CardTitle>
          <CardDescription>
            Enter the authorization code from the GoHighLevel OAuth flow to set up API access.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="authCode" className="text-sm font-medium">
                  Authorization Code
                </label>
                <Input
                  id="authCode"
                  placeholder="Enter the code parameter from the redirect URL"
                  value={authCode}
                  onChange={(e) => setAuthCode(e.target.value)}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <label htmlFor="redirectUri" className="text-sm font-medium">
                  Redirect URI
                </label>
                <Input
                  id="redirectUri"
                  placeholder="The redirect URI used in the OAuth flow"
                  value={redirectUri}
                  onChange={(e) => setRedirectUri(e.target.value)}
                  required
                />
                <p className="text-xs text-gray-500">
                  This should match the redirect URI used when generating the authorization code.
                </p>
              </div>
            </div>
            
            <Button type="submit" className="w-full mt-4" disabled={loading}>
              {loading ? 'Setting Up...' : 'Set Up OAuth'}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col items-start">
          <h3 className="text-sm font-medium mb-2">How to get the authorization code:</h3>
          <ol className="text-xs text-gray-500 list-decimal pl-4 space-y-1">
            <li>Go to the GoHighLevel Marketplace OAuth URL</li>
            <li>Select your Agency account and authorize the app</li>
            <li>After the redirect, copy the "code" parameter from the URL</li>
            <li>Paste it in the field above</li>
          </ol>
        </CardFooter>
      </Card>
    </div>
  );
}
