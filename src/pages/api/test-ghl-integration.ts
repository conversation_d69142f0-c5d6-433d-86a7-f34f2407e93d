
import { Request, Response } from 'express';
import { createGHLSubaccountAndUser } from '@/utils/ghlPrivateIntegration';

export default async function handler(req: Request, res: Response) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      email,
      firstName,
      lastName,
      companyName,
      phone,
      city,
      state,
      country,
    } = req.body;

    // Validate required fields
    if (!email || !firstName || !lastName) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Create GHL account using Private Integration token
    const result = await createGHLSubaccountAndUser({
      email,
      firstName,
      lastName,
      companyName: companyName || `${firstName}'s Agency`,
      phone,
      city,
      state,
      country,
    });

    // Return the result
    return res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    console.error('Error testing GHL integration:', error);
    return res.status(500).json({
      error: 'Failed to create GHL account',
      message: error.message,
    });
  }
}
