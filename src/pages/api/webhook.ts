
import { Request, Response } from 'express';
import { createClient } from '@supabase/supabase-js';
import { createGHLSubaccountAndUser, createGHLContact } from '@/utils/ghlPrivateIntegration';

// Initialize Supabase client with service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://fpratwslcktwpzlbzlhm.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

export default async function handler(req: Request, res: Response) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      email,
      firstName,
      lastName,
      companyName,
      phone,
      city,
      state,
      country,
      goal,
      // Add any other fields from your form
    } = req.body;

    // Validate required fields
    if (!email || !firstName || !lastName) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Create GHL account using Private Integration token
    const ghlResult = await createGHLSubaccountAndUser({
      email,
      firstName,
      lastName,
      companyName: companyName || `${firstName}'s Agency`,
      phone,
      city,
      state,
      country,
    });

    if (!ghlResult?.locationId) {
      throw new Error('Failed to create GHL account');
    }

    // Create contact in GHL to trigger automation pipeline
    try {
      const contactResult = await createGHLContact({
        locationId: ghlResult.locationId,
        email,
        firstName,
        lastName,
        companyName,
        phone,
        city,
        state,
        country,
        tags: ['signup', 'automated-creation', 'website_signups', goal || 'no-goal'].filter(Boolean),
        source: 'website-signup',
      });

      console.log('✅ Successfully created GHL contact to trigger automation:', contactResult);

      // Store contact ID for future reference
      if (contactResult?.contact?.id) {
        // You can store this in your database if needed
        console.log('GHL Contact ID:', contactResult.contact.id);
      }

    } catch (contactError) {
      console.error('❌ Failed to create GHL contact (automation may not trigger):', contactError);
      // Don't throw error here - account creation was successful, contact creation is for automation
    }

    // Create a temporary password for the user
    const tempPassword = Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8);

    // Create Supabase user
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password: tempPassword,
      email_confirm: true, // Auto-confirm the email
      user_metadata: {
        first_name: firstName,
        last_name: lastName,
        full_name: `${firstName} ${lastName}`,
        company_name: companyName,
        phone,
        city,
        state,
        country,
        goal,
      },
    });

    if (authError) {
      console.error('Error creating Supabase user:', authError);
      throw authError;
    }

    // Store GHL info in user profile
    if (authData.user) {
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: authData.user.id,
          ghl_account_id: ghlResult.locationId,
          ghl_location_id: ghlResult.locationId,
          ghl_user_id: ghlResult.userId,
          updated_at: new Date().toISOString(),
        });

      if (profileError) {
        console.error('Error updating profile:', profileError);
        throw profileError;
      }
    }

    // Send password reset email so user can set their own password
    const { error: resetError } = await supabase.auth.admin.generateLink({
      type: 'recovery',
      email,
    });

    if (resetError) {
      console.error('Error sending password reset:', resetError);
      // Don't throw here, as the account was created successfully
    }

    // Return success
    return res.status(200).json({
      success: true,
      message: 'Account created successfully',
    });
  } catch (error: any) {
    console.error('Webhook error:', error);
    return res.status(500).json({
      error: 'Failed to process signup',
      message: error.message,
    });
  }
}
