import { NextApiRequest, NextApiResponse } from 'next';
import { triggerG<PERSON><PERSON>utomation, testGHLContactCreation, validateContactData } from '@/utils/ghlFormSubmission';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Set proper CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  console.log('📝 Received request body:', req.body);

  try {
    const {
      ghlToken,
      email,
      firstName,
      lastName,
      companyName,
      phone,
      city,
      state,
      country,
      postalCode,
      goal,
      locationId,
      testMode = false,
    } = req.body;

    let result;

    // Use server-side token instead of requiring it from client
    const serverToken = process.env.GHL_AGENCY_TOKEN;

    if (!serverToken) {
      return res.status(500).json({ error: 'GHL_AGENCY_TOKEN not found in server environment variables' });
    }

    if (!locationId) {
      return res.status(400).json({ error: 'locationId is required' });
    }

    // Use server token instead of client-provided token
    const tokenToUse = serverToken;

    if (testMode) {
      // Use test data if in test mode
      result = await testGHLContactCreation(locationId, tokenToUse);
    } else {
      // Validate the contact data
      const contactData = {
        email,
        firstName,
        lastName,
        companyName,
        phone,
        city,
        state,
        country,
        postalCode,
        goal,
        locationId,
        customToken: tokenToUse,
      };

      const validationErrors = validateContactData(contactData);
      if (validationErrors.length > 0) {
        return res.status(400).json({
          error: 'Validation failed',
          details: validationErrors
        });
      }

      // Create contact to trigger automation
      result = await triggerGHLAutomation(contactData);
    }

    // Return results
    const statusCode = result.success ? 200 : 207; // 207 = Multi-Status (partial success)

    const response = {
      success: result.success,
      message: result.success
        ? 'GHL contact created successfully - automation should trigger'
        : 'Contact creation failed',
      data: result,
      contactId: result.contactCreation?.contact?.id || null,
      warnings: result.errors.length > 0 ? result.errors : undefined,
      timestamp: new Date().toISOString(),
    };

    console.log('📤 Sending response:', response);
    return res.status(statusCode).json(response);

  } catch (error: any) {
    console.error('❌ Error testing GHL contact creation:', error);

    const errorResponse = {
      success: false,
      error: 'Failed to trigger GHL automation',
      message: error.message,
      timestamp: new Date().toISOString(),
    };

    console.log('📤 Sending error response:', errorResponse);
    return res.status(500).json(errorResponse);
  }
}
