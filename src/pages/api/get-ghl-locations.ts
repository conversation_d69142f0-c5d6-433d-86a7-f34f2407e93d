import { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Set proper CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get token from server-side environment variables only
    const token = process.env.GHL_AGENCY_TOKEN;

    if (!token) {
      return res.status(500).json({
        error: 'GHL_AGENCY_TOKEN not found in server environment variables'
      });
    }

    console.log('🔍 Fetching GHL locations...');
    
    const response = await axios({
      method: 'GET',
      url: 'https://services.leadconnectorhq.com/locations/',
      headers: {
        Authorization: `Bear<PERSON> ${token}`,
        'Content-Type': 'application/json',
        'Version': '2021-07-28',
      },
    });

    console.log('✅ GHL locations response:', response.data);
    
    return res.status(200).json({
      success: true,
      locations: response.data.locations || response.data,
      message: 'Locations fetched successfully',
    });

  } catch (error: any) {
    console.error('❌ Error fetching GHL locations:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });
    
    const errorResponse = {
      success: false,
      error: 'Failed to fetch GHL locations',
      message: error.response?.data?.message || error.message,
      details: error.response?.data,
    };

    return res.status(500).json(errorResponse);
  }
}
