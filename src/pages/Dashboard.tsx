
import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import Logo from '@/components/Logo';
import { useToast } from '@/hooks/use-toast';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import ConnectGHLButton from '@/components/ConnectGHLButton';
import OnboardingWizard from '@/components/OnboardingWizard';
import EnhancedOnboardingWizard from '@/components/EnhancedOnboardingWizard';
import EdgeFunctionTester from '@/components/EdgeFunctionTester';
import { supabase, callEdgeFunction } from '@/utils/supabaseClient';
import { authenticateGHLUser, updateSupabaseProfileWithGHL } from '@/utils/ghlPrivateAuth';
import { getGHLCredentials } from '@/utils/ghlClient';

const Dashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [initializing, setInitializing] = useState(true);
  const [ghlConnected, setGhlConnected] = useState(false);
  const [autoDetectionFailed, setAutoDetectionFailed] = useState(false);
  const [showErrorSheet, setShowErrorSheet] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [creatingAccount, setCreatingAccount] = useState(false);
  const [showOnboardingForm, setShowOnboardingForm] = useState(false);

  // Check for pending social sign-up
  const checkPendingSocialSignUp = async (userId: string, userEmail: string) => {
    try {
      // Check if we have pending social sign-up data in localStorage
      const isPending = localStorage.getItem('aha_signup_pending');
      if (isPending !== 'true') return false;

      // Get the stored data
      const fullName = localStorage.getItem('aha_signup_fullname') || '';
      const companyName = localStorage.getItem('aha_signup_company') || '';
      const provider = localStorage.getItem('aha_signup_provider') || '';
      const timestamp = localStorage.getItem('aha_signup_timestamp') || '';

      // Clear the localStorage data to prevent duplicate processing
      localStorage.removeItem('aha_signup_pending');
      localStorage.removeItem('aha_signup_fullname');
      localStorage.removeItem('aha_signup_company');
      localStorage.removeItem('aha_signup_provider');
      localStorage.removeItem('aha_signup_timestamp');

      // Check if the data is too old (more than 10 minutes)
      const signupTime = new Date(timestamp).getTime();
      const now = new Date().getTime();
      const tenMinutes = 10 * 60 * 1000;
      if (now - signupTime > tenMinutes) {
        console.log('Social sign-up data is too old, ignoring');
        return false;
      }

      console.log('Social sign-up detected:', { fullName, companyName, provider });

      // Instead of automatically creating the account, show the onboarding form
      // with pre-filled data from the social sign-up
      setAutoDetectionFailed(true);
      setShowOnboardingForm(true);

      // Return true to indicate we've handled the social sign-up
      return true;
    } catch (error) {
      console.error('Error processing social sign-up:', error);
      return false;
    }
  };

  // Automatically check user status and GHL connection
  useEffect(() => {
    const checkUser = async () => {
      try {
        console.log('checkUser: Starting initialization...');
        setInitializing(true);
        // Use the imported supabase instance
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('checkUser: Error fetching session:', error);
          throw error;
        }

        console.log('checkUser: Session fetched:', session);

        if (!session) {
          console.log('checkUser: No session found, redirecting to external signin.');
          // Redirect to external sign-in page
          window.location.href = 'https://app.aha-innovations.com';
          return;
        }

        // Set the user from the session
        const userData = session.user;
        console.log('checkUser: User data from session:', userData);

        // Ensure we have the user's email
        if (userData && !userData.email) {
          console.warn('checkUser: User email is missing from session user');
          // Try to get email from user metadata
          if (userData.user_metadata && userData.user_metadata.email) {
            userData.email = userData.user_metadata.email;
            console.log('checkUser: Retrieved email from user_metadata:', userData.email);
          }
        }

        // Set the user state
        setUser(userData);

        // Make sure we have the user's email
        if (!session.user.email) {
          console.warn('checkUser: User email is missing from session user object');
          // Try to get the email from user metadata if available
          if (session.user.user_metadata && session.user.user_metadata.email) {
            session.user.email = session.user.user_metadata.email;
            console.log('checkUser: Retrieved email from user_metadata:', session.user.email);
          }
        }

        setUser(session.user);
        console.log('checkUser: User set:', session.user);
        // Log the user email specifically for debugging
        console.log('checkUser: User email:', session.user?.email);

        // Check if user has GHL connection already
        console.log('checkUser: Checking for existing GHL connection in profile...');

        // Try to get profile data from both Supabase and the edge function
        const [supabaseProfileResult, edgeFunctionResult] = await Promise.allSettled([
          // 1. Get profile from Supabase directly
          supabase
            .from('profiles')
            .select('ghl_account_id, ghl_auth_code, ghl_location_id, role, company_size, referral_source, email, first_name, last_name, company_name') // Include all profile fields
            .eq('id', session.user.id)
            .single(),

          // 2. Get profile from edge function (which has service role access)
          getGHLCredentials(session.user.id)
        ]);

        // Process Supabase profile result
        let data = null;
        let profileError = null;

        if (supabaseProfileResult.status === 'fulfilled') {
          data = supabaseProfileResult.value.data;
          profileError = supabaseProfileResult.value.error;
        }

        // Process edge function result if Supabase direct query failed
        if (!data && edgeFunctionResult.status === 'fulfilled') {
          const edgeData = edgeFunctionResult.value;
          if (edgeData && edgeData.userProfile) {
            data = edgeData.userProfile;
            console.log('checkUser: Retrieved profile data from edge function:', data);
          }
        }

        // Add profile data to the user object if available
        if (data) {
          // Create a profile_data object to store all profile information
          const profile_data = {
            email: data.email || session.user.email,
            first_name: data.first_name || session.user.user_metadata?.first_name,
            last_name: data.last_name || session.user.user_metadata?.last_name,
            company_name: data.company_name || session.user.user_metadata?.company_name,
            role: data.role || session.user.user_metadata?.role,
            company_size: data.company_size || session.user.user_metadata?.company_size,
            referral_source: data.referral_source || session.user.user_metadata?.referral_source
          };

          // If we still don't have an email but it's in the profile, use that
          if (data.email && !session.user.email) {
            console.log('checkUser: Retrieved email from profile:', data.email);
            session.user.email = data.email;
          }

          // Add the profile data to the user object
          session.user.profile_data = profile_data;
          console.log('checkUser: Added profile data to user object:', profile_data);

          // Update the user state with the new data
          setUser({...session.user});
        }

        console.log('checkUser: Profile fetch result:', { data, profileError });

        console.log('checkUser: Profile data:', data);
        console.log('checkUser: Profile error:', profileError);

        if (profileError && profileError.code !== 'PGRST116') {
          // PGRST116 is "no rows returned" which is expected for new users
          console.error('checkUser: Error fetching profile:', profileError);
          // Don't throw here, just show the onboarding form
          setAutoDetectionFailed(true);
          setShowOnboardingForm(true);
        } else if (data?.ghl_auth_code || data?.ghl_location_id) {
          console.log('checkUser: Found GHL auth code or location ID in profile.');
          // Check if the account is fully set up with all required fields
          if (data?.role && data?.company_size && data?.referral_source) {
            console.log('checkUser: Profile is complete, setting ghlConnected to true.');
            setGhlConnected(true);
            toast({
              title: "GHL Connected",
              description: "Your GoHighLevel account is connected and ready to use.",
            });
          } else {
            // Account exists but missing onboarding data - force onboarding
            console.log('checkUser: Account exists but missing onboarding data - showing onboarding form.');
            setAutoDetectionFailed(true);
            setShowOnboardingForm(true);
          }
        } else {
          // New user or no GHL connection found in profile
          console.log('checkUser: New user or no GHL connection found in profile - showing onboarding form.');
          setAutoDetectionFailed(true);
          setShowOnboardingForm(true);

          // We'll still check for pending social sign-up to pre-fill data
          console.log('checkUser: Checking for pending social sign-up...');
          await checkPendingSocialSignUp(session.user.id, session.user.email);
          console.log('checkUser: checkPendingSocialSignUp finished.');
        }
      } catch (error: any) {
        console.error('checkUser: Caught error during initialization:', error);
        setErrorMessage(`Error initializing: ${error.message || 'Unknown error'}`);
        setShowErrorSheet(true);
        // Even if there's an error, we should still show the onboarding form
        setAutoDetectionFailed(true);
        setShowOnboardingForm(true);
      } finally {
        console.log('checkUser: Initialization finished. Setting loading and initializing to false.');
        setLoading(false);
        setInitializing(false);
      }
    };

    checkUser();
  }, [navigate, toast]);

  // Auto-detect if user has GHL account using private integration
  // This is now only used for pre-filling data, not for skipping onboarding
  const autoDetectGHLAccount = async (userId: string, email: string) => {
    if (!email) return false;

    try {
      // Check if user already exists in GHL using private integration
      const ghlAuth = await authenticateGHLUser(email);

      if (ghlAuth.success && ghlAuth.locationId) {
        // Update user profile with existing GHL information
        await updateSupabaseProfileWithGHL(userId, {
          locationId: ghlAuth.locationId,
          userId: ghlAuth.userId
        });

        console.log('Existing GHL account found:', ghlAuth);

        // We don't automatically set ghlConnected anymore
        // We'll still show the onboarding form to collect required information

        toast({
          title: "Account Found",
          description: "We found your existing account. Please complete the onboarding to continue.",
        });

        return true;
      }

      // No existing account found
      console.log('No existing GHL account found for', email);
      return false;
    } catch (error: any) {
      console.error('Error auto-detecting GHL account:', error);
      // Don't show error toast here as it might be confusing for users
      // Just log it for debugging
      return false;
    }
  };

  // Show the onboarding form to collect information before creating a GHL account
  const createGHLAccount = () => {
    if (!user?.email) {
      toast({
        title: "Error",
        description: "User email not found. Please sign out and sign in again.",
        variant: "destructive",
      });
      return;
    }

    // Show the onboarding form
    setShowOnboardingForm(true);
  };

  // Handle successful account creation from the onboarding form
  const handleAccountCreated = (locationId: string) => {
    setGhlConnected(true);
    setShowOnboardingForm(false);
  };

  // Handle cancellation of the onboarding form
  const handleCancelOnboarding = () => {
    setShowOnboardingForm(false);
  };

  const handleSignOut = async () => {
    try {
      // Use the imported supabase instance
      await supabase.auth.signOut();
      // First check if we have an OAuth code in the URL
      const params = new URLSearchParams(window.location.search);
      if (params.has('code')) {
        // If we have a code, we're in a callback - redirect to dashboard
        navigate('/dashboard');
      } else {
        // Otherwise, go to home
        navigate('/');
      }
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-aha-dark flex items-center justify-center">
        <p className="text-white">Loading...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-aha-dark">
      <header className="border-b border-gray-800 bg-aha-darkpurple/60">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Logo />
          <div className="flex items-center gap-4">
            {user && (
              <span className="text-gray-300 text-sm hidden md:block">
                {user.email}
              </span>
            )}
            <Button
              variant="outline"
              onClick={handleSignOut}
              className="border-gray-700 text-gray-300 hover:bg-gray-800"
            >
              Sign Out
            </Button>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        <div className="bg-aha-darkpurple/60 rounded-lg p-8 text-center">
          <h1 className="text-2xl font-bold text-white mb-6">Welcome to Your Dashboard</h1>

          {initializing ? (
            <div className="text-gray-400 mb-6">
              Setting up your account...
            </div>
          ) : (
            <div className="mb-8">
              {ghlConnected ? (
                <div className="text-green-400 mb-4">
                  ✓ Your account is connected and ready to use
                </div>
              ) : (
                <div className="text-yellow-400 mb-4">
                  Please complete the account setup below to continue.
                </div>
              )}

              {/* Enhanced Onboarding Wizard - always show when not connected */}
              {!ghlConnected && (
                <div className="mb-6">
                  <EnhancedOnboardingWizard
                    user={user}
                    onSuccess={handleAccountCreated}
                    onCancel={handleCancelOnboarding}
                  />
                </div>
              )}

              {/* Removed fallback button since onboarding form is always shown when not connected */}
            </div>
          )}

          {/* Main dashboard content - only shown when connected */}
          {ghlConnected && (
            <div className="grid gap-4 max-w-md mx-auto">
              <Button
                className="bg-aha-red hover:bg-aha-darkred text-white"
                onClick={() => window.location.href = '/app/dashboard'}
              >
                Go to Dashboard
              </Button>
            </div>
          )}

          {/* Admin buttons - hidden from regular users */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-8 pt-8 border-t border-gray-800">
              <p className="text-xs text-gray-500 mb-4">Developer Options (not visible in production)</p>
              <div className="grid gap-4 max-w-md mx-auto">
                <Button
                  onClick={() => navigate('/setup-ghl-token')}
                  className="bg-blue-600 hover:bg-blue-700 text-white text-sm"
                  size="sm"
                >
                  Setup GHL Private Integration Token
                </Button>
                <Button
                  onClick={() => navigate('/test-ghl-setup.html')}
                  className="bg-green-600 hover:bg-green-700 text-white text-sm"
                  size="sm"
                >
                  GHL Setup Options
                </Button>
              </div>

              {/* Edge Function Tester */}
              <div className="mt-8">
                <h3 className="text-lg font-medium text-white mb-4">Test Edge Functions</h3>
                <EdgeFunctionTester />
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Error Sheet */}
      <Sheet open={showErrorSheet} onOpenChange={setShowErrorSheet}>
        <SheetContent className="bg-aha-darkpurple text-white">
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-red-400">Error</h3>
            <p>{errorMessage}</p>
            <p className="text-sm text-gray-400">
              Please try refreshing the page or contact support if this issue persists.
            </p>
            <Button
              onClick={() => setShowErrorSheet(false)}
              className="mt-4 bg-aha-red hover:bg-aha-darkred"
            >
              Close
            </Button>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default Dashboard;
