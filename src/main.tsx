import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import mockGHLCredentials from './utils/mockGHLCredentials'
import { initPerformancePolyfill } from './utils/performancePolyfill'
import { initErrorHandler } from './utils/errorHandler'

// Initialize performance polyfill to prevent conflicts between animation libraries
// This needs to happen before any animation libraries are initialized
try {
  initPerformancePolyfill();
  console.log('Performance polyfill initialized');
} catch (error) {
  console.error('Failed to initialize performance polyfill:', error);
}

// Initialize error handler to catch common JavaScript errors
try {
  initErrorHandler();
  console.log('Error handler initialized');
} catch (error) {
  console.error('Failed to initialize error handler:', error);
}

// Initialize mock GHL credentials for local development
// TEMPORARILY DISABLED FOR TESTING WITH REAL GHL CREDENTIALS
if (false && window.location.hostname === 'localhost') {
  try {
    mockGHLCredentials();
    console.log('Mock GHL credentials initialized for local development');
  } catch (error) {
    console.error('Failed to initialize mock GHL credentials:', error);
  }
}

console.log('Mock GHL credentials DISABLED - using real Supabase Edge Functions');

// Create and render the app
const rootElement = document.getElementById("root");
if (rootElement) {
  createRoot(rootElement).render(<App />);
} else {
  console.error('Root element not found');
}
