// Stripe service for fetching plan details dynamically
export interface StripePlan {
  id: string;
  productId: string;
  name: string;
  description: string;
  amount: number;
  currency: string;
  interval: 'month' | 'year';
  trialPeriodDays?: number;
  features: string[];
  popular?: boolean;
  highlighted?: boolean;
  limitedOffer?: string;
}

export interface PlanGroup {
  productId: string;
  name: string;
  description: string;
  plans: StripePlan[];
  features: string[];
  popular?: boolean;
  highlighted?: boolean;
  limitedOffer?: string;
}

// Product configurations with features and metadata
const PRODUCT_CONFIG = {
  'prod_SWPC9unYpg0Hc2': {
    name: 'Free Plan',
    description: 'Perfect for getting started',
    features: [
      '20 Contacts',
      '5 Users/Teammates', 
      'Basic CRM',
      'Email Support',
      'Basic Analytics'
    ]
  },
  'prod_RA7Wr64BcGkiFa': {
    name: 'Basic Plan',
    description: 'Perfect for freelancers',
    features: [
      '50 Contacts',
      '2 Users/Teammates',
      'Unlimited Sales Funnel',
      'Email Marketing',
      'Basic Automation'
    ]
  },
  'prod_R9llZ9zoxgWOgE': {
    name: 'Agency Plan', 
    description: 'Ideal for small agencies',
    features: [
      '5,000 Contacts',
      '50 Users/Teammates',
      'Advanced Workflows',
      'Email Marketing',
      'Advanced Analytics'
    ],
    popular: true
  },
  'prod_ScxhXd1F2zBsHw': {
    name: 'Enterprise Plan',
    description: 'Recommended for business',
    features: [
      'Unlimited Contacts',
      'Unlimited Users',
      'All Premium Features',
      'Priority Support',
      'Custom Integrations'
    ],
    highlighted: true,
    limitedOffer: 'Limited offer, get Enterprise for the price of Basic!'
  }
};

export class StripeService {
  private publishableKey: string;

  constructor() {
    this.publishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '';
    if (!this.publishableKey) {
      console.warn('Stripe publishable key not found in environment variables');
    }
  }

  async fetchPlanGroups(): Promise<PlanGroup[]> {
    if (!this.publishableKey) {
      console.warn('Using fallback static plans - Stripe key not configured');
      return this.getFallbackPlans();
    }

    try {
      const productIds = Object.keys(PRODUCT_CONFIG);
      const planGroups: PlanGroup[] = [];

      for (const productId of productIds) {
        const config = PRODUCT_CONFIG[productId];
        
        // Fetch prices for this product
        const prices = await this.fetchPricesForProduct(productId);
        
        if (prices.length > 0) {
          const plans: StripePlan[] = prices.map(price => ({
            id: price.id,
            productId: productId,
            name: config.name,
            description: config.description,
            amount: price.unit_amount || 0,
            currency: price.currency,
            interval: price.recurring?.interval || 'month',
            trialPeriodDays: price.recurring?.trial_period_days,
            features: config.features
          }));

          planGroups.push({
            productId,
            name: config.name,
            description: config.description,
            plans,
            features: config.features,
            popular: config.popular,
            highlighted: config.highlighted,
            limitedOffer: config.limitedOffer
          });
        }
      }

      return planGroups;
    } catch (error) {
      console.error('Error fetching plans from Stripe:', error);
      return this.getFallbackPlans();
    }
  }

  private async fetchPricesForProduct(productId: string) {
    try {
      // Use Stripe's REST API directly (no SDK needed for simple fetches)
      const response = await fetch(`https://api.stripe.com/v1/prices?product=${productId}&active=true`, {
        headers: {
          'Authorization': `Bearer ${this.publishableKey}`,
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch prices: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data || [];
    } catch (error) {
      console.error(`Error fetching prices for product ${productId}:`, error);
      return [];
    }
  }

  private getFallbackPlans(): PlanGroup[] {
    // Fallback static plans if Stripe fetch fails
    return [
      {
        productId: 'prod_SWPC9unYpg0Hc2',
        name: 'Free Plan',
        description: 'Perfect for getting started',
        plans: [{
          id: 'price_1RbMOCL5UMPPQRhs2NiED6ml',
          productId: 'prod_SWPC9unYpg0Hc2',
          name: 'Free Plan',
          description: 'Perfect for getting started',
          amount: 0,
          currency: 'usd',
          interval: 'year',
          features: ['20 Contacts', '5 Users', 'Basic CRM']
        }],
        features: ['20 Contacts', '5 Users', 'Basic CRM']
      },
      {
        productId: 'prod_RA7Wr64BcGkiFa',
        name: 'Basic Plan',
        description: 'Perfect for freelancers',
        plans: [{
          id: 'price_1RgPvyL5UMPPQRhsztBmRmsl',
          productId: 'prod_RA7Wr64BcGkiFa',
          name: 'Basic Plan',
          description: 'Perfect for freelancers',
          amount: 500,
          currency: 'usd',
          interval: 'month',
          trialPeriodDays: 30,
          features: ['50 Contacts', '2 Users', 'Email Marketing']
        }],
        features: ['50 Contacts', '2 Users', 'Email Marketing']
      },
      {
        productId: 'prod_R9llZ9zoxgWOgE',
        name: 'Agency Plan',
        description: 'Ideal for small agencies',
        plans: [{
          id: 'price_1RgQ6zL5UMPPQRhs3qb0X73a',
          productId: 'prod_R9llZ9zoxgWOgE',
          name: 'Agency Plan',
          description: 'Ideal for small agencies',
          amount: 2500,
          currency: 'usd',
          interval: 'month',
          trialPeriodDays: 30,
          features: ['5,000 Contacts', '50 Users', 'Advanced Workflows']
        }],
        features: ['5,000 Contacts', '50 Users', 'Advanced Workflows'],
        popular: true
      },
      {
        productId: 'prod_ScxhXd1F2zBsHw',
        name: 'Enterprise Plan',
        description: 'Recommended for business',
        plans: [{
          id: 'price_1Rhhm1L5UMPPQRhsfirgJolC',
          productId: 'prod_ScxhXd1F2zBsHw',
          name: 'Enterprise Plan',
          description: 'Recommended for business',
          amount: 500,
          currency: 'usd',
          interval: 'month',
          trialPeriodDays: 30,
          features: ['Unlimited Contacts', 'Unlimited Users', 'Priority Support']
        }],
        features: ['Unlimited Contacts', 'Unlimited Users', 'Priority Support'],
        highlighted: true,
        limitedOffer: 'Limited offer, get Enterprise for the price of Basic!'
      }
    ];
  }

  formatPrice(amount: number, currency: string): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount / 100);
  }
}

export const stripeService = new StripeService();
