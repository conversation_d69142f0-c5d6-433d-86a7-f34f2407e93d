// Stripe service for fetching plan details dynamically
export interface StripePlan {
  id: string;
  productId: string;
  name: string;
  description: string;
  amount: number;
  currency: string;
  interval: 'month' | 'year';
  trialPeriodDays?: number;
  features: string[];
  popular?: boolean;
  highlighted?: boolean;
  limitedOffer?: string;
}

export interface PlanGroup {
  productId: string;
  name: string;
  description: string;
  plans: StripePlan[];
  features: string[];
  popular?: boolean;
  highlighted?: boolean;
  limitedOffer?: string;
}

// Product configurations with features and metadata
const PRODUCT_CONFIG = {
  'prod_SWPC9unYpg0Hc2': {
    name: 'Free Plan',
    description: 'Perfect for getting started',
    features: [
      '20 Contacts',
      '5 Users/Teammates', 
      'Basic CRM',
      'Email Support',
      'Basic Analytics'
    ]
  },
  'prod_RA7Wr64BcGkiFa': {
    name: 'Basic Plan',
    description: 'Perfect for freelancers',
    features: [
      '50 Contacts',
      '2 Users/Teammates',
      'Unlimited Sales Funnel',
      'Email Marketing',
      'Basic Automation'
    ]
  },
  'prod_R9llZ9zoxgWOgE': {
    name: 'Agency Plan', 
    description: 'Ideal for small agencies',
    features: [
      '5,000 Contacts',
      '50 Users/Teammates',
      'Advanced Workflows',
      'Email Marketing',
      'Advanced Analytics'
    ],
    popular: true
  },
  'prod_ScxhXd1F2zBsHw': {
    name: 'Enterprise Plan',
    description: 'Recommended for business',
    features: [
      'Unlimited Contacts',
      'Unlimited Users',
      'All Premium Features',
      'Priority Support',
      'Custom Integrations'
    ],
    highlighted: true,
    limitedOffer: 'Limited offer, get Enterprise for the price of Basic!'
  }
};

export class StripeService {
  private publishableKey: string;

  constructor() {
    this.publishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '';
    if (!this.publishableKey) {
      console.warn('Stripe publishable key not found in environment variables');
    }
  }

  async fetchPlanGroups(): Promise<PlanGroup[]> {
    // Note: We can't fetch prices directly from frontend due to CORS and auth restrictions
    // Using fallback static plans with known price IDs
    console.log('Using static plan configuration with known price IDs');
    return this.getFallbackPlans();
  }

  private async fetchPricesForProduct(productId: string) {
    try {
      // Use Stripe's REST API directly (no SDK needed for simple fetches)
      const response = await fetch(`https://api.stripe.com/v1/prices?product=${productId}&active=true`, {
        headers: {
          'Authorization': `Bearer ${this.publishableKey}`,
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch prices: ${response.statusText}`);
      }

      const data = await response.json();
      return data.data || [];
    } catch (error) {
      console.error(`Error fetching prices for product ${productId}:`, error);
      return [];
    }
  }

  private getFallbackPlans(): PlanGroup[] {
    // Fallback static plans if Stripe fetch fails
    return [
      {
        productId: 'prod_SWPC9unYpg0Hc2',
        name: 'Free',
        description: 'Perfect for getting started',
        plans: [{
          id: 'price_1RbMOCL5UMPPQRhs2NiED6ml',
          productId: 'prod_SWPC9unYpg0Hc2',
          name: 'Free',
          description: 'Perfect for getting started',
          amount: 0,
          currency: 'usd',
          interval: 'year',
          features: ['20 Contacts', '10 Users/Teammates', 'Social Planner', 'Email Support', 'Basic Analytics']
        }],
        features: ['20 Contacts', '10 Users/Teammates', 'Social Planner', 'Email Support', 'Basic Analytics']
      },
      {
        productId: 'prod_RA7Wr64BcGkiFa',
        name: 'Basic Plan',
        description: 'Perfect for freelancers',
        plans: [{
          id: 'price_1RbMOCL5UMPPQRhs1AFQl4GH', // Known price ID from memories
          productId: 'prod_RA7Wr64BcGkiFa',
          name: 'Basic Plan',
          description: 'Perfect for freelancers',
          amount: 458, // $4.58/month as shown in UI
          currency: 'usd',
          interval: 'month',
          trialPeriodDays: 30,
          features: ['25 Pipelines', 'Unlimited Sales Funnel', 'Unlimited Contacts', 'Basic Automation']
        }],
        features: ['25 Pipelines', 'Unlimited Sales Funnel', 'Unlimited Contacts', 'Basic Automation']
      },
      {
        productId: 'prod_R9llZ9zoxgWOgE',
        name: 'Agency Plan',
        description: 'Ideal for small agencies',
        plans: [{
          id: 'price_1RgQ6zL5UMPPQRhs3qb0X73a',
          productId: 'prod_R9llZ9zoxgWOgE',
          name: 'Agency Plan',
          description: 'Ideal for small agencies',
          amount: 2375, // $23.75/month as shown in UI
          currency: 'usd',
          interval: 'month',
          trialPeriodDays: 30,
          features: ['50 Pipelines', 'Unlimited Sales Funnel', '7,500 Email Marketing', 'Workflow Triggers & Actions', 'Advanced Analytics']
        }],
        features: ['50 Pipelines', 'Unlimited Sales Funnel', '7,500 Email Marketing', 'Workflow Triggers & Actions', 'Advanced Analytics'],
        popular: true
      },
      {
        productId: 'prod_ScxhXd1F2zBsHw',
        name: 'Enterprise Plan',
        description: 'Recommended for business',
        plans: [{
          id: 'price_1Rhhm1L5UMPPQRhsfirgJolC',
          productId: 'prod_ScxhXd1F2zBsHw',
          name: 'Enterprise Plan',
          description: 'Recommended for business',
          amount: 458, // $4.58/month as shown in UI (limited offer)
          currency: 'usd',
          interval: 'month',
          trialPeriodDays: 30,
          features: ['Unlimited Pipelines', 'Unlimited Sales Funnel', '15,000 Email Marketing', 'Reputation Management', 'Priority Support']
        }],
        features: ['Unlimited Pipelines', 'Unlimited Sales Funnel', '15,000 Email Marketing', 'Reputation Management', 'Priority Support'],
        highlighted: true,
        limitedOffer: 'Limited offer, get Enterprise for the price of Basic!'
      }
    ];
  }

  formatPrice(amount: number, currency: string): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount / 100);
  }
}

export const stripeService = new StripeService();
