// GHL SSO Service - Handles all GHL SSO user management operations
// This service integrates with the ghl-sso-users Edge Function

import { callEdgeFunction, supabase } from '@/utils/supabaseClient';

// Types for GHL SSO operations
export interface GHLUser {
  id: string;
  name: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  externalUserId?: string;
  roles?: {
    type: string;
    role: string;
    locationIds: string[];
  };
  permissions?: any;
}

export interface CreateGHLUserData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  locationIds: string[];
  externalUserId: string; // Maps to our Supabase user ID
  password?: string;
  type?: 'account' | 'agency';
  role?: 'admin' | 'user';
}

export interface SearchGHLUserData {
  email?: string;
  externalUserId?: string;
  query?: string;
  locationId?: string;
  limit?: number;
}

export class GHLSSOService {
  
  /**
   * Create a new GHL user for SSO integration
   */
  static async createUser(userData: CreateGHLUserData): Promise<GHLUser> {
    try {
      console.log('Creating GHL SSO user:', userData);

      const response = await callEdgeFunction('ghl-sso-users', {
        ...userData,
        action: 'create'
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to create GHL user');
      }

      return response.user;
    } catch (error) {
      console.error('Error creating GHL SSO user:', error);
      throw error;
    }
  }

  /**
   * Search for existing GHL users
   */
  static async searchUsers(searchData: SearchGHLUserData): Promise<GHLUser[]> {
    try {
      console.log('Searching GHL SSO users:', searchData);

      const response = await callEdgeFunction('ghl-sso-users', {
        ...searchData,
        action: 'search'
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to search GHL users');
      }

      return response.users || [];
    } catch (error) {
      console.error('Error searching GHL SSO users:', error);
      throw error;
    }
  }

  /**
   * Get a specific GHL user by ID
   */
  static async getUser(userId: string): Promise<GHLUser> {
    try {
      console.log('Getting GHL SSO user:', userId);

      const response = await callEdgeFunction('ghl-sso-users', {
        action: 'get',
        userId: userId
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to get GHL user');
      }

      return response.user;
    } catch (error) {
      console.error('Error getting GHL SSO user:', error);
      throw error;
    }
  }

  /**
   * Check if a user already exists in GHL by email or externalUserId
   */
  static async checkUserExists(email: string, externalUserId?: string): Promise<{
    exists: boolean;
    user?: GHLUser;
    locationIds?: string[];
  }> {
    try {
      console.log('Checking if GHL user exists:', { email, externalUserId });

      // First search by email
      const usersByEmail = await this.searchUsers({ email, limit: 1 });
      
      if (usersByEmail.length > 0) {
        const user = usersByEmail[0];
        return {
          exists: true,
          user: user,
          locationIds: user.roles?.locationIds || []
        };
      }

      // If not found by email and we have externalUserId, search by that
      if (externalUserId) {
        const usersByExternalId = await this.searchUsers({ externalUserId, limit: 1 });
        
        if (usersByExternalId.length > 0) {
          const user = usersByExternalId[0];
          return {
            exists: true,
            user: user,
            locationIds: user.roles?.locationIds || []
          };
        }
      }

      return { exists: false };
    } catch (error) {
      console.error('Error checking if GHL user exists:', error);
      // Return false on error to allow account creation to proceed
      return { exists: false };
    }
  }

  /**
   * Create a complete GHL account for a new user (Location + User)
   * This combines location creation with user creation for seamless onboarding
   */
  static async createCompleteAccount(userData: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    companyName?: string;
    city?: string;
    state?: string;
    country?: string;
    supabaseUserId: string;
  }): Promise<{
    success: boolean;
    locationId: string;
    userId: string;
    ghlUser: GHLUser;
    dashboardUrl: string;
  }> {
    try {
      console.log('Creating complete GHL account for SSO:', userData);

      // Step 1: Create GHL Location (using existing function)
      const locationData = await callEdgeFunction('create-ghl-full-account', {
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        phone: userData.phone || '',
        companyName: userData.companyName || `${userData.firstName} ${userData.lastName}`,
        city: userData.city || 'New York',
        state: userData.state || 'NY',
        country: userData.country || 'US'
      });

      if (!locationData.success) {
        throw new Error('Failed to create GHL location');
      }

      const locationId = locationData.locationId;
      console.log('GHL location created:', locationId);

      // Step 2: Create GHL User for SSO with externalUserId
      const ghlUser = await this.createUser({
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        phone: userData.phone,
        locationIds: [locationId],
        externalUserId: userData.supabaseUserId, // Critical for SSO mapping
        type: 'account',
        role: 'admin'
      });

      console.log('GHL SSO user created:', ghlUser);

      // Step 3: Update Supabase profile with GHL data
      const { error: updateError } = await supabase
        .from('profiles')
        .upsert({
          id: userData.supabaseUserId,
          ghl_location_id: locationId,
          ghl_user_id: ghlUser.id,
          ghl_external_user_id: userData.supabaseUserId,
          updated_at: new Date().toISOString(),
        });

      if (updateError) {
        console.error('Error updating profile with GHL data:', updateError);
        // Don't throw error here, account creation was successful
      }

      // Step 4: Generate dashboard URL
      const dashboardUrl = `https://app.aha-innovations.com/v2/location/${locationId}`;

      return {
        success: true,
        locationId: locationId,
        userId: ghlUser.id,
        ghlUser: ghlUser,
        dashboardUrl: dashboardUrl
      };

    } catch (error) {
      console.error('Error creating complete GHL account:', error);
      throw error;
    }
  }

  /**
   * Handle user authentication for existing GHL users
   * This will be used once SSO is enabled to redirect users to their dashboard
   */
  static async authenticateExistingUser(email: string, supabaseUserId: string): Promise<{
    success: boolean;
    dashboardUrl?: string;
    locationId?: string;
    userId?: string;
  }> {
    try {
      console.log('Authenticating existing GHL user:', email);

      const existingUser = await this.checkUserExists(email, supabaseUserId);
      
      if (!existingUser.exists || !existingUser.user) {
        return { success: false };
      }

      const user = existingUser.user;
      const locationIds = existingUser.locationIds || [];
      
      // Use the first location ID for dashboard redirect
      const primaryLocationId = locationIds[0];
      
      if (!primaryLocationId) {
        console.warn('No location ID found for existing user');
        return { success: false };
      }

      // Update Supabase profile with GHL data if not already set
      const { error: updateError } = await supabase
        .from('profiles')
        .upsert({
          id: supabaseUserId,
          ghl_location_id: primaryLocationId,
          ghl_user_id: user.id,
          ghl_external_user_id: supabaseUserId,
          updated_at: new Date().toISOString(),
        });

      if (updateError) {
        console.error('Error updating profile for existing user:', updateError);
      }

      const dashboardUrl = `https://app.aha-innovations.com/v2/location/${primaryLocationId}`;

      return {
        success: true,
        dashboardUrl: dashboardUrl,
        locationId: primaryLocationId,
        userId: user.id
      };

    } catch (error) {
      console.error('Error authenticating existing GHL user:', error);
      return { success: false };
    }
  }

  /**
   * Generate SSO login URL for existing users (once SSO is enabled)
   * This will be used when GHL enables SSO on the account
   */
  static generateSSOLoginUrl(locationId: string): string {
    // This will be the SSO-enabled login URL once GHL activates SSO
    return `https://app.aha-innovations.com/v2/location/${locationId}`;
  }
}

export default GHLSSOService;
