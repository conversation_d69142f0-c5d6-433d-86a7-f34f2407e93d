import { supabase } from '@/utils/supabaseClient';

export interface CreateSubscriptionRequest {
  firstName: string;
  lastName: string;
  email: string;
  priceId: string;
  companyName?: string;
}

export interface CreateSubscriptionResponse {
  success: boolean;
  stripeCustomerId: string;
  subscriptionId: string;
  planType: string;
  isFreePlan: boolean;
  contactCreated: boolean;
  message: string;
  nextSteps: string[];
  error?: string;
  details?: string;
}

export class SubscriptionService {
  private supabaseUrl: string;
  private anonKey: string;

  constructor() {
    this.supabaseUrl = import.meta.env.VITE_SUPABASE_URL || '';
    this.anonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || '';
    
    if (!this.supabaseUrl || !this.anonKey) {
      throw new Error('Supabase configuration missing');
    }
  }

  async createSubscription(request: CreateSubscriptionRequest): Promise<CreateSubscriptionResponse> {
    try {
      console.log('Creating subscription via unified edge function:', request);

      // Get current session for auth
      const { data: { session } } = await supabase.auth.getSession();
      
      const response = await fetch(`${this.supabaseUrl}/functions/v1/create-stripe-subscription`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.anonKey}`,
          ...(session?.access_token && { 'X-User-Token': session.access_token })
        },
        body: JSON.stringify(request)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      console.log('Subscription created successfully:', data);
      return data;

    } catch (error) {
      console.error('Error creating subscription:', error);
      throw error;
    }
  }

  async createFreeSubscription(userInfo: { firstName: string; lastName: string; email: string; companyName?: string }): Promise<CreateSubscriptionResponse> {
    return this.createSubscription({
      firstName: userInfo.firstName,
      lastName: userInfo.lastName,
      email: userInfo.email,
      priceId: 'price_1RbMOCL5UMPPQRhs2NiED6ml', // Free Plan Improved (1 year)
      companyName: userInfo.companyName
    });
  }

  async createPaidSubscription(userInfo: { firstName: string; lastName: string; email: string; companyName?: string }, priceId: string): Promise<CreateSubscriptionResponse> {
    return this.createSubscription({
      firstName: userInfo.firstName,
      lastName: userInfo.lastName,
      email: userInfo.email,
      priceId: priceId,
      companyName: userInfo.companyName
    });
  }
}

export const subscriptionService = new SubscriptionService();
