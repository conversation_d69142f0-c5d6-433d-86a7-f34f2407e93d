// Supabase client configuration
// This file provides a centralized Supabase client for the application

import { createClient } from '@supabase/supabase-js';

// Supabase credentials - use environment variables when available
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://fpratwslcktwpzlbzlhm.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZwcmF0d3NsY2t0d3B6bGJ6bGhtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ0MTIwNjAsImV4cCI6MjA1OTk4ODA2MH0.fGZ9h7gKvfIknNS1Ru3sRmTUnROAnoAsdEGGySfA8ms';

// Initialize Supabase client with auth configuration
let supabaseClient: ReturnType<typeof createClient> | null = null;

export const getSupabaseClient = () => {
  if (!supabaseClient) {
    supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce'
      }
    });
  }
  return supabaseClient;
};

export const supabase = getSupabaseClient();

/**
 * Call a Supabase Edge Function securely
 * This is a wrapper around fetch that adds the necessary headers
 */
export const callEdgeFunction = async (
  functionName: string,
  body: any = {}
): Promise<any> => {
  try {
    console.log(`Calling Edge Function: ${functionName}`, body);
    console.log(`URL: ${supabaseUrl}/functions/v1/${functionName}`);

    const response = await fetch(`${supabaseUrl}/functions/v1/${functionName}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Accept': 'application/json',
        'x-client-info': 'supabase-js/2.38.4'
      },
      credentials: 'include',
      body: JSON.stringify(body),
    });

    console.log(`Response status: ${response.status}`);

    if (!response.ok) {
      let errorText;
      try {
        // Try to parse as JSON first
        const errorJson = await response.json();
        errorText = JSON.stringify(errorJson);
      } catch {
        // If not JSON, get as text
        errorText = await response.text();
      }
      throw new Error(`Failed to call ${functionName}: ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error calling edge function ${functionName}:`, error);
    throw error;
  }
};

// Export the URL and key for direct use when needed
export { supabaseUrl, supabaseAnonKey };
