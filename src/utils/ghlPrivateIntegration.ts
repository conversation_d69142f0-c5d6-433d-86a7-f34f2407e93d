
import axios from 'axios';

// Function to store the Private Integration token in localStorage
export const storePrivateIntegrationToken = async (token: string): Promise<void> => {
  localStorage.setItem('ghl_private_integration_token', token);
};

// Function to retrieve the Private Integration token from environment (server-side only)
export const getPrivateIntegrationToken = async (): Promise<string> => {
  // Only try to get from server-side environment variables (NO VITE_ prefix)
  if (typeof process !== 'undefined' && process.env) {
    const envToken = process.env.GHL_AGENCY_TOKEN;
    if (envToken) {
      console.log('✅ Using GHL token from server environment variables');
      return envToken;
    }
  }

  throw new Error('No Private Integration token found in server environment variables. This token should not be exposed to the frontend.');
};

// Function to remove the Private Integration token from localStorage
export const removePrivateIntegrationToken = async (): Promise<void> => {
  localStorage.removeItem('ghl_private_integration_token');
};

// Function to make a request to the GHL API using the Private Integration token
const makeGHLRequest = async (endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE', data?: any, customToken?: string) => {
  const token = customToken || await getPrivateIntegrationToken();
  const url = `https://services.leadconnectorhq.com${endpoint}`;

  try {
    console.log(`🔄 Making GHL API request: ${method} ${url}`);
    console.log('📝 Request data:', data);

    const response = await axios({
      method,
      url,
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Version': '2021-07-28', // Required API version header
      },
      data,
    });

    console.log('✅ GHL API response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error(`❌ GHL API request failed for endpoint ${endpoint}:`, {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });

    // Provide more specific error messages
    const errorMessage = error.response?.data?.message ||
                        error.response?.data?.error ||
                        error.message ||
                        'GHL API request failed';

    throw new Error(errorMessage);
  }
};

// Function to create a subaccount in GoHighLevel using the Private Integration token
export const createGHLSubaccountAndUser = async ({
  email,
  firstName,
  lastName,
  companyName,
  phone,
  city,
  state,
  country,
}: {
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  phone?: string;
  city?: string;
  state?: string;
  country?: string;
}) => {
  // Validate required fields
  if (!email || !firstName || !lastName) {
    throw new Error('Missing required fields for creating a GHL subaccount and user.');
  }

  // Construct the location data
  const locationData = {
    name: companyName || `${firstName}'s Agency`,
    email,
    phone: phone || '',
    address: city || '',
    city: city || '',
    state: state || '',
    country: country || '',
    postalCode: '12345', // Default postal code
    timezone: 'America/Los_Angeles', // Default timezone
  };

  // Create the subaccount (location)
  const locationResponse = await makeGHLRequest('/locations', 'POST', locationData);
  const locationId = locationResponse.id;

  if (!locationId) {
    throw new Error('Failed to create GHL subaccount (location).');
  }

  // Construct the user data
  const userData = {
    firstName,
    lastName,
    email,
    phone: phone || '',
  };

  // Invite the user to the subaccount
  const userResponse = await makeGHLRequest(`/locations/${locationId}/users`, 'POST', userData);

  if (!userResponse.id) {
    throw new Error('Failed to create GHL user.');
  }

  return {
    locationId,
    userId: userResponse.id,
  };
};

// Note: Direct form submission via API is not available in GHL API
// We'll use contact creation instead to trigger automations

// Function to create contact directly in GHL using the official Contacts API
export const createGHLContact = async ({
  locationId,
  email,
  firstName,
  lastName,
  companyName,
  phone,
  city,
  state,
  country,
  postalCode,
  timezone = 'America/Los_Angeles',
  tags = [],
  source = 'signup-automation',
  customToken,
}: {
  locationId: string;
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  phone?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  timezone?: string;
  tags?: string[];
  source?: string;
  customToken?: string;
}) => {
  // Construct contact data according to official API documentation
  const contactData = {
    locationId, // Required field
    email: email || null,
    firstName: firstName || null,
    lastName: lastName || null,
    name: `${firstName} ${lastName}`, // Full name
    phone: phone || null,
    address1: city || null, // Using city as address1
    city: city || null,
    state: state || null,
    country: country || 'US', // Default to US if not provided
    postalCode: postalCode || null,
    companyName: companyName || null,
    timezone,
    source,
    tags: tags.length > 0 ? tags : ['signup', 'automated-creation'],
    // Optional: Add custom fields if needed
    customFields: [],
  };

  try {
    console.log('Creating GHL contact with data:', contactData);

    // Use the correct endpoint from the documentation
    const response = await makeGHLRequest(
      '/contacts/',
      'POST',
      contactData,
      customToken
    );

    console.log('GHL contact created successfully:', response);
    return response;
  } catch (error: any) {
    console.error('Failed to create GHL contact:', error);
    throw new Error(`Contact creation failed: ${error.message}`);
  }
};

export default makeGHLRequest;
