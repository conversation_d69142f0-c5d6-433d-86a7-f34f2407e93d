// GHL Private Integration Authentication Service
// This file handles authentication with GHL using the private integration

import { supabase, callEdgeFunction } from './supabaseClient';
import { getPrivateIntegrationToken } from './ghlPrivateIntegration';

/**
 * Authenticate a user with GHL using private integration
 * This is used during sign-in to verify the user exists in GHL
 */
export const authenticateGHLUser = async (email: string): Promise<{
  success: boolean;
  locationId?: string;
  userId?: string;
  message?: string;
}> => {
  try {
    // Check if user exists in GHL
    const existingUser = await callEdgeFunction('check-ghl-user-exists', { email });

    console.log('authenticateGHLUser - GHL check result:', existingUser);

    if (!existingUser?.exists) {
      return {
        success: false,
        message: 'User does not exist in GHL'
      };
    }

    // Extract the first locationId from the locationIds array
    const locationId = existingUser.locationIds && existingUser.locationIds.length > 0
      ? existingUser.locationIds[0]
      : null;

    console.log('authenticateGHLUser - Extracted locationId:', locationId);

    return {
      success: true,
      locationId: locationId,
      userId: existingUser.userId,
      message: 'User authenticated successfully'
    };
  } catch (error: any) {
    console.error('Error authenticating GHL user:', error);
    return {
      success: false,
      message: error.message || 'Failed to authenticate with GHL'
    };
  }
};

/**
 * Create a new GHL account during sign-up
 * This uses the private integration to create a subaccount and user
 */
export const createGHLAccountOnSignUp = async (userData: {
  email: string;
  firstName?: string;
  lastName?: string;
  companyName?: string;
  password?: string;
  // Additional fields
  city?: string;
  state?: string;
  country?: string;
  phone?: string;
  role?: string;
  company_size?: string;
  referral_source?: string;
}): Promise<{
  success: boolean;
  locationId?: string;
  userId?: string;
  message?: string;
}> => {
  try {
    // First check if user already exists
    const existingUser = await callEdgeFunction('check-ghl-user-exists', { email: userData.email });

    if (existingUser?.exists) {
      return {
        success: true,
        locationId: existingUser.locationId,
        message: 'User already exists in GHL'
      };
    }

    // Ensure firstName and lastName are strings (required by GHL API)
    const firstName = userData.firstName || userData.email.split('@')[0] || 'User';
    const lastName = userData.lastName || 'Account';

    // Create new GHL account using the private integration
    const result = await callEdgeFunction('create-ghl-full-account', {
      email: userData.email,
      firstName,
      lastName,
      companyName: userData.companyName || `${firstName}'s Business`,
      password: userData.password,
      city: userData.city || '',
      state: userData.state || '',
      country: userData.country || '',
      phone: userData.phone || '',
      role: userData.role || '',
      company_size: userData.company_size || '',
      referral_source: userData.referral_source || ''
    });

    if (!result?.locationId) {
      throw new Error('Failed to create GHL account');
    }

    return {
      success: true,
      locationId: result.locationId,
      userId: result.userId,
      message: 'GHL account created successfully'
    };
  } catch (error: any) {
    console.error('Error creating GHL account:', error);
    return {
      success: false,
      message: error.message || 'Failed to create GHL account'
    };
  }
};

/**
 * Update Supabase profile with GHL information
 */
export const updateSupabaseProfileWithGHL = async (
  userId: string,
  ghlData: {
    locationId: string;
    userId?: string;
  }
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('profiles')
      .upsert({
        id: userId,
        ghl_account_id: ghlData.locationId,
        ghl_location_id: ghlData.locationId,
        ghl_user_id: ghlData.userId,
        updated_at: new Date().toISOString(),
      });

    if (error) {
      console.error('Error updating profile:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error updating Supabase profile:', error);
    return false;
  }
};
