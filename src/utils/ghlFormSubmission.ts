import { createGHLContact } from './ghlPrivateIntegration';

// Configuration for your GHL automation
export const GHL_AUTOMATION_CONFIG = {
  defaultTags: ['signup', 'automated-creation', 'website_signups'], // Added website_signups tag for filtering
  defaultSource: 'website-signup',
  defaultTimezone: 'America/Los_Angeles',
};

// Interface for contact creation data
export interface ContactCreationData {
  email: string;
  firstName: string;
  lastName: string;
  companyName?: string;
  phone?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  goal?: string;
  locationId: string;
  customToken?: string;
}

// Main function to trigger GHL automation pipeline via contact creation
export const triggerGHLAutomation = async (data: ContactCreationData) => {
  const results = {
    contactCreation: null as any,
    errors: [] as string[],
    success: false,
  };

  try {
    console.log('🚀 Triggering GHL automation by creating contact...');

    const contactResult = await createGHLContact({
      locationId: data.locationId,
      email: data.email,
      firstName: data.firstName,
      lastName: data.lastName,
      companyName: data.companyName,
      phone: data.phone,
      city: data.city,
      state: data.state,
      country: data.country || 'US',
      postalCode: data.postalCode,
      tags: [
        ...GHL_AUTOMATION_CONFIG.defaultTags,
        data.goal || 'no-goal',
        'api-created'
      ].filter(Boolean),
      source: GHL_AUTOMATION_CONFIG.defaultSource,
      timezone: GHL_AUTOMATION_CONFIG.defaultTimezone,
      customToken: data.customToken,
    });

    results.contactCreation = contactResult;
    results.success = true;
    console.log('✅ GHL contact created successfully - automation should trigger:', contactResult);

    // Log the contact ID for tracking
    if (contactResult?.contact?.id) {
      console.log('📝 GHL Contact ID:', contactResult.contact.id);
    }

  } catch (error: any) {
    const errorMsg = `Contact creation failed: ${error.message}`;
    console.error('❌', errorMsg);
    results.errors.push(errorMsg);
  }

  return results;
};

// Function to test contact creation with sample data
export const testGHLContactCreation = async (locationId: string, customToken?: string) => {
  const testData: ContactCreationData = {
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    companyName: 'Test Company',
    phone: '+1234567890',
    city: 'Test City',
    state: 'CA',
    country: 'US',
    postalCode: '90210',
    goal: 'Testing automation',
    locationId,
    customToken,
  };

  console.log('🧪 Testing GHL contact creation with data:', testData);
  return await triggerGHLAutomation(testData);
};

// Function to validate contact data before submission
export const validateContactData = (data: Partial<ContactCreationData>): string[] => {
  const errors: string[] = [];

  if (!data.locationId) errors.push('locationId is required');
  if (!data.email) errors.push('email is required');
  if (!data.firstName) errors.push('firstName is required');
  if (!data.lastName) errors.push('lastName is required');

  // Email validation
  if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push('Invalid email format');
  }

  return errors;
};
