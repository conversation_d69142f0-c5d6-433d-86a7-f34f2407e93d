/**
 * This file provides a polyfill for window.performance to prevent conflicts
 * between multiple animation libraries trying to use it simultaneously.
 */

// Store the original performance object
const originalPerformance = window.performance;

// Create a proxy-based wrapper that will pass through all methods and properties
export const initPerformancePolyfill = () => {
  // Only apply in browser environment
  if (typeof window !== 'undefined') {
    try {
      // Instead of a limited wrapper, use a Proxy to handle all possible methods and properties
      const performanceProxy = new Proxy(originalPerformance, {
        get: (target, prop) => {
          // Special handling for timing entries that need toJSON
          if (prop === 'getEntriesByType' || prop === 'getEntriesByName') {
            return function(...args: any[]) {
              const result = target[prop]?.apply(target, args) || [];
              return result;
            };
          }

          // For all other properties, return the original
          if (typeof target[prop] === 'function') {
            return function(...args: any[]) {
              return target[prop]?.apply(target, args);
            };
          }

          return target[prop];
        }
      });

      // Replace the performance object with our proxy
      // Use a safer approach that doesn't completely replace the object
      // This preserves all native methods including toJSON on timing entries
      const originalDesc = Object.getOwnPropertyDescriptor(window, 'performance');
      if (originalDesc && originalDesc.configurable) {
        Object.defineProperty(window, 'performance', {
          ...originalDesc,
          value: performanceProxy
        });
      }

      console.log('Performance polyfill initialized successfully');
    } catch (error) {
      console.error('Failed to initialize performance polyfill:', error);
      // If the polyfill fails, the original performance object will still be used
    }
  }
};
