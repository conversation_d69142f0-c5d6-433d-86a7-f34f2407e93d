// Mock GHL credentials for local development
// This file provides mock implementations of the Supabase Edge Functions
// related to GHL integration for local development

import { createClient } from '@supabase/supabase-js';

// Supabase credentials - import from supabaseClient to ensure consistency
import { supabaseUrl, supabaseAnon<PERSON>ey } from './supabaseClient';

// Initialize Supabase client safely
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Original function implementations
const originalFunctions = {
  invoke: supabase.functions.invoke.bind(supabase.functions)
};

// Mock implementation of Supabase Edge Functions for local development
const mockEdgeFunctions = () => {
  // Only apply mocks in local development
  if (window.location.hostname !== 'localhost') {
    return;
  }

  console.log('Using mock GHL credentials for local development');

  // Override the invoke method to intercept specific function calls
  supabase.functions.invoke = async (functionName: string, options: any) => {
    console.log(`Mock invoke: ${functionName}`, options);

    // Mock get-ghl-credentials function
    if (functionName === 'get-ghl-credentials') {
      // You can replace these with your actual GHL Marketplace app credentials
      // for more realistic testing
      return {
        data: {
          clientId: '17231107080-attmciog99ngqerdu91g8fr54202bopt.apps.googleusercontent.com',
          clientSecret: 'mock-client-secret-for-local-development',
          agencyToken: 'mock-agency-token-for-local-development',
          companyId: 'mock-company-id-for-local-development'
        },
        error: null
      };
    }

    // Mock check-ghl-user-exists function
    if (functionName === 'check-ghl-user-exists') {
      const email = JSON.parse(options.body).email;
      console.log('Mock checking if GHL user exists:', email);

      // For testing, return that the user exists for specific test emails
      if (email === '<EMAIL>' || email === '<EMAIL>') {
        return {
          data: {
            exists: true,
            locationId: 'mock-location-123',
            source: 'mock_location_search',
            count: 1
          },
          error: null
        };
      }

      // For testing the form submission, let's accept any email with @test.com
      if (email.endsWith('@test.com')) {
        return {
          data: {
            exists: true,
            locationId: 'mock-location-' + Math.random().toString(36).substring(2, 8),
            source: 'mock_location_search',
            count: 1
          },
          error: null
        };
      }

      // Otherwise, return that the user doesn't exist
      return {
        data: {
          exists: false,
          locationId: null,
          source: 'mock',
          count: 0
        },
        error: null
      };
    }

    // Mock exchange-ghl-auth-code function
    if (functionName === 'exchange-ghl-auth-code') {
      return {
        data: {
          access_token: 'mock-access-token',
          refresh_token: 'mock-refresh-token',
          expires_in: 3600,
          token_type: 'Bearer'
        },
        error: null
      };
    }

    // Mock create-ghl-full-account function
    if (functionName === 'create-ghl-full-account') {
      const userData = JSON.parse(options.body);
      return {
        data: {
          locationId: 'mock-location-id',
          userId: 'mock-user-id',
          role: userData.role || '',
          company_size: userData.company_size || '',
          referral_source: userData.referral_source || '',
          message: 'GHL location and user created successfully (mock)'
        },
        error: null
      };
    }

    // For any other functions, use the original implementation
    return originalFunctions.invoke(functionName, options);
  };
};

export default mockEdgeFunctions;
