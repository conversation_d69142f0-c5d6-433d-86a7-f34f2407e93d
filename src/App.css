
#root {
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Enhanced gradient backgrounds */
.auth-gradient {
  background: linear-gradient(135deg, #1A1F2C 0%, rgba(144, 25, 28, 0.8) 100%);
}

.premium-gradient {
  background: linear-gradient(135deg, #1A1F2C 0%, #2a1a2e 50%, rgba(144, 25, 28, 0.8) 100%);
}

/* Animations */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes shimmer {
  0% { background-position: -1000px 0; }
  100% { background-position: 1000px 0; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* Draggable card styles */
.cursor-grab {
  cursor: grab;
}

.cursor-grabbing {
  cursor: grabbing !important;
  z-index: 50 !important;
}

/* Add a subtle transition when dragging */
.testimonial-card-dragging {
  transition: transform 0.1s ease-out, box-shadow 0.2s ease-out;
  z-index: 50;
}

@keyframes marquee {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

@keyframes marquee-reverse {
  0% { transform: translateX(-50%); }
  100% { transform: translateX(0); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse 4s ease-in-out infinite;
}

.animate-marquee {
  animation: marquee 40s linear infinite;
  width: max-content;
}

.animate-marquee-reverse {
  animation: marquee-reverse 40s linear infinite;
  width: max-content;
}

/* Pause animation on hover */
.hover\:pause-animation:hover {
  animation-play-state: paused;
}

/* Glassmorphism */
.glass-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
}

.glass-card-hover {
  transition: all 0.3s ease;
}

.glass-card-hover:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(234, 56, 76, 0.3);
  transform: translateY(-5px);
  box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.3);
}

/* Text gradient */
.text-gradient {
  background: linear-gradient(90deg, #ffffff, #ea384c);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}

/* Glow effects */
.glow-red {
  box-shadow: 0 0 15px rgba(234, 56, 76, 0.5);
}

.glow-text {
  text-shadow: 0 0 10px rgba(234, 56, 76, 0.5);
}

/* Shimmer effect */
.shimmer {
  background: linear-gradient(90deg,
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,0.1) 50%,
    rgba(255,255,255,0) 100%);
  background-size: 1000px 100%;
  animation: shimmer 3s infinite linear;
}

/* Scroll animations */
.reveal-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
}

.reveal-on-scroll.active {
  opacity: 1;
  transform: translateY(0);
}

.chat-bubble {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background-color: #ea384c;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.chat-bubble svg {
  color: white;
  width: 24px;
  height: 24px;
}

.chat-window {
  position: fixed;
  bottom: 90px;
  right: 20px;
  width: 350px;
  height: 400px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 999;
}

.chat-header {
  background-color: #ea384c;
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-body {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.chat-footer {
  padding: 10px;
  border-top: 1px solid #e0e0e0;
  display: flex;
}

.chat-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  margin-right: 10px;
}

.send-button {
  background-color: #ea384c;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 0 15px;
  cursor: pointer;
}

.agent-message {
  background-color: #f0f0f0;
  padding: 10px 15px;
  border-radius: 18px 18px 18px 0;
  margin-bottom: 10px;
  max-width: 80%;
  align-self: flex-start;
}

.user-message {
  background-color: #ea384c;
  color: white;
  padding: 10px 15px;
  border-radius: 18px 18px 0 18px;
  margin-bottom: 10px;
  max-width: 80%;
  align-self: flex-end;
  margin-left: auto;
}

.agent-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
}

