import fetch from 'node-fetch';

// Handler for API requests
export async function handleRequest(request) {
  const url = new URL(request.url);
  const path = url.pathname;

  // Handle proxy-webhook endpoint
  if (path === '/api/proxy-webhook' && request.method === 'POST') {
    try {
      const data = await request.json();
      
      // Use the test webhook URL for now (can be toggled via environment variable later)
      const useTestWebhook = true; // Set to false to use the production webhook
      
      const webhookUrl = useTestWebhook 
        ? 'https://n8n-1-i8dz.onrender.com/webhook-test/aha-signup'
        : 'https://n8n-1-i8dz.onrender.com/webhook/aha-signup';
      
      console.log('Proxying request to n8n webhook:', webhookUrl, data);
      
      // Forward the request to n8n
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      // Get the response from n8n
      const responseData = await response.json().catch(() => ({}));
      
      // Return the response from n8n
      return new Response(JSON.stringify(responseData), {
        status: response.status,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    } catch (error) {
      console.error('Error proxying request to n8n:', error);
      return new Response(JSON.stringify({ error: 'Failed to proxy request to n8n' }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }
  }

  // Default response for unknown endpoints
  return new Response(JSON.stringify({ error: 'Not Found' }), {
    status: 404,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
