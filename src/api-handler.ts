// API Handler for Vite
// This file provides a simple adapter to handle Next.js API routes in a Vite environment

import type { NextApiRequest, NextApiResponse } from 'next';

// Define a type for the Next.js API handler
type NextApiHandler = (req: NextApiRequest, res: NextApiResponse) => Promise<void> | void;

// Create a function to adapt Next.js API handlers to Vite/Express middleware
export function createApiHandler(handler: NextApiHandler) {
  return async (req: Request) => {
    try {
      // Create a mock NextApiRequest
      const method = req.method;
      const url = new URL(req.url);
      const query = Object.fromEntries(url.searchParams.entries());
      
      let body = null;
      if (method !== 'GET' && method !== 'HEAD') {
        try {
          body = await req.json();
        } catch (e) {
          // If parsing as JSON fails, try to parse as form data
          const formData = await req.formData();
          body = Object.fromEntries(formData.entries());
        }
      }
      
      const nextReq = {
        method,
        url: req.url,
        headers: Object.fromEntries(req.headers.entries()),
        query,
        body,
        cookies: {},
      } as NextApiRequest;
      
      // Create a mock NextApiResponse
      let statusCode = 200;
      let responseHeaders = new Headers({
        'Content-Type': 'application/json',
      });
      let responseBody: any = null;
      
      const nextRes = {
        status: (code: number) => {
          statusCode = code;
          return nextRes;
        },
        setHeader: (name: string, value: string) => {
          responseHeaders.set(name, value);
          return nextRes;
        },
        json: (data: any) => {
          responseBody = data;
          return nextRes;
        },
        send: (data: any) => {
          responseBody = data;
          return nextRes;
        },
        end: () => {
          return nextRes;
        },
      } as unknown as NextApiResponse;
      
      // Call the Next.js API handler
      await handler(nextReq, nextRes);
      
      // Return the response
      return new Response(
        responseBody !== null ? JSON.stringify(responseBody) : '',
        {
          status: statusCode,
          headers: responseHeaders,
        }
      );
    } catch (error) {
      console.error('API handler error:', error);
      return new Response(
        JSON.stringify({ error: 'Internal Server Error', message: (error as Error).message }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
  };
}
