@import url('./styles/glass.css');

/* Gotham Font Family - Complete Typography System */
/* Gotham Font Family - Complete Modern Collection */

/* Gotham Regular */
@font-face {
  font-family: 'Gotham';
  src: url('/fonts/Gotham-font-family/Gotham/Gotham-Black.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham';
  src: url('/fonts/Gotham-font-family/Gotham/Gotham-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham';
  src: url('/fonts/Gotham-font-family/Gotham/Gotham-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham';
  src: url('/fonts/Gotham-font-family/Gotham/Gotham-Book.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham';
  src: url('/fonts/Gotham-font-family/Gotham/Gotham-Ultra.otf') format('opentype');
  font-weight: 950;
  font-style: normal;
  font-display: swap;
}

/* Gotham Condensed - Perfect for modern headers */
@font-face {
  font-family: 'Gotham Condensed';
  src: url('/fonts/Gotham-font-family/Gotham Condensed/GothamCond-Black.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Condensed';
  src: url('/fonts/Gotham-font-family/Gotham Condensed/GothamCond-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Condensed';
  src: url('/fonts/Gotham-font-family/Gotham Condensed/GothamCond-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* Gotham Narrow - Modern space-efficient */
@font-face {
  font-family: 'Gotham Narrow';
  src: url('/fonts/Gotham-font-family/Gotham Narrow/GothamNarrow-Black.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham Narrow';
  src: url('/fonts/Gotham-font-family/Gotham Narrow/GothamNarrow-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

/* Gotham ScreenSmart - Optimized for digital */
@font-face {
  font-family: 'Gotham ScreenSmart';
  src: url('/fonts/Gotham-font-family/Gotham ScreenSmart/GothamSSm-Black.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Gotham ScreenSmart';
  src: url('/fonts/Gotham-font-family/Gotham ScreenSmart/GothamSSm-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 0 70% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 224 20% 13%;
    --foreground: 210 40% 98%;

    --card: 224 20% 13%;
    --card-foreground: 210 40% 98%;

    --popover: 224 20% 13%;
    --popover-foreground: 210 40% 98%;

    --primary: 0 70% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 222 25% 20%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    --sidebar-background: 224 20% 13%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-black text-foreground m-0 p-0 min-h-screen w-full font-gotham;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-gotham;
    font-weight: 700;
    letter-spacing: -0.01em;
  }
}

.auth-gradient {
  background: linear-gradient(135deg, #1A1F2C 0%, rgba(144, 25, 28, 0.8) 100%);
}

/* Hero2 component styles */
.bg-noise {
  background-image:
    radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0);
  background-size: 20px 20px;
}

.bg-grainy {
  background-image:
    radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0);
  background-size: 15px 15px;
}

/* Hero Section 1 component styles */
.aspect-15\/8 {
  aspect-ratio: 15 / 8;
}

.h-10\.5 {
  height: 2.625rem;
}

.inset-shadow-2xs {
  box-shadow: inset 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.blur-xs {
  filter: blur(2px);
}

.contain-strict {
  contain: strict;
}

/* Slow spin animation for background elements */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow linear infinite;
}
