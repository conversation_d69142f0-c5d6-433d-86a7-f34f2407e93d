import fetch from 'node-fetch';

// GHL API constants
const GHL_API_URL = 'https://services.leadconnectorhq.com';
const GHL_TOKEN = 'pit-a329e4c6-962c-4d43-9c41-01eb6933dbea';

// Test data
const testData = {
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  companyName: 'Test Company'
};

async function createGHLLocation() {
  try {
    console.log('Creating GHL location for:', testData.email);

    // Create a location in GHL
    const createResponse = await fetch(`${GHL_API_URL}/api/v1/locations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': GHL_TOKEN,
        'Version': '2021-07-28'
      },
      body: JSON.stringify({
        name: testData.companyName,
        email: testData.email,
        phone: '',
        address: {
          city: '',
          state: '',
          country: '',
        },
        timezone: 'America/New_York',
      }),
    });

    console.log('Response status:', createResponse.status);
    console.log('Response status text:', createResponse.statusText);

    const responseHeaders = {};
    createResponse.headers.forEach((value, name) => {
      responseHeaders[name] = value;
    });
    console.log('Response headers:', JSON.stringify(responseHeaders, null, 2));

    const responseText = await createResponse.text();
    console.log('Response body:', responseText);

    if (!createResponse.ok) {
      console.error('Error creating location');
      return;
    }

    const locationData = JSON.parse(responseText);
    console.log('Location created successfully:', locationData.id);

    // Create a user for this location
    const password = Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8);

    const createUserResponse = await fetch(`${GHL_API_URL}/api/v1/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': GHL_TOKEN,
        'Version': '2021-07-28'
      },
      body: JSON.stringify({
        email: testData.email,
        firstName: testData.firstName,
        lastName: testData.lastName,
        password: password,
        locationId: locationData.id,
        role: 'admin',
        type: 'account'
      }),
    });

    console.log('User response status:', createUserResponse.status);

    const userResponseText = await createUserResponse.text();
    console.log('User response body:', userResponseText);

    if (!createUserResponse.ok) {
      console.error('Error creating user');
      return;
    }

    const userData = JSON.parse(userResponseText);
    console.log('User created successfully:', userData.id);

    console.log('Complete result:', {
      location: locationData,
      user: userData
    });
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the test
createGHLLocation();
