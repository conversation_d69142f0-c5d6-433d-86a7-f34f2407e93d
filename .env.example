# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# GoHighLevel API Configuration
# These are used by Edge Functions and should be set in Supabase
# For local development, you can set them here
VITE_GHL_CLIENT_ID=your-ghl-client-id
VITE_GHL_CLIENT_SECRET=your-ghl-client-secret
VITE_GHL_AGENCY_TOKEN=your-ghl-agency-token
VITE_GHL_COMPANY_ID=your-ghl-company-id

# n8n Webhook URLs (fallback integration)
VITE_N8N_WEBHOOK_URL=https://n8n-1-i8dz.onrender.com/webhook/aha-signup
VITE_N8N_TEST_WEBHOOK_URL=https://n8n-1-i8dz.onrender.com/webhook-test/aha-signup

# Application URLs
VITE_APP_URL=http://localhost:5173
VITE_API_URL=http://localhost:5173/api

# Feature Flags
VITE_ENABLE_SOCIAL_LOGIN=true
VITE_ENABLE_N8N_FALLBACK=true
VITE_ENABLE_EDGE_FUNCTIONS=true

# Other Settings
VITE_DEFAULT_TIMEZONE=America/New_York
